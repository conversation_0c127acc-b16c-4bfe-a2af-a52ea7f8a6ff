<template>
  <div class="receive-information-container">
    <!-- 接报信息管理 - Page -->
    <portal-table
      ref="portalTableRef"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
      @add="handleAdd"
    >
      <template #infoTitle="{ row }">
        <span>{{ row.infoTitle }}</span>
        <span v-if="row.infoStatusText === '待办'" class="pending-icon"></span>
      </template>
    </portal-table>

    <receive-information-form
      :dialog-visible="dialogVisible"
      :dialog-title="dialogTitle"
      :is-edit-mode="isEditMode"
      :is-continue-mode="isContinueMode"
      :editing-row="editingRow"
      :location-options="locationOptions"
      :event-type-options="eventTypeOptions"
      :report-method-options="reportMethodOptions"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    />

    <receive-information-detail
      :dialog-visible="detailDialogVisible"
      :detail-id="currentDetailId"
      :detail-title="currentDetailTitle"
      @close="handleDetailClose"
    />
    <add-dialog
      ref="addDialogRef"
      :dialog-visible="addDialogVisible"
      :detail-title="addDialogTitle"
      :report-method-options="reportMethodOptions"
      :emergency-type-list="emergencyTypeList"
      :user-info-list="userInfoList"
      :dialog-type="addDialogType"
      @close="handleAddDialogClose"
      @submitSuccess="fetchData"
    />
    <binding-camera ref="bindingCameraRef" />

    <one-click-response-dialog
      :visible.sync="oneClickResponseDialogVisible"
      :detail-id="currentResponseId"
      @success="handleResponseSuccess"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import ReceiveInformationForm from "./components/ReceiveInformationForm.vue";
import ReceiveInformationDetail from "./components/ReceiveInformationDetail.vue";
import AddDialog from "@/components/InformationSubmit/AddDialog.vue";
import BindingCamera from "@/components/cameraComponents/bindingCamera.vue";
import OneClickResponseDialog from "./components/OneClickResponseDialog.vue";

import {
  receiveInformationApi,
  systemManagementApi,
  dutyManagementApi,
  systemConfigApi,
} from "@/api";
import {
  transformLocationData,
  buildQueryParams,
  infoStatusMap,
} from "./utils/receiveInfoUtils";
import { auth } from "@/utils";

export default {
  name: "ReceiveInformation",
  components: {
    PortalTable,
    ReceiveInformationForm,
    ReceiveInformationDetail,
    AddDialog,
    BindingCamera,
    OneClickResponseDialog,
  },
  data() {
    return {
      addDialogType: "",
      loading: false,
      tableData: [],
      selectedRows: [], // 存储选中的行
      currentSearchParams: {}, // 存储当前搜索参数
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      dialogVisible: false,
      dialogTitle: "新增接报信息",
      isEditMode: false,
      isContinueMode: false,
      editingRow: null,

      locationOptions: [],
      eventTypeOptions: [],
      reportMethodOptions: [],
      emergencyTypeList: [],
      // 详情查看相关
      detailDialogVisible: false,
      currentDetailId: null,
      currentDetailTitle: "",

      // 一键响应相关
      oneClickResponseDialogVisible: false,
      currentResponseId: null,

      searchItems: [
        {
          type: "input",
          prop: "infoTitle",
          label: "事件标题",
          width: 200,
          placeholder: "请输入事件标题",
        },
        {
          type: "select",
          prop: "infoType",
          label: "事件类型",
          placeholder: "请选择事件类型",
          options: [{ label: "全部", value: "" }],
          width: 200,
        },
        {
          type: "startEndPicker",
          prop: "infoTime",
          label: "事发时间",
          placeholder: "请选择事发时间范围",
          width: 300,
        },
      ],
      columns: [
        {
          prop: "infoTitle",
          label: "事件标题",
          showOverflowTooltip: true,
          width: 200,
          text: true,
          slotName: "infoTitle",
        },
        { prop: "infoTime", label: "事发时间", text: true },
        { prop: "createTime", label: "接报时间", text: true },
        {
          prop: "infoTownshipStreetDisplay",
          label: "事件地点",
          showOverflowTooltip: true,
          text: true,
        },
        { prop: "infoType", label: "事件类型", width: 120, text: true },
        {
          prop: "infoReportingUnit",
          label: "报送单位",
          showOverflowTooltip: true,
          text: true,
        },
        {
          prop: "infoStatusText",
          label: "事件状态",
          text: true,
        },
        {
          action: true,
          label: "操作",
          width: 260,
          operationList: [
            {
              label: "查看",
              permission: "info:view",
              buttonClick: this.handleView,
              isShow: () => {
                return true;
              },
            },

            {
              label: "续报",
              permission: "info:report",
              buttonClick: this.handleContinue,
              isShow: () => {
                return true;
              },
            },
            {
              label: "摄像头",
              permission: "info:camera",
              buttonClick: this.handleBindCamera,
              isShow: () => {
                return true;
              },
            },
            // {
            //   label: "一键响应",
            //   permission: "info:reqonse",
            //   buttonClick: this.handleResponse,
            //   isShow: () => {
            //     return true;
            //   },
            // },
          ],
        },
      ],
      addDialogVisible: false,
      addDialogTitle: "新增接报信息",
      userInfoList: {},
    };
  },
  mounted() {
    this.fetchData();
    this.initDictionaryData();
    this.loadLocationData();
    this.queryEmergencyTypeList();
    this.getUserInfo();
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: this.handleAdd,
    });

    this.$store.commit("generalEvent/registerEventHandler", {
      type: "export_top",
      handler: this.handleExport,
    });
  },
  beforeDestroy() {
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: null,
    });

    this.$store.commit("generalEvent/registerEventHandler", {
      type: "export_top",
      handler: null,
    });
  },
  methods: {
    getUserInfo() {
      this.userInfoList = auth.getUserInfo();
    },
    // 获取事件类型树数据
    queryEmergencyTypeList() {
      try {
        systemConfigApi.queryEmergencyTypeList({}).then((res) => {
          this.emergencyTypeList = res?.data || [];
        });
      } catch (error) {}
    },
    async initDictionaryData() {
      try {
        const eventTypeResponse = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507031421",
        });
        if (eventTypeResponse.code === 0) {
          this.eventTypeOptions = eventTypeResponse.data.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));

          // 更新搜索表单中的事件类型选项
          const searchEventTypeItem = this.searchItems.find(
            (item) => item.prop === "infoType"
          );
          if (searchEventTypeItem) {
            searchEventTypeItem.options = [
              { label: "全部", value: "" },
              ...this.eventTypeOptions,
            ];
          }
        }

        const reportMethodResponse =
          await systemManagementApi.queryItemListById({
            systemDictionaryId: "202507041558",
          });
        if (reportMethodResponse.code === 0) {
          this.reportMethodOptions = reportMethodResponse.data.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        }
      } catch (error) {
        this.$message.error("获取字典数据失败");
      }
    },

    async loadLocationData() {
      try {
        const response = await receiveInformationApi.queryTownsBeijing();

        if (response && response.code === 0 && response.data) {
          const transformedData = transformLocationData(response.data);

          if (transformedData && transformedData.length > 0) {
            this.locationOptions = transformedData;
          } else this.loadFallbackLocationData();
        } else this.loadFallbackLocationData();
      } catch (error) {
        this.loadFallbackLocationData();
      }
    },

    loadFallbackLocationData() {
      this.locationOptions = [];
      this.$message.error("获取区县街道数据失败");
    },

    async fetchData(searchParams = {}) {
      this.loading = true;
      try {
        const params = buildQueryParams(searchParams, this.pagination);
        const response = await receiveInformationApi.queryReportInfoList(
          params
        );

        this.handleQueryResponse(response);
      } catch (error) {
        this.handleQueryError();
      } finally {
        this.loading = false;
      }
    },

    handleQueryResponse(response) {
      const { code, data, message, error } = response;
      if (code !== 0) {
        this.$message.error(message || error);
        return;
      }

      this.tableData = (data.items || []).map((item) => ({
        ...item,
        infoStatusText: infoStatusMap[item.infoStatus] || item.infoStatus || "",
        // 保留原始地点字段，同时创建显示用的组合地点信息
        infoTownshipStreetDisplay:
          item.infoTownshipStreet ||
          item.infoDistrict ||
          item.infoLocationDetail ||
          "",
        infoDistrict: item.infoDistrict,
        infoTownshipStreet: item.infoTownshipStreet,
      }));

      this.pagination.total = data.total || 0;
    },

    handleQueryError() {
      this.$message.error("数据加载失败");
      this.tableData = [];
      this.pagination.total = 0;
    },

    handleView(row) {
      this.currentDetailId = row.id;
      this.currentDetailTitle = row.infoTitle;
      this.detailDialogVisible = true;
    },
    // 旧续报按钮
    // handleContinue(row) {
    //   this.isEditMode = false;
    //   this.isContinueMode = true;
    //   this.editingRow = row;
    //   this.dialogTitle = "续保接报信息";
    //   this.dialogVisible = true;
    // },
    // 新续报按钮
    handleContinue(row) {
      this.addDialogVisible = true;
      this.addDialogTitle = "续报信息";
      this.addDialogType = "edit";
      this.$refs.addDialogRef.queryReportInfo(row.id);
    },

    handleBindCamera(row) {
      this.$nextTick(() => {
        if (this.$refs.bindingCameraRef) {
          this.$refs.bindingCameraRef.init(row);
        }
      });
    },

    handleResponse(row) {
      this.currentResponseId = row.id;
      this.oneClickResponseDialogVisible = true;
    },

    // 一键响应成功回调
    handleResponseSuccess() {
      this.oneClickResponseDialogVisible = false;
      this.fetchData(); // 刷新列表数据
      this.$message.success("一键响应成功");
    },
    handleSearch(searchData) {
      this.currentSearchParams = searchData; // 保存搜索参数
      this.pagination.currentPage = 1;
      this.fetchData(searchData);
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },
    handleSelectionChange(selection) {
      // 存储选中的行
      this.selectedRows = selection;
    },

    async handleExport() {
      try {
        // 添加加载状态
        const loading = this.$loading({
          lock: true,
          text: "正在导出数据...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });

        // 直接从PortalTable组件获取当前搜索参数
        const currentSearchParams = this.$refs.portalTableRef
          ? this.$refs.portalTableRef.searchParams || {}
          : this.currentSearchParams || {};

        // 构建查询参数，排除分页参数
        const searchParams = buildQueryParams(
          currentSearchParams,
          this.pagination
        );

        // 删除分页参数，只传递搜索/筛选条件
        delete searchParams.page;
        delete searchParams.count;

        // 调用导出接口
        const response = await receiveInformationApi.exportList(searchParams);

        // 验证响应数据类型和内容
        if (
          !response.data ||
          !(response.data instanceof Blob) ||
          response.data.size === 0
        ) {
          loading.close();
          this.$message.error(
            "导出数据为空或格式错误，请检查搜索条件或联系管理员"
          );
          return;
        }

        // 直接使用response.data（它已经是Blob）
        const blob = response.data;

        // 获取文件名
        let fileName = "接报信息.xlsx";
        const contentDisposition = response.headers["content-disposition"];
        if (contentDisposition) {
          try {
            if (contentDisposition.includes("filename*=UTF-8''")) {
              const encodedFileName =
                contentDisposition.split("filename*=UTF-8''")[1];
              if (encodedFileName) {
                fileName = decodeURIComponent(encodedFileName);
              }
            } else if (contentDisposition.includes('filename="')) {
              const match = contentDisposition.match(/filename="([^"]+)"/);
              if (match && match[1]) {
                fileName = decodeURIComponent(match[1]);
              }
            } else if (contentDisposition.includes("filename=")) {
              const match = contentDisposition.match(/filename=([^;]+)/);
              if (match && match[1]) {
                fileName = decodeURIComponent(match[1].replace(/"/g, ""));
              }
            }
          } catch (error) {
            console.warn("文件名解析失败，使用默认文件名:", error);
          }
        }

        // 创建下载链接
        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;

        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);

        loading.close();
        this.$message.success("导出成功！");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败，请重试");
      }
    },
    // 旧新增弹框
    // handleAdd() {
    //   this.isEditMode = false;
    //   this.isContinueMode = false;
    //   this.editingRow = null;
    //   this.dialogTitle = "新增接报信息";
    //   this.dialogVisible = true;
    // },
    // 新新增弹框
    handleAdd() {
      this.addDialogVisible = true;
      this.addDialogType = "add";
    },
    handleEdit(row) {
      this.isEditMode = true;
      this.isContinueMode = false;
      this.editingRow = row;
      this.dialogTitle = "编辑接报信息";
      this.dialogVisible = true;
    },

    handleCancel() {
      this.dialogVisible = false;
      this.isEditMode = false;
      this.isContinueMode = false;
      this.editingRow = null;
    },

    async handleSubmit(submitData) {
      try {
        let response;
        if (this.isContinueMode) {
          // 续保模式：使用续报接口
          response = await receiveInformationApi.createEventProcessInfo(
            submitData
          );
        } else if (this.isEditMode) {
          // 编辑模式：使用更新接口
          response = await dutyManagementApi.updateReportInfo(submitData);
        } else {
          // 新增模式：使用创建接口
          response = await receiveInformationApi.createReportInfo(submitData);
        }

        if (response.code === 0) {
          let successMessage = "新增接报信息成功！";
          if (this.isEditMode) {
            successMessage = "编辑接报信息成功！";
          } else if (this.isContinueMode) {
            successMessage = "续报接报信息成功！";
          }

          this.$message.success(successMessage);
          this.dialogVisible = false;
          this.fetchData();
        } else {
          let errorMessage = "新增失败";
          if (this.isEditMode) {
            errorMessage = "编辑失败";
          } else if (this.isContinueMode) {
            errorMessage = "续报失败";
          }

          this.$message.error(response.message || errorMessage);
        }
      } catch (error) {
        this.$message.error("提交失败，请重试");
      }
    },

    handleDetailClose() {
      this.detailDialogVisible = false;
      this.currentDetailId = null;
      this.currentDetailTitle = "";
      this.fetchData();
    },
    handleAddDialogClose() {
      this.addDialogVisible = false;
    },
  },
};
</script>

<style scoped>
.receive-information-container {
  padding: 20px;
}
</style>
