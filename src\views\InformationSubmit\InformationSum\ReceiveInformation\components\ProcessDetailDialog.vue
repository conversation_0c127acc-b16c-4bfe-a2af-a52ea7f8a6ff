<!--
  过程详情弹框组件 - Component

  主要功能：
  - 显示单个过程项的详细信息
  - 包含责任部门、事件标题、事发时间地点、事件详情等信息
  - 显示伤亡统计信息
-->

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="过程详情"
    width="70%"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="false"
    class="process-detail-dialog"
  >
    <div v-loading="loading" class="process-detail-content">
      <div v-if="currentProcessItem && !loading" class="detail-content-area">
        <!-- 基本信息区域 -->
        <div class="info-section main-section">
          <div class="section-title">基本信息</div>
          <div class="info-grid base-info">
            <!-- <div class="info-item">
              <div class="info-label">责任部门</div>
              <div class="info-value">
                {{
                  currentProcessItem.responsibleDepartment ||
                  currentProcessItem.eventReportingUnit ||
                  currentProcessItem.originalData?.courseName ||
                  "-"
                }}
              </div>
            </div> -->
            <div class="info-item">
              <div class="info-label">事件标题</div>
              <div class="info-value event-title">
                {{
                  currentProcessItem.eventTitle ||
                  currentProcessItem.originalData?.infoTitle ||
                  "-"
                }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">事发时间</div>
              <div class="info-value">
                {{
                  formatDate(currentProcessItem.originalData?.infoTime) || "--"
                }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">事发地点</div>
              <div class="info-value">
                {{
                  currentProcessItem.originalData?.infoLocationDetail || "--"
                }}
              </div>
            </div>
            <div class="info-item">
              <div class="map">
                <tian-di-tu ref="mapRef" />
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">事件类型</div>
              <div class="info-value">
                {{ currentProcessItem.infoType || "--" }}
              </div>
            </div>
          </div>
        </div>

        <!-- 伤亡统计区域 -->
        <div class="info-section">
          <div class="section-title">伤亡统计</div>
          <div class="casualty-summary">
            <div class="casualty-item">
              <div class="casualty-number death">
                {{ currentProcessItem.deathNum || 0 }}
              </div>
              <div class="casualty-label">死亡</div>
            </div>
            <div class="casualty-item">
              <div class="casualty-number missing">
                {{ currentProcessItem.missingNum || 0 }}
              </div>
              <div class="casualty-label">失联</div>
            </div>
            <div class="casualty-item">
              <div class="casualty-number severe">
                {{ currentProcessItem.severeInjuryNum || 0 }}
              </div>
              <div class="casualty-label">重伤</div>
            </div>
            <div class="casualty-item">
              <div class="casualty-number light">
                {{ currentProcessItem.lightInjuryNum || 0 }}
              </div>
              <div class="casualty-label">轻伤</div>
            </div>
          </div>
        </div>

        <!-- 事件详情区域 -->
        <div class="info-section">
          <div class="section-title">事件详情</div>
          <div class="event-detail-content">
            {{
              currentProcessItem.eventInfo ||
              currentProcessItem.originalData?.courseInfo ||
              "暂无详细信息"
            }}
          </div>
        </div>

        <!-- 上报信息区域 -->
        <div class="info-section">
          <div class="section-title">上报信息</div>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">上报方式</div>
              <div class="info-value">
                {{ currentProcessItem.infoReportingMethod || "-" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">上报时间</div>
              <div class="info-value">
                {{ formatDate(currentProcessItem.createTime) || "-" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">上报单位</div>
              <div class="info-value">
                {{
                  currentProcessItem.eventReportingUnit ||
                  currentProcessItem.originalData?.courseName ||
                  "-"
                }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">上报人</div>
              <div class="info-value">
                {{ currentProcessItem.eventReportingUser || "-" }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="!loading && visible && currentProcessItem === null"
        class="no-selection"
      >
        <i class="el-icon-info"></i>
        <p>暂无详情信息</p>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import TianDiTu from "@/components/TianDiTu.vue";
export default {
  components: {
    TianDiTu,
  },
  name: "ProcessDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentProcessItem: {
      type: Object,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },

    // 设置经纬度
    mapMarkLatAndLng() {
      setTimeout(() => {
        if (
          this.currentProcessItem?.infoLongitude &&
          this.currentProcessItem?.infoLatitude
        ) {
          this.$refs.mapRef.addMarker(
            this.currentProcessItem.infoLongitude,
            this.currentProcessItem.infoLatitude
          );
        }
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.process-detail-dialog {
  .process-detail-content {
    min-height: 200px;

    .detail-content-area {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: minmax(100px, auto) minmax(300px, auto) minmax(
          100px,
          auto
        );
      gap: 16px;
      .info-section.main-section {
        grid-row: span 3;
        height: 100%;
      }
      .info-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;
          padding-left: 12px;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #409eff;
            border-radius: 2px;
          }
        }

        .info-grid.base-info {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px 24px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .info-item {
            .info-label {
              font-size: 13px;
              color: #909399;
              margin-bottom: 6px;
              font-weight: 500;
            }

            .info-value {
              color: #303133;
              font-size: 14px;
              line-height: 1.5;
              word-break: break-word;

              &.event-title {
                font-weight: 500;
                color: #409eff;
              }
            }
            .map {
              width: 90%;
              height: 300px;
              border: 1px solid #409eff;
              border-radius: 4px;
            }
          }
        }

        .event-detail-content {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          padding: 16px;
          min-height: 230px;
          line-height: 1.6;
          color: #303133;
          font-size: 14px;
          white-space: pre-wrap;
          word-break: break-word;
        }

        .casualty-summary {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
          }

          .casualty-item {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px 16px;
            text-align: center;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transform: translateY(-2px);
            }

            .casualty-number {
              font-size: 24px;
              font-weight: 700;
              margin-bottom: 8px;
              display: block;

              &.death {
                color: #f56c6c;
              }

              &.missing {
                color: #e6a23c;
              }

              &.severe {
                color: #f56c6c;
              }

              &.light {
                color: #409eff;
              }
            }

            .casualty-label {
              font-size: 13px;
              color: #000;
              font-weight: 700;
            }
          }
        }
      }
    }

    .no-selection {
      text-align: center;
      padding: 40px;
      color: #999;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
