<template>
  <div class="duty-evaluation-statistics-container">
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover">
          <el-form :inline="true" :model="searchData" class="demo-form-inline">
            <el-form-item label="单位名称">
              <el-input
                v-model="searchData.orgName"
                placeholder="请输入单位名称"
              ></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="onSubmit"
                >高级筛选</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-refresh"
                @click="resetSubmit"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover">
          <div class="plate-title">{{ statisticsTitle }}</div>
          <div class="Statistics">
            <div
              class="Statistics-item"
              v-for="(item, index) in statisticsData"
              :key="item.title"
            >
              <div class="vertical-line" :class="`bg-color-${index % 4}`"></div>
              <div class="content">
                <div class="image">
                  <el-image
                    :src="
                      require(`@/assets/images/statistics_${index + 1}.png`)
                    "
                  />
                </div>
                <div class="content-text">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <div class="data">
                    <div class="number">{{ item.data }}</div>
                  </div>
                  <div
                    class="percentage"
                    :style="{ color: getPercentageColor(item.percentage) }"
                  >
                    {{ item.percentage }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="plate-title">{{ noticeTitle }}</div>
          <div class="notification-container">
            <div
              v-for="(section, index) in sections"
              :key="index"
              class="section"
            >
              <h3 class="section-title">{{ section.title }}</h3>
              <p
                v-for="(paragraph, pIndex) in section.content"
                :key="pIndex"
                class="paragraph"
              >
                {{ paragraph }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-row :style="{ margin: '0px' }">
          <el-card
            :body-style="{ padding: '0px', margin: '0px' }"
            shadow="hover"
          >
            <div class="list-card-container">
              <div
                style="border-left: 4px solid #007bff; padding-left: 8px"
                class="list-card-title"
              >
                优秀单位名单
              </div>
              <div class="list-card-content">
                <table class="unit-table">
                  <thead>
                    <tr>
                      <th>排名</th>
                      <th>综合得分</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in excellentUnitData" :key="index">
                      <td class="left-align">{{ item.orgName }}</td>
                      <td>{{ item.totalFinalScore }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-card>
        </el-row>
        <el-row :style="{ margin: '20px 0 0 0' }">
          <el-card
            :body-style="{ padding: '0px', margin: '0px' }"
            shadow="hover"
          >
            <div class="list-card-container proble-bgc">
              <div
                style="border-left: 4px solid #007bff; padding-left: 8px"
                class="list-card-title"
              >
                存在问题单位
              </div>
              <div class="list-card-content">
                <table class="unit-table">
                  <thead>
                    <tr>
                      <th>排名</th>
                      <th>综合得分</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in probleUnitData" :key="index">
                      <td class="left-align">{{ item.orgName }}</td>
                      <td>{{ item.totalFinalScore }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-card>
        </el-row>
      </el-col>
    </el-row>
    <el-row :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="plate-title">单位排名情况</div>
          <div class="table-container">
            <el-table
              max-height="300px"
              :data="tableData"
              border
              style="width: 100%"
            >
              <el-table-column
                align="center"
                label="排名"
                prop="rank"
                width="50"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="orgName"
                label="单位"
                width="180"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="totalFinalScore"
                label="综合得分"
              >
              </el-table-column>
              <el-table-column
                align="center"
                prop="ratingLevel"
                label="评价等级"
              >
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="plate-title">附件下载</div>
          <div class="download-container">
            <div
              class="download-item"
              v-for="(item, index) in downloadData"
              :key="index"
              @click="handleDownload(item)"
            >
              <div>{{ item.name }}</div>
              <div>{{ item.size }}</div>
              <div>{{ item.date }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row> </el-row>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import echartsComponent from "@/components/echarts.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  name: "ComprehensiveEvaluationReport",
  components: {
    PortalTable,
    echartsComponent,
  },
  data() {
    return {
      searchData: {
        orgName: "",
      },
      statisticsTitle: "2025年度应急值守综合评价通报",
      statisticsData: [
        {
          title: "全市平均得分",
          data: "92.5",
          percentage: "3.2%",
        },
        {
          title: "参与评价单位",
          data: "126",
          percentage: "5%",
        },
        {
          title: "自评完成率",
          data: "98.6%",
          percentage: "1.2%",
        },
        {
          title: "优秀率",
          data: "45%",
          percentage: "3.2%",
        },
      ],
      typeOptions: {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {d}%",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542", "#24d2d3"],
        series: [
          {
            name: "评价等级统计",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 30, name: "优秀" },
              { value: 25, name: "良好" },
              { value: 25, name: "合格" },
              { value: 20, name: "不及格" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "84.5",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "平均分",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData: [
        {
          title: "优秀",
          number: 30,
          color: "#fb788a",
        },
        {
          title: "良好",
          number: 25,
          color: "#52c1f5",
        },
        {
          title: "合格",
          number: 25,
          color: "#ffc542",
        },
        {
          title: "不及格",
          number: 20,
          color: "#24d2d3",
        },
      ],
      tableData: [
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
        {
          region: "东城区",
          compositeScore: "97.2",
          rating: "优秀",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
      downloadData: [
        {
          name: "2025年度应急值守综合评价报告-第一季度.pdf",
          size: "980 KB",
          date: "2025-01-15",
        },
        {
          name: "2025年度应急值守工作分析总结-第二季度.pdf",
          size: "1.5 MB",
          date: "2025-02-15",
        },
        {
          name: "2025年度应急值守综合评估结果.pdf",
          size: "2.1 MB",
          date: "2025-02-24",
        },
        {
          name: "2025年度应急值守绩效评估报告-最终版.pdf",
          size: "750 KB",
          date: "2025-03-21",
        },
        {
          name: "2025年度应急值守工作成果总结报告.pdf",
          size: "1.8 MB",
          date: "2025-06-25",
        },
      ],
      excellentUnitData: [
        {
          name: "东城区应急管理局",
          score: "97.5",
        },
        {
          name: "西城区应急管理局",
          score: "96.8",
        },
        {
          name: "朝阳区卫生局",
          score: "96.2",
        },
        {
          name: "通州区卫生局",
          score: "95.5",
        },
        {
          name: "房山区卫生局",
          score: "94.8",
        },
      ],
      probleUnitData: [
        {
          name: "丰台区应急管理局",
          score: "77.5",
        },
        {
          name: "通州区应急管理局",
          score: "56.8",
        },
        {
          name: "通州区卫生局",
          score: "76.2",
        },
        {
          name: "房山区卫生局",
          score: "65.5",
        },
        {
          name: "密云区卫生局",
          score: "54.8",
        },
      ],
      sections: [
        {
          title: "一、总体情况",
          content: [
            "为进一步加强全市应急值守工作，提高应急处置能力，根据《关于开展2025年度应急值守工作综合评价的通知》要求，市应急管理局组织对全市各地区、各部门应急值守工作进行了综合评价。现将有关情况通报如下：",
            "本次综合评价涵盖全市16个区和110个市级部门，评价内容包括值班制度建设、值班人员配备、值班信息报送、应急处置能力、值班设施保障等方面。通过自评、系统评价和综合评定三个环节，最终形成评价结果。",
            "全市平均得分为92.5分，较上一年度提高3.2%。其中，优秀(90分以上)单位占45%，良好(80-89分)单位占32%，合格(70-79分)单位占18%，不合格(70分以下)单位占5%。总体来看，全市应急值守工作水平稳步提升，多数单位能够严格落实值班制度，规范值班信息报送，有效处置各类突发事件。",
          ],
        },
        {
          title: "二、主要成效",
          content: [
            "(一)值班制度建设进一步完善。多数单位能够按照要求建立健全值班工作制度，明确值班职责、值班流程和工作标准，值班工作规范化水平不断提高。",
            "(二)值班人员配备更加合理。各单位普遍加强了值班力量，配足配强值班人员，特别是在节假日和重要敏感时期，能够严格落实领导带班和24小时专人值班制度。",
            "(三)值班信息报送更加及时准确。多数单位能够严格执行突发事件信息报告制度，及时、准确、规范地报送各类突发事件信息，信息报送的时效性和质量明显提高。",
            "(四)应急处置能力不断提升。各单位结合实际制定完善应急预案，加强应急演练，提高应急处置能力，能够有效应对各类突发事件，最大限度减少损失和影响。",
          ],
        },
        {
          title: "三、存在问题",
          content: [
            "(一)部分单位对值班工作重视程度不够，存在值班人员配备不足、值班制度执行不严格等问题。",
          ],
        },
      ],
      noticeTitle: "通报正文",
    };
  },
  methods: {
    // 查询机构排名情况列表
    queryOrgRanStatusList() {
      comprehensiveAssessmentApi
        .queryOrgRanStatusList({
          count: this.pagination.pageSize,
          page: this.pagination.currentPage,
          ...this.searchData,
        })
        .then((res) => {
          this.tableData = res.data;
          this.pagination.total = res.total;
        });
    },
    handleSearch(params) {
      console.log(params);
    },
    getPercentageColor(percentage) {
      const value = parseFloat(percentage);
      if (value > 1) return "#67C23A";
      if (value < 1) return "#F56C6C";
      return "#999"; // 等于1时的颜色
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
    },
    handleDetail(row) {
      this.$refs.detailDialogRef.detailDialogVisible = true;
    },
    handleDownload(item) {
      this.$message.warning("暂无数据，无法提供下载！");
      console.log(item);
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryOrgRanStatusList();
    },
    resetSubmit() {
      this.searchData = {
        orgName: "",
      };
      this.queryOrgRanStatusList();
    },
    // 优秀单位名单
    queryExcellentUnitList() {
      comprehensiveAssessmentApi
        .queryExcellentUnitList({
          // orgName: "关键字",
          // type: "评价类型",
        })
        .then((res) => {
          this.excellentUnitData = res.data;
        });
    },
    // 存在问题单位名单
    queryProblemsUnitList() {
      comprehensiveAssessmentApi
        .queryProblemsUnitList({
          // orgName: "关键字",
          // type: "评价类型",
        })
        .then((res) => {
          this.probleUnitData = res.data;
        });
    },
  },
  mounted() {
    this.queryOrgRanStatusList();
    this.queryExcellentUnitList();
    this.queryProblemsUnitList();
  },
};
</script>

<style lang="scss" scoped>
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
  .Statistics {
    display: flex;
    gap: 30px;
    .Statistics-item {
      padding-left: 15px;
      height: 140px;
      display: flex;
      gap: 15px;
      .vertical-line {
        width: 6px;
        height: 120px;
      }
      .bg-color-0 {
        background-color: #007bff; // 蓝色
      }
      .bg-color-1 {
        background-color: #9f47ec; // 绿色
      }
      .bg-color-2 {
        background-color: #0ce63f; // 黄色
      }
      .bg-color-3 {
        background-color: #cc3b4e; // 红色
      }

      .content {
        height: 100px;
        display: flex;
        align-items: center;
        margin-top: 10px;
        gap: 20px;
        .image {
          width: 50px;
          height: 50px;
        }
        .content-text {
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-bottom: 1px solid #3b3a3a;
          width: 200px;
          .title {
            font-size: 15px;
            margin-bottom: 5px;
          }
          .data {
            display: flex;
            gap: 40px;
            align-items: flex-end;
            margin-bottom: 5px;
            .number {
              font-size: 20px;
              font-weight: bold;
              color: #007bff;
            }
            .unit {
              font-size: 10px;
              color: #999;
            }
          }
          .percentage {
            font-size: 15px;
            color: #999;
          }
        }
      }
    }
  }
  .text-notification {
    height: 723px;
    overflow-y: auto;
  }
  .notification-container {
    height: 723px;
    overflow-y: auto;
    font-family: "Microsoft YaHei", sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0 auto;
  }
  .list-card-container {
    width: 100%;
    height: 400px;
    // background-color: #caf982;
    padding: 20px;
    .list-card-title {
      font-size: 20px;
      margin-bottom: 20px;
    }
    .list-card-content {
      margin: 0 20px;
      .list-card-item {
        margin-bottom: 10px;
      }
      .unit-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 10px;
        th,
        td {
          text-align: center; // 居中对齐
          padding: 8px 12px; // 单元格内边距
          vertical-align: middle; // 垂直居中
          &.left-align {
            text-align: center; // 第一列左对齐
          }

          &:not(.left-align) {
            text-align: center; // 其他列居中对齐
          }
        }
      }
    }
  }
  .proble-bgc {
    // background-color: #faf0c0;
  }
  .chart-title,
  .table-title {
    font-size: 20px;
    font-weight: bold;
  }
  .plate-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 30px;
  }
  .chart-container {
    height: 300px;
    display: flex;
    gap: 20px;
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 8%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .table-container {
    height: 320px;
  }
  .download-container {
    margin-left: 30px;
    height: 320px;
    .download-item {
      display: flex;
      gap: 30px;
      cursor: pointer;
      margin-bottom: 10px;
    }
    .download-item:hover {
      color: #242eee;
    }
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>