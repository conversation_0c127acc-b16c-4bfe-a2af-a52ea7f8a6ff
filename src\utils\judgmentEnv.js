/**
 * 环境判断
 * @returns
 */
export const judgmentEnv = () => {
  const HOST = window.location.host;
  const PROD = "http://xxx.xxx.xxx.xxx:xxxx"; // 生产环境
  let ENV;
  switch (HOST) {
    case PROD:
      ENV = "prod";
      break;
    default:
      // ENV = "dev";
      ENV = "prod";
      break;
  }
  return ENV;
};

/**
 * 获取baseUrl
 * @returns
 */
export const getBaseURL = () => {
  // return "http://*************:9801/v1";     // 开发环境
  // return 'http://*************:9801/v1/';    // 测试环境
  return "http://*************:18889"; // 本地环境
  // const HOST = window.location.host;
  // const PROD = "http://*************:9801/v1"; // 生产环境
  // let baseURL;
  // switch (HOST) {
  //   case PROD:
  //     baseURL = PROD; // 生产环境
  //     break;
  //   default:
  //     baseURL = "/api"; // 使用代理路径
  //     break;
  // }
  // return baseURL;
};
