<template>
    <general-dialog
        :dialog-visible="dialogVisible"
        :dialog-width="dialogWidth"
        :general-dialog-title="generalDialogTitle"
        :set-component-name="$store.getters.componentName"
        :showFooter="formType != 3"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      >
        <el-form
          ref="addForm"
          :model="form"
          :rules="rules"
          class="add-form demo-form-inline"
          label-position="right"
          label-width="150px"
        >
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="用户名称" prop="userName">
                        <el-input :disabled="true" v-model="form.userName" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="用户分级" prop="userClass">
                        <el-input :disabled="true" v-model="form.userClass" />
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="所属机构" prop="orgName">
                        <el-input :disabled="true" v-model="form.orgName" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">                    
                    <el-form-item label="用户职务" prop="positions">
                        <el-input :disabled="true" v-model="form.positions" />
                    </el-form-item>
                 </el-col>
            </el-row>

                <el-form-item label="分配角色" prop="phone">
                    <addUersDialog ref="addUersDialogRef" @addIds="addIdsFn" :allocationData="allocationData" />
                </el-form-item>
        </el-form>
    </general-dialog>
</template>

<script>
import { orgApi } from '@/api/index'
import GeneralDialog from "@/components/GeneralDialog.vue";
import addUersDialog from "./addUersDialog.vue"



export default {
    components: { GeneralDialog,addUersDialog },
    data() {
        return {
            title: '',
            formType:1,
            rowData:{},
            dialogVisible: false,
            dialogWidth: "68%",
            generalDialogTitle: "角色分配",
            allocationData:[],
            form: {
                userName:"",
                userClass:"",
                orgName:"",
                positions:"",
                roleIds:[],
            },
            rules: {
            },
        }
    },
    created(){
    },
    mounted(){
        
    },
    methods:{
        addIdsFn(data){ //选中的角色
            this.form.roleIds = data;
        },
        handleChange(value){
        },
        addEditShowFn(data){
            this.formType = data.formType; //1 添加 2 编辑 3查看 4分配角色
            this.generalDialogTitle = "分配角色";
            this.rowData = data.row?data.row:[];
            this.form = {
                userName:this.rowData.userName,
                userClass:this.rowData.userClass,
                orgName:this.rowData.orgName,
                positions:this.rowData.positions
            };
            this.allocationXq(this.rowData.id);
            this.dialogVisible = true;
        },
        async allocationXq(id){ //查询角色分配
            this.allocationData = [];
            const res = await orgApi.queryRoleAssignment({id:id});
            const {code, data, message, error} = res;
            if (code === 0) {
                if(data.length > 0){
                     data.map(item => {
                        this.allocationData.push({
                            roleId:item.roleId
                        })
                    })
                }
            } else {
              this.$message.error(message || error);
            }
        },

        //取消
        handleCancel() {
            this.dialogVisible = false;
            this.form = {
                userName:"",
                userClass:"",
                orgName:"",
                positions:""
            };
        },
        handleSubmit() {
            if(this.form.roleIds.length == 0){
                this.$message.error("请选择角色");
                return;
            }
            var dataObj = JSON.parse(JSON.stringify(this.form)); // 深拷贝用于请求
            this.addFn(dataObj);
        },
        async addFn(dataObj){
            const res = await orgApi.roleAssignment({id:this.rowData.id, roleIds:dataObj.roleIds});
            const {code, message, error} = res;
            if (code === 0) {
              this.dialogVisible = false;
              this.$message.success('分配角色成功');
              this.$emit('confirm');
            } else {
              this.$message.error(message || error);
            }
        },
    }
}
</script>

<style scoped lang="scss">

</style>