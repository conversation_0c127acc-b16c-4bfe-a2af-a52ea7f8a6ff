<template>
  <div class="form-section">
    <!-- 伤亡信息表单组件 - Component -->
    <h4 class="section-title">
      <i class="el-icon-user"></i>
      伤亡信息
    </h4>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="死亡人数">
          <el-input-number
            :value="value.deathCount"
            :min="0"
            :max="9999"
            placeholder="0"
            style="width: 100%"
            @input="handleDeathCountChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="失联人数">
          <el-input-number
            :value="value.missingCount"
            :min="0"
            :max="9999"
            placeholder="0"
            style="width: 100%"
            @input="handleMissingCountChange"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="重伤人数">
          <el-input-number
            :value="value.seriousInjuryCount"
            :min="0"
            :max="9999"
            placeholder="0"
            style="width: 100%"
            @input="handleSeriousInjuryCountChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="轻伤人数">
          <el-input-number
            :value="value.minorInjuryCount"
            :min="0"
            :max="9999"
            placeholder="0"
            style="width: 100%"
            @input="handleMinorInjuryCountChange"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "CasualtyInfoForm",
  props: {
    value: {
      type: Object,
      default: () => ({
        deathCount: 0,
        missingCount: 0,
        seriousInjuryCount: 0,
        minorInjuryCount: 0,
      }),
    },
  },
  methods: {
    handleDeathCountChange(value) {
      this.$emit("input", { ...this.value, deathCount: value });
    },
    handleMissingCountChange(value) {
      this.$emit("input", { ...this.value, missingCount: value });
    },
    handleSeriousInjuryCountChange(value) {
      this.$emit("input", { ...this.value, seriousInjuryCount: value });
    },
    handleMinorInjuryCountChange(value) {
      this.$emit("input", { ...this.value, minorInjuryCount: value });
    },
  },
};
</script>

<style scoped>
.form-section {
  margin-bottom: 30px;
  padding: 0;
  background: #ffffff;
  border: none;
  position: relative;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0;
  color: #0a1629;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.section-title::before {
  display: none;
}

.section-title i {
  color: #4569af;
  font-size: 16px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}

.el-input-number .el-input__inner {
  text-align: left;
  padding-left: 12px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
  font-size: 14px;
  background-color: #ffffff;
}

.el-input-number .el-input__inner:hover {
  border-color: #c0c4cc;
}

.el-input-number .el-input__inner:focus {
  border-color: #4569af;
  box-shadow: none;
  outline: none;
}

.el-input-number .el-input-number__increase,
.el-input-number .el-input-number__decrease {
  border-color: #dcdfe6;
}

.el-input-number .el-input-number__increase:hover,
.el-input-number .el-input-number__decrease:hover {
  color: #409eff;
  border-color: #409eff;
}
</style>
