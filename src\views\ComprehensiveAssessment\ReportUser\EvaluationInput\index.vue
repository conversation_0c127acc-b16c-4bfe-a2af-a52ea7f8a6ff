<template>
    <div class="evaluation-input">
        
        <div class="title-top">
            <div>
                <p class="title">应急值守综合评价</p>
                <p class="color">请填写自评内容并上传佐证材料</p>
            </div>
            <div>
                <el-button icon="el-icon-warning-outline">帮助</el-button>
                <el-button type="primary" icon="el-icon-receiving">保存草稿</el-button>
            </div>
        </div>

        <PieceTop :dataArr="dataArr" />

        <div class="form-box">
            <div class="title">个人基本信息</div>
            <el-form :label-position="'top'" label-width="80px" :model="form">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="form.name" :disabled="true" autocomplete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="部门" prop="department">
                            <el-input v-model="form.department" :disabled="true" autocomplete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="职位" prop="position">
                            <el-input v-model="form.position" :disabled="true" autocomplete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="phone">
                            <el-input v-model="form.phone" :disabled="true" autocomplete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <div class="teb-box">

                <div class="header">
                    <div class="item w-8">评价类别</div>
                    <div class="item">评价指标要点</div>
                    <div class="item">评价标准</div>
                    <div class="item w-8">自评分数</div>
                    <div class="item w-8">材料</div>
                </div>

                <div class="teb-item">
                    <div class="item" v-for="(item,index) in tebData">
                        <div class="title  w-8"  style="align-content: center;text-align: center;">
                            <span>{{ item.title }}</span>
                        </div>
                        <div class="teb-tr">
                            <div class="txt-item" v-for="(items,indexs) in item.list">
                                <span>{{ items.name }}</span>
                            </div>
                        </div>
                        
                        <div class="teb-tr">
                            <div class="txt-item" v-for="(items,indexs) in item.list">
                                <span>{{ items.name1 }}</span>
                            </div>
                        </div>
                        
                        <div class="teb-tr  w-8">
                            <div class="txt-item" v-for="(items,indexs) in item.list">
                                <span>11</span>
                            </div>
                        </div>
                        
                        <div class="teb-tr  w-8">
                            <div class="txt-item" v-for="(items,indexs) in item.list">
                                <span>xx</span>
                            </div>
                        </div>
                    </div>

                </div>

            </div>







        </div>


    </div>
</template>

<script>
import PieceTop from './components/pieceTop.vue'


export default {
    components: {
        PieceTop
    },
    data() {
        return {
            dataArr: [],
            form:{
                name:"张xx",
                department:"朝阳区街道办事处",
                position:"处长",
                phone:"138xxxx8888",
            },
            tebData:[
                {
                    title:"管理体制",
                    list:[
                        {
                            name:"1.贯彻落实《北京市应急值守工作管理规范》，区政府履行本级政府系统应急值守工作主体责任，强化区政府办公室与区应急管理部门的协同机制，不断完善工作体制。(2分)",
                            name1:"未制定或修订本地区应急值守工作制度规范的，扣1分;区政府办公室未按规定履行本级政府系统应急值守工作管理责任的，扣1分;未按规定授权区应急管理部门承担本级政府总值班室应急值守职责的，扣1分。",
                            in:2
                        },
                        {
                            name:"2.健全本地区应急管理组织体系，加强对应急值守工作的组织领导和安排部署。(2分)65",
                            name1:"未制定印发本地区 2023 年应急值守工作要点的，扣1分;2022年本地区党委政府换届后，在 2023年8月底前未调整区应急委和各专项指挥部领导成员的，扣1分。",
                            in:1
                        },
                    ]
                },
                {
                    title:"值班值守",
                    list:[
                        {
                            name:"3.做好法定节假日和重大活动期间值班值守工作。(4分)",
                            name1:"未制定本均,区法定节假日和重大活动期间值班值守工作方案的，扣2分:未按照上级通知要求，严格执行三级 24 小时在岗带班值班等制度的，每发生一次扣分;未建立相关工作复盘总结机制的，扣1分。因值班值守工作，被国务院总值班室或应急管理部通报批评的，此项不得分。",
                            in:4
                        },
                        {
                            name:"4.建立规范的应急值守交接班制度。(2分)",
                            name1:"未制定本圳区应急值守交接班制度的，扣2分:未按规定登记每日应急值守情况或们班日志存在严重缺失的，扣1分。",
                            in:2
                        },
                        {
                            name:"5.督促、检查本地区政府系统值班值守工作。(4分)",
                            name1:"未制定本均,区法定节假日和重大活动期间值班值守工作方案的，扣2分:未按照上级通知要求，严格执行三级 24 小时在岗带班值班等制度的，每发生一次扣分;未建立相关工作复盘总结机制的，扣1分。因值班值守工作，被国务院总值班室或应急管理部通报批评的，此项不得分。",
                            in:2
                        },
                    ]
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.evaluation-input {
    p {
        margin: 0;
    }
    .title-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 2%;
        .title {
            font-size: 24px;
            margin-bottom: 6px;
        }
        .color {
            color: var(--text-auxiliary);
            .color {
                color: var(--icon-danger);
            }
        }
    }
}

.form-box {
    box-shadow: 0 1px 6px 0px rgba(215, 215, 215, 0.75);
    padding: 20px 2%;
    border-radius: 12px;
    margin: 20px 2%;
    .title {
        margin-bottom: 20px;
    }
    ::v-deep .el-form-item__label {
        line-height: 12px
    }
}

.teb-box {
    .header {
        display: flex;
        justify-content: space-between;
        border: solid 1px var(--text-placeholder);
        border-left: none;
        .item {
            border-left: solid 1px var(--text-placeholder);
            padding: 12px;
            text-align: center;
            width: 37%;
            font-size: 16px;
            font-weight: bold;
        }
        .w-8 {
            width: 8%;
        }
    }
    .teb-item {
        border-right: 1px var(--text-placeholder) solid;
        .item {
            display: flex;
            justify-content: space-between;
            border-bottom: solid 1px var(--text-placeholder);
            > div {
                width: 37%;
                border-left: solid 1px var(--text-placeholder);
                margin-bottom: 0;
            }
            .w-8 {
                width: 8%;
            }
            .teb-tr {
                display: grid;
                 .txt-item:nth-child(1) {
                    border: none;
                }
                .txt-item {
                    border-top: solid 1px var(--text-placeholder);
                    height: 100px;
                    display: flex;
                    align-items: center;
                    padding: 0 1.5%;
                }
            }
            
        }
    }
   
}
</style>