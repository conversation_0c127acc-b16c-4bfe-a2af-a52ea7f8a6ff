import request from "@/utils/request";

/**
 * 用户管理相关API接口
 */

const userData = [
  {
    id: 1,
    name: "王强",
    phone: "***********",
    username: "<PERSON><PERSON><PERSON><PERSON>",
    mobile: "***********",
    landline: "***********",
    department: "市应急管理局/应急指挥中心",
    position: "处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-15",
  },
  {
    id: 2,
    name: "张雪林",
    phone: "***********",
    username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    mobile: "***********",
    landline: "***********",
    department: "市应急管理局/应急指挥中心",
    position: "处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-16",
  },
  {
    id: 3,
    name: "刘涛",
    phone: "***********",
    username: "liuta<PERSON>",
    mobile: "***********",
    landline: "***********",
    department: "市应急管理局/应急指挥中心",
    position: "处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-17",
  },
  {
    id: 4,
    name: "邱康",
    phone: "***********",
    username: "qiukang",
    mobile: "***********",
    landline: "***********",
    department: "市应急管理局/应急指挥中心",
    position: "副处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心副处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-18",
  },
  {
    id: 5,
    name: "张伟",
    phone: "***********",
    username: "zhangwei",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/应急指挥中心",
    position: "副处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心副处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-19",
  },
  {
    id: 6,
    name: "张威",
    phone: "***********",
    username: "zhangwei2",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/应急指挥中心",
    position: "副处长",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心副处长",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-20",
  },
  {
    id: 7,
    name: "张雨",
    phone: "***********",
    username: "zhangyu",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/应急指挥中心",
    position: "科员",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心科员",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-21",
  },
  {
    id: 8,
    name: "张雨",
    phone: "***********",
    username: "zhangyu2",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/应急指挥中心",
    position: "科员",
    userType: "全部",
    accountStatus: "全部",
    remark: "应急指挥中心科员",
    departmentId: 11,
    status: "active",
    createTime: "2024-01-22",
  },
  {
    id: 9,
    name: "张雪林",
    phone: "***********",
    username: "zhangxuelin2",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/风险监测科",
    position: "科长",
    userType: "全部",
    accountStatus: "全部",
    remark: "风险监测科科长",
    departmentId: 12,
    status: "active",
    createTime: "2024-01-23",
  },
  {
    id: 10,
    name: "张数",
    phone: "***********",
    username: "zhangshu",
    mobile: "***********",
    landline: "",
    department: "市应急管理局/风险监测科",
    position: "科员",
    userType: "全部",
    accountStatus: "全部",
    remark: "风险监测科科员",
    departmentId: 12,
    status: "active",
    createTime: "2024-01-24",
  },
];

export default {
  async getUserInfo() {
    return request({
      url: "/ds/user/getUserInfo",
      method: "get",
    });
  },

  async getList(params = {}) {
    await new Promise((resolve) => setTimeout(resolve, 300));
    let data = [...userData];

    // 关键字搜索
    if (params.keyword) {
      data = data.filter(
        (item) =>
          item.name.includes(params.keyword) ||
          item.phone.includes(params.keyword) ||
          item.username.includes(params.keyword) ||
          item.department.includes(params.keyword)
      );
    }

    // 按姓名搜索
    if (params.name) {
      data = data.filter((item) => item.name.includes(params.name));
    }

    // 按用户名搜索
    if (params.username) {
      data = data.filter((item) => item.username.includes(params.username));
    }

    // 按电话搜索
    if (params.phone) {
      data = data.filter((item) => item.phone.includes(params.phone));
    }

    // 按部门搜索
    if (params.department) {
      data = data.filter((item) => item.department.includes(params.department));
    }

    // 按部门ID过滤
    if (params.departmentId) {
      data = data.filter((item) => {
        // 直接匹配部门ID
        if (item.departmentId === params.departmentId) {
          return true;
        }

        // 如果是子部门，也要包含在内
        const parentDeptMap = {
          11: 1, // 应急指挥中心 -> 市应急管理局
          12: 1, // 风险监测科 -> 市应急管理局
          21: 2, // 指挥中心 -> 市公安局
          22: 2, // 交警支队 -> 市公安局
          31: 3, // 特勤大队 -> 市消防救援支队
          32: 3, // 战勤保障大队 -> 市消防救援支队
          41: 4, // 医政医管科 -> 市卫健委
          42: 4, // 疾控中心 -> 市卫健委
          51: 5, // 运输管理科 -> 市交通局
          52: 5, // 路政管理科 -> 市交通局
        };

        // 如果选择的是父部门，包含所有子部门的用户
        return parentDeptMap[item.departmentId] === params.departmentId;
      });
    }

    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;

    const result = {
      data: {
        list: data.slice(start, start + pageSize),
        total: data.length,
        current: page,
        pageSize,
      },
    };

    return result;
  },

  async search(keyword, params = {}) {
    // 搜索功能，复用getList的逻辑
    return this.getList({ keyword, ...params });
  },

  async getByOrganization(orgId, params = {}) {
    // 按组织获取用户，复用getList的逻辑
    return this.getList({ departmentId: orgId, ...params });
  },

  async create(userData) {
    await new Promise((resolve) => setTimeout(resolve, 500));
    // 模拟创建用户
    return { success: true, data: { id: Date.now(), ...userData } };
  },

  async update(id, userData) {
    await new Promise((resolve) => setTimeout(resolve, 500));
    // 模拟更新用户
    return { success: true, data: { id, ...userData } };
  },

  async delete(id) {
    await new Promise((resolve) => setTimeout(resolve, 300));
    // 模拟删除用户
    return { success: true };
  },

  async batchDelete(ids) {
    await new Promise((resolve) => setTimeout(resolve, 500));
    // 模拟批量删除用户
    return { success: true };
  },
};
