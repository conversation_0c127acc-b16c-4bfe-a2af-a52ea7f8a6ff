<!--
  事件步骤条组件 - Component

  主要功能：
  - 显示事件处理4个步骤：待办→正在处置→启动响应→已办结
  - 根据当前事件状态高亮显示对应步骤
  - 支持前进点击，不支持后退
-->

<template>
  <div class="event-step-container">
    <div class="custom-step-bar">
      <div
        v-for="(step, index) in steps"
        :key="step.value"
        :class="[
          'step-item',
          {
            'step-active': index === activeStep && currentStatus != 2,
            'step-completed':
              index < activeStep ||
              (currentStatus == 2 && index === activeStep),
            'step-clickable': isStepClickable(step, index),
            'step-disabled': !isStepClickable(step, index),
          },
        ]"
        @click="handleStepClick(step, index)"
      >
        <div class="step-icon">
          <i :class="step.icon"></i>
        </div>
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-status">{{ getStepStatusText(index) }}</div>
        </div>
        <div class="step-connector" v-if="index < steps.length - 1"></div>
      </div>
    </div>

    <!-- 一键响应对话框组件 -->
    <OneClickResponseDialog
      :visible.sync="oneClickResponseDialogVisible"
      :detail-id="eventId"
      @success="handleResponseSuccess"
    />
  </div>
</template>

<script>
import { receiveInformationApi } from "@/api";
import OneClickResponseDialog from "./OneClickResponseDialog.vue";

export default {
  name: "EventStepComponent",
  components: {
    OneClickResponseDialog,
  },
  props: {
    eventId: {
      type: [String, Number],
      required: true,
    },
    currentStatus: {
      type: [String, Number],
      default: 3,
    },
  },
  data() {
    return {
      oneClickResponseDialogVisible: false,
      loading: false,
      steps: [
        {
          title: "待办",
          value: 3,
          icon: "el-icon-document",
        },
        {
          title: "正在处置",
          value: 0,
          icon: "el-icon-cpu",
        },
        {
          title: "启动响应",
          value: 1,
          icon: "el-icon-s-promotion",
        },
        {
          title: "处置完毕",
          value: 2,
          icon: "el-icon-circle-check",
        },
      ],
    };
  },
  computed: {
    activeStep() {
      const currentIndex = this.steps.findIndex(
        (step) => step.value == this.currentStatus
      );
      return currentIndex >= 0 ? currentIndex : 0;
    },
  },
  methods: {
    getStepStatus(index) {
      if (index < this.activeStep) {
        return "success";
      } else if (index === this.activeStep) {
        return "process";
      } else {
        return "wait";
      }
    },

    getStepStatusText(index) {
      if (this.currentStatus == 2 && index === this.activeStep) {
        return "已完成";
      }

      if (index < this.activeStep) {
        return "已完成";
      } else if (index === this.activeStep) {
        return "进行中";
      } else {
        return "待处理";
      }
    },

    isStepClickable(_step, index) {
      return index > this.activeStep;
    },

    async handleStepClick(step, index) {
      if (!this.isStepClickable(step, index)) {
        return;
      }

      if (step.value === 1) {
        this.oneClickResponseDialogVisible = true;
        return;
      }

      if (step.value === 2) {
        try {
          await this.$confirm(
            "确认将该事件标记为处置完毕吗？此操作将结束事件处理流程，请确保所有处置工作已完成。",
            "确认处置完毕?",
            {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
              dangerouslyUseHTMLString: false,
              showClose: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
            }
          );
        } catch (error) {
          return;
        }
      }

      try {
        this.loading = true;
        const params = {
          id: this.eventId,
          infoStatus: step.value,
        };

        const response = await receiveInformationApi.eventPostProcessor(params);

        if (response.code === 0) {
          this.$message.success(`已切换至${step.title}状态`);
          this.$emit("status-changed", step.value);
        } else {
          this.$message.error(response.message || "状态切换失败");
        }
      } catch (error) {
        console.error("状态切换失败:", error);
        this.$message.error("状态切换失败，请重试");
      } finally {
        this.loading = false;
      }
    },

    async handleResponseSuccess(responseData) {
      try {
        this.loading = true;

        const params = {
          id: this.eventId,
          infoStatus: 1,
          responseLevel: responseData.responseLevel,
          infoTypeList: responseData.infoTypeList,
        };

        const response = await receiveInformationApi.eventPostProcessor(params);

        if (response.code === 0) {
          this.$message.success("启动响应操作完成");
          this.$emit("status-changed", 1);
        } else {
          this.$message.error(response.message || "启动响应失败");
        }
      } catch (error) {
        console.error("启动响应失败:", error);
        this.$message.error("启动响应失败，请重试");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.event-step-container {
  position: sticky;
  top: 15px;
  z-index: 1000;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  padding: 16px;
  margin: 10px 0;
}

.custom-step-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  cursor: pointer;
  transition: all 0.2s ease;

  &.step-clickable:hover {
    .step-icon {
      border-color: #3b82f6;
      color: #3b82f6;
    }
  }

  &.step-disabled {
    cursor: not-allowed;

    .step-icon {
      background: #f3f4f6;
      border-color: #e5e7eb;
      color: #9ca3af;
    }

    .step-content {
      .step-title {
        color: #9ca3af;
      }
      .step-status {
        color: #d1d5db;
      }
    }
  }

  .step-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    border: 2px solid #d1d5db;
    background: #f9fafb;
    color: #6b7280;
  }

  .step-content {
    text-align: center;

    .step-title {
      font-size: 13px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 2px;
    }

    .step-status {
      font-size: 11px;
      color: #9ca3af;
    }
  }

  .step-connector {
    position: absolute;
    top: 14px;
    left: calc(50% + 14px);
    right: calc(-50% + 14px);
    height: 1px;
    background: #e5e7eb;
    z-index: 1;
  }

  &.step-completed {
    .step-icon {
      background: #10b981;
      border-color: #10b981;
      color: white;
    }

    .step-content {
      .step-title {
        color: #10b981;
      }
      .step-status {
        color: #10b981;
      }
    }

    .step-connector {
      background: #10b981;
    }
  }

  &.step-active {
    .step-icon {
      background: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }

    .step-content {
      .step-title {
        color: #3b82f6;
        font-weight: 600;
      }
      .step-status {
        color: #3b82f6;
      }
    }
  }

  &.step-clickable {
    .step-icon {
      border-color: #93c5fd;
      color: #3b82f6;
      background: #eff6ff;
    }

    .step-content {
      .step-title {
        color: #3b82f6;
      }
    }
  }
}
</style>
