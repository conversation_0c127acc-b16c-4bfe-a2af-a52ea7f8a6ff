<template>
  <div class="typical-case-container">
    <div class="typical-case-main" v-show="typicalCaseVisible">
      <portal-table
        style="width: 100%"
        :tableHeight="600"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="id"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @handle-selection-change="handleSelectionChange"
      />
    </div>
    <public-dialog
      ref="dialogRef"
      :eventTypeOptions="eventTypeOptions"
      @typicalCaseShow="typicalCaseVisible = true"
      @getTypicalCaseList="getTypicalCaseList"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import PublicDialog from "./components/publicDialog.vue";
import { emergencyKnowledgeBaseApi } from "@/api";
import { getItemList, eventType } from "@/utils/dictionary";
import conversionDate from "@/utils/publicMethod";
export default {
  name: "TypicalCase",
  components: {
    PortalTable,
    PublicDialog,
  },
  data() {
    return {
      typicalCaseVisible: true,
      searchItems: [
        {
          prop: "name",
          label: "智能检索",
          type: "input",
          placeholder: "请输入关键字搜索",
        },

        {
          prop: "eventType",
          label: "事件类型",
          type: "select",
          placeholder: "请输入关键字搜索",
          options: [],
        },
      ],
      eventTypeOptions: [],
      tableData: [],
      columns: [
        {
          text: true,
          prop: "name",
          label: "案例名称",
        },
        {
          text: true,
          prop: "eventType",
          label: "事故类型",
          formatter: this.formatterEventType,
        },

        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "查看",
              permission: "typicalCase:view",
              buttonClick: this.handleView,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "编辑",
              permission: "typicalCase:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "typicalCase:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 100,
      },
      form: {
        category: "",
        name: "",
        effectiveEndDate: "",
        effectiveStartDate: "",
        eventType: "",
        type: 4,
      },
      categoryOptions: [],
      selectedRows: [],
    };
  },

  methods: {
    // 获取案例数据
    async getTypicalCaseList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...this.form,
      };
      let res;
      try {
        res = await emergencyKnowledgeBaseApi.queryKnowledgeBasePage(params);
      } catch (error) {}
      this.tableData = res?.data.items || [];
      this.pagination.total = res?.data.total || 0;
    },
    // 搜索
    handleSearch(data) {
      console.log(data, "data", this.form);
      const {
        category = "",
        name = "",
        implementationTime = "",
        eventType = "",
      } = data;
      this.form = {
        ...this.form,
        category: category === "全部" ? "" : category,
        name,
        eventType: eventType === "全部" ? "" : eventType,
        effectiveEndDate: implementationTime[1]
          ? conversionDate(implementationTime[1])
          : "",
        effectiveStartDate: implementationTime[0]
          ? conversionDate(implementationTime[0])
          : "",
      };
      this.pagination.currentPage = 1;
      this.pagination.pageSize = 10;
      this.getTypicalCaseList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.getTypicalCaseList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTypicalCaseList();
    },

    addHandler() {
      this.typicalCaseVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "add";
      this.$refs.dialogRef.title = "新增案例";
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
    },
    handleSelectionChange(selection) {
      console.log(selection, "selection");
      this.selectedRows = [];
      selection.forEach((item) => {
        this.selectedRows.push(item.id);
      });
    },
    deleteHandler() {
      if (this.selectedRows.length === 0) {
        this.$message({
          message: "请选择要删除选项",
          type: "warning",
        });
        return;
      }
      this.deleteKnowledgeItems(this.selectedRows);
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "deleteBatch_top",
        handler: this.deleteHandler,
      });
    },
    //取消
    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        groupExplain: "",
        groupName: "",
      };
    },
    //提交
    handleSubmit() {
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType == "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .createGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.pagination.currentPage = 1;
              this.pagination.pageSize = 10;
              this.$refs.addForm.resetFields();
              this.getTypicalCaseList();
            })
            .catch(() => {
              // this.$message.error("新增失败");
            });
        }
      });
    },
    // 列表编辑提交
    editSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .updateGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.$refs.addForm.resetFields();
              this.getTypicalCaseList();
            })
            .catch(() => {
              // this.$message.error("编辑失败");
            });
        }
      });
    },
    // 列表删除按钮
    handleDelete(row) {
      this.deleteKnowledgeItems([row.id]);
    },
    deleteKnowledgeItems(ids) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          emergencyKnowledgeBaseApi
            .deleteKnowledgeBase({ id: ids })
            .then(() => {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getTypicalCaseList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 列表编辑按钮
    handleEdit(row) {
      this.typicalCaseVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "edit";
      this.$refs.dialogRef.title = "编辑案例";
      this.$refs.dialogRef.queryKnowledgeBaseInfo({ id: row.id });
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
    },
    // 列表查看按钮
    handleView(row) {
      this.typicalCaseVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "view";
      this.$refs.dialogRef.title = "查看案例";
      this.$refs.dialogRef.queryKnowledgeBaseInfo({ id: row.id });
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
      this.$refs.dialogRef.rules = {};
    },
    // 字典查询
    async queryItemListByDictionaryId(id) {
      let res;
      try {
        res = await emergencyKnowledgeBaseApi.queryItemListByDictionaryId({
          systemDictionaryId: id,
        });
      } catch (error) {}
      const arr = [];

      res?.data.items.forEach((item) => {
        arr.push({
          label: item.itemName,
          value: item.id,
        });
      });
      this.categoryOptions = arr;
      this.searchItems = this.searchItems.map((item) => {
        return {
          ...item,
          options: item.prop === "category" ? arr : [],
        };
      });
    },
    async getEventTypeOptions() {
      const res = await getItemList(eventType);
      this.eventTypeOptions = res.map((item) => ({
        label: item.itemName,
        value: item.itemValue,
      }));
      this.searchItems.find((item) => item.prop === "eventType").options =
        this.eventTypeOptions;
    },
    formatterEventType(row) {
      const eventType = this.eventTypeOptions.find(
        (item) => item.value === row.eventType
      );
      return eventType?.label || row.eventType;
    },
  },
  mounted() {
    this.registerHandlers();
    this.getTypicalCaseList();
    this.getEventTypeOptions();
  },
  created() {
    this.queryItemListByDictionaryId("202507011607");
  },
};
</script>

<style lang="scss" scoped>
.typical-case-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px;
  box-sizing: border-box;
  .typical-case-main {
    flex: 1;
    // display: flex;
    // gap: 8px;
    // padding: 8px;
    // overflow: hidden;
  }
}

.search-wrapper {
  margin-bottom: 8px;

  /* 搜索表单样式优化 */
  ::v-deep .search-form {
    .search-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-row,
      .search-buttons-row {
        margin: 0 !important;
      }
    }
  }
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

  /* 按钮样式优化 */
  ::v-deep .el-button--text {
    &:nth-child(2) {
      color: #b93742 !important;
      &:hover {
        color: #f70a0a !important;
      }
    }
    &:nth-child(1):hover,
    &:nth-child(3):hover {
      color: #0621f1 !important;
    }
  }
}
</style>