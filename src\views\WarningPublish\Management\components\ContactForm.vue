<template>
  <general-dialog :showFooter="isOperation != 3" :dialog-visible="dialogVisible"
    :general-dialog-title="isOperation == 3 ? '查看' : (isOperation == 1 ? '新增' : '编辑')" dialog-width="800px"
    @cancel="handleClose">
    <!-- class="add-form" -->
    <el-form ref="form" class="add-form" :model="form" :rules="rules" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警事项" prop="aa">
            <el-input v-model="form.aa" placeholder="请输入预警事项" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="bb">
            <el-cascader v-model="form.bb" :options="eventList" :props="{ checkStrictly: true,value: 'typeCode' }" clearable size="small"
              :disabled='isOperation == 3'>
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布单位" prop="cc">
            <el-input v-model="form.cc" placeholder="请输入发布单位" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布日期" prop="dd">
            <el-date-picker v-model="form.dd" type="date" value-format="yyyy-MM-dd" placeholder="发布日期" size="small"
              :disabled='isOperation == 3'>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预计持续时间" prop="ee">
            <el-input v-model="form.ee" placeholder="请输入发布单位" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起始日期" prop="ff">
            <el-date-picker v-model="form.ff" type="date" value-format="yyyy-MM-dd" placeholder="起始日期" size="small"
              :disabled='isOperation == 3'>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="影响范围" prop="gg">
            <el-cascader v-model="form.gg" :options="eventList" :props="{ checkStrictly: true,value: 'typeCode' }" clearable size="small"
              :disabled='isOperation == 3'>
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警等级" prop="hh">
            <el-input v-model="form.hh" placeholder="请输入预警等级" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情况说明" prop="ii">
            <el-input v-model="form.ii" type="textarea" maxlength="200" :rows="10" resize="none" placeholder="请输入情况说明"
              size="small" :disabled='isOperation == 3' />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="咨询人" prop="jj">
            <el-input v-model="form.jj" placeholder="请输入咨询人" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="咨询电话" prop="kk">
            <el-input v-model="form.kk" placeholder="请输入咨询电话" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人" prop="ll">
            <el-input v-model="form.ll" placeholder="请输入上报人" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人联系方式" prop="mm">
            <el-input v-model="form.mm" placeholder="请输入上报人联系方式" size="small" :disabled='isOperation == 3'></el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
import { getItemList, textMessageTemplateType } from "@/utils/dictionary";
export default {
  name: "ContactForm",
  components: {
    GeneralDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isOperation: {
      type: Number,
      default: false,
    },
  },
  data() {
    return {
      form: {
        aa: '',
        bb: '',
        cc: '',
        dd: '',
        ee: '',
        ff: '',
        gg: '',
        hh: '',
        ii: '',
        jj: '',
        kk: '',
        ll: '',
        mm: ''
      },
      rules: {
        aa: [
          {
            required: true,
            message: '请输入预警事项',
            trigger: "blur"
          },
        ],
        bb: [
          {
            required: true,
            message: '请选择事件类型',
            trigger: "blur"
          },
        ],
        cc: [
          {
            required: true,
            message: '请输入发布单位',
            trigger: "blur"
          },
        ],
        dd: [
          {
            required: true,
            message: '请选择发布日期',
            trigger: "blur"
          },
        ],
        ee: [
          {
            required: true,
            message: '请输入预计持续时间',
            trigger: "blur"
          },
        ],
        ff: [
          {
            required: true,
            message: '请选择起始日期',
            trigger: "blur"
          },
        ],
        gg: [
          {
            required: true,
            message: '请选择影响范围',
            trigger: "blur"
          },
        ],
        hh: [
          {
            required: true,
            message: '请选择预警级别',
            trigger: "blur"
          },
        ],
        ii: [
          {
            required: true,
            message: '请输入情况说明',
            trigger: "blur"
          },
        ],
        jj: [
          {
            required: true,
            message: '请输入咨询人',
            trigger: "blur"
          },
        ],
        kk: [
          {
            required: true,
            message: '请输入咨询电话',
            trigger: "blur",
            validator: (rule, value, callback) => {
              const mobileReg = /^1[3-9]\d{9}$/;
              const phoneReg = /^(\d{3,4}-)?\d{7,8}$/;
              if (!value) {
                return callback(new Error('请输入联系方式'));
              }
              if (!mobileReg.test(value) && !phoneReg.test(value)) {
                callback(new Error('请输入正确的手机号或电话号码'));
              } else {
                callback();
              }
            },
          },
        ]
      },
      submitting: false,
      eventList: [
        {
          "id": "1",
          "typeCode": "11000",
          "classifyId": null,
          "specialId": null,
          "label": "自然灾害",
          "responsibility": null,
          "children": [
            {
              "id": "6",
              "typeCode": "11A00",
              "classifyId": null,
              "specialId": null,
              "label": "水旱灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "50",
                  "typeCode": "11A01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "洪水",
                  "responsibility": null
                },
                {
                  "id": "51",
                  "typeCode": "11A02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "内涝",
                  "responsibility": null
                },
                {
                  "id": "52",
                  "typeCode": "11A03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "水库重大险情",
                  "responsibility": null
                },
                {
                  "id": "53",
                  "typeCode": "11A04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "堤防重大险情",
                  "responsibility": null
                },
                {
                  "id": "54",
                  "typeCode": "11A05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "凌汛灾害",
                  "responsibility": null
                },
                {
                  "id": "55",
                  "typeCode": "11A55",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农村人畜饮水困难",
                  "responsibility": null
                },
                {
                  "id": "56",
                  "typeCode": "11A51",
                  "classifyId": null,
                  "specialId": null,
                  "label": "山洪灾害事件",
                  "responsibility": null
                },
                {
                  "id": "57",
                  "typeCode": "11A52",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业干旱",
                  "responsibility": null
                },
                {
                  "id": "58",
                  "typeCode": "11A53",
                  "classifyId": null,
                  "specialId": null,
                  "label": "城镇缺水",
                  "responsibility": null
                },
                {
                  "id": "59",
                  "typeCode": "11A54",
                  "classifyId": null,
                  "specialId": null,
                  "label": "生态干旱",
                  "responsibility": null
                },
                {
                  "id": "60",
                  "typeCode": "11A99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其它水旱灾害",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "7",
              "typeCode": "11B00",
              "classifyId": null,
              "specialId": null,
              "label": "气象灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "61",
                  "typeCode": "11B01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "台风",
                  "responsibility": null
                },
                {
                  "id": "62",
                  "typeCode": "11B02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "龙卷风",
                  "responsibility": null
                },
                {
                  "id": "63",
                  "typeCode": "11B03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "暴雨",
                  "responsibility": null
                },
                {
                  "id": "64",
                  "typeCode": "11B04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "雪灾",
                  "responsibility": null
                },
                {
                  "id": "65",
                  "typeCode": "11B05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "寒潮",
                  "responsibility": null
                },
                {
                  "id": "66",
                  "typeCode": "11B06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "大风",
                  "responsibility": null
                },
                {
                  "id": "67",
                  "typeCode": "11B07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "沙尘暴",
                  "responsibility": null
                },
                {
                  "id": "68",
                  "typeCode": "11B08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "低温冻害",
                  "responsibility": null
                },
                {
                  "id": "69",
                  "typeCode": "11B09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "冻雨",
                  "responsibility": null
                },
                {
                  "id": "70",
                  "typeCode": "11B10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "高温天气",
                  "responsibility": null
                },
                {
                  "id": "71",
                  "typeCode": "11B11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "热浪",
                  "responsibility": null
                },
                {
                  "id": "72",
                  "typeCode": "11B12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "干热风",
                  "responsibility": null
                },
                {
                  "id": "73",
                  "typeCode": "11B13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "下击暴流",
                  "responsibility": null
                },
                {
                  "id": "74",
                  "typeCode": "11B14",
                  "classifyId": null,
                  "specialId": null,
                  "label": "雪崩",
                  "responsibility": null
                },
                {
                  "id": "75",
                  "typeCode": "11B15",
                  "classifyId": null,
                  "specialId": null,
                  "label": "雷电",
                  "responsibility": null
                },
                {
                  "id": "76",
                  "typeCode": "11B16",
                  "classifyId": null,
                  "specialId": null,
                  "label": "冰雹",
                  "responsibility": null
                },
                {
                  "id": "77",
                  "typeCode": "11B17",
                  "classifyId": null,
                  "specialId": null,
                  "label": "霜冻",
                  "responsibility": null
                },
                {
                  "id": "78",
                  "typeCode": "11B18",
                  "classifyId": null,
                  "specialId": null,
                  "label": "大雾",
                  "responsibility": null
                },
                {
                  "id": "79",
                  "typeCode": "11B99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其它气象灾害",
                  "responsibility": null
                },
                {
                  "id": "371",
                  "typeCode": "11B19",
                  "classifyId": null,
                  "specialId": null,
                  "label": "灰霾",
                  "responsibility": null
                },
                {
                  "id": "372",
                  "typeCode": "11B20",
                  "classifyId": null,
                  "specialId": null,
                  "label": "低空风切变",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "8",
              "typeCode": "11C00",
              "classifyId": null,
              "specialId": null,
              "label": "地震灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "80",
                  "typeCode": "11C01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "人工地震",
                  "responsibility": null
                },
                {
                  "id": "81",
                  "typeCode": "11C02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "天然地震",
                  "responsibility": null
                },
                {
                  "id": "82",
                  "typeCode": "11C99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其它地震灾害",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "9",
              "typeCode": "11D00",
              "classifyId": null,
              "specialId": null,
              "label": "地质灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "83",
                  "typeCode": "11D01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "滑坡",
                  "responsibility": null
                },
                {
                  "id": "84",
                  "typeCode": "11D02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "泥石流",
                  "responsibility": null
                },
                {
                  "id": "85",
                  "typeCode": "11D03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "崩塌",
                  "responsibility": null
                },
                {
                  "id": "86",
                  "typeCode": "11D04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "塌陷",
                  "responsibility": null
                },
                {
                  "id": "87",
                  "typeCode": "11D05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地裂",
                  "responsibility": null
                },
                {
                  "id": "88",
                  "typeCode": "11D06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地面沉降",
                  "responsibility": null
                },
                {
                  "id": "89",
                  "typeCode": "11D07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "火山喷发",
                  "responsibility": null
                },
                {
                  "id": "90",
                  "typeCode": "11D99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其它地质灾害",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "10",
              "typeCode": "11E00",
              "classifyId": null,
              "specialId": null,
              "label": "海洋灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "91",
                  "typeCode": "11E01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "海啸",
                  "responsibility": null
                },
                {
                  "id": "92",
                  "typeCode": "11E02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "风暴潮",
                  "responsibility": null
                },
                {
                  "id": "93",
                  "typeCode": "11E03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "海冰",
                  "responsibility": null
                },
                {
                  "id": "94",
                  "typeCode": "11E04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "巨浪",
                  "responsibility": null
                },
                {
                  "id": "95",
                  "typeCode": "11E05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "赤潮",
                  "responsibility": null
                },
                {
                  "id": "96",
                  "typeCode": "11E99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他海洋灾害事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "11",
              "typeCode": "11F00",
              "classifyId": null,
              "specialId": null,
              "label": "生物灾害",
              "responsibility": null,
              "children": [
                {
                  "id": "97",
                  "typeCode": "11F01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业病害",
                  "responsibility": null
                },
                {
                  "id": "98",
                  "typeCode": "11F02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业虫害",
                  "responsibility": null
                },
                {
                  "id": "99",
                  "typeCode": "11F03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业草害",
                  "responsibility": null
                },
                {
                  "id": "100",
                  "typeCode": "11F04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业鼠害",
                  "responsibility": null
                },
                {
                  "id": "101",
                  "typeCode": "11F05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "森林病害",
                  "responsibility": null
                },
                {
                  "id": "102",
                  "typeCode": "11F06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "森林虫害",
                  "responsibility": null
                },
                {
                  "id": "103",
                  "typeCode": "11F07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "森林鼠害",
                  "responsibility": null
                },
                {
                  "id": "104",
                  "typeCode": "11F08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业转基因生物安全突发事件",
                  "responsibility": null
                },
                {
                  "id": "105",
                  "typeCode": "11F09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "林业转基因生物安全突发事件",
                  "responsibility": null
                },
                {
                  "id": "106",
                  "typeCode": "11F10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "林业有害植物事件",
                  "responsibility": null
                },
                {
                  "id": "107",
                  "typeCode": "11F11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "外来有害动植物威胁农业生产事件",
                  "responsibility": null
                },
                {
                  "id": "108",
                  "typeCode": "11F12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "外来有害动植物威胁林业生产事件",
                  "responsibility": null
                },
                {
                  "id": "109",
                  "typeCode": "11F99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他生物灾害",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "12",
              "typeCode": "11G00",
              "classifyId": null,
              "specialId": null,
              "label": "森林草原火灾",
              "responsibility": null,
              "children": [
                {
                  "id": "110",
                  "typeCode": "11G01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "境内森林火灾",
                  "responsibility": null
                },
                {
                  "id": "111",
                  "typeCode": "11G02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "跨境森林火灾",
                  "responsibility": null
                },
                {
                  "id": "112",
                  "typeCode": "11G03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "境外威胁我国境内的森林火灾",
                  "responsibility": null
                },
                {
                  "id": "113",
                  "typeCode": "11G04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他森林火灾",
                  "responsibility": null
                },
                {
                  "id": "114",
                  "typeCode": "11G05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "境内草原火灾",
                  "responsibility": null
                },
                {
                  "id": "115",
                  "typeCode": "11G06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "跨境草原火灾",
                  "responsibility": null
                },
                {
                  "id": "116",
                  "typeCode": "11G07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "境外威胁我国境内的草原火灾",
                  "responsibility": null
                },
                {
                  "id": "117",
                  "typeCode": "11G08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其它草原火灾",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "13",
              "typeCode": "11Y00",
              "classifyId": null,
              "specialId": null,
              "label": "其他自然灾害事件",
              "responsibility": null
            }
          ]
        },
        {
          "id": "2",
          "typeCode": "12000",
          "classifyId": null,
          "specialId": null,
          "label": "事故灾难",
          "responsibility": null,
          "children": [
            {
              "id": "14",
              "typeCode": "12A00",
              "classifyId": null,
              "specialId": null,
              "label": "煤矿事故",
              "responsibility": null,
              "children": [
                {
                  "id": "118",
                  "typeCode": "12A01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿瓦斯事故",
                  "responsibility": null
                },
                {
                  "id": "119",
                  "typeCode": "12A02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿顶板事故",
                  "responsibility": null
                },
                {
                  "id": "120",
                  "typeCode": "12A03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿运输事故",
                  "responsibility": null
                },
                {
                  "id": "121",
                  "typeCode": "12A04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿水害事故",
                  "responsibility": null
                },
                {
                  "id": "122",
                  "typeCode": "12A05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿机电事故",
                  "responsibility": null
                },
                {
                  "id": "123",
                  "typeCode": "12A06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿放炮事故",
                  "responsibility": null
                },
                {
                  "id": "124",
                  "typeCode": "12A07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿火灾事故",
                  "responsibility": null
                },
                {
                  "id": "125",
                  "typeCode": "12A99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "煤矿其他事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "15",
              "typeCode": "12B00",
              "classifyId": null,
              "specialId": null,
              "label": "金属与非金属矿山事故",
              "responsibility": null,
              "children": [
                {
                  "id": "126",
                  "typeCode": "12B01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿顶板事故",
                  "responsibility": null
                },
                {
                  "id": "127",
                  "typeCode": "12B02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿水害事故",
                  "responsibility": null
                },
                {
                  "id": "128",
                  "typeCode": "12B03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿中毒和窒息事故",
                  "responsibility": null
                },
                {
                  "id": "129",
                  "typeCode": "12B04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿尾矿库垮坝事故",
                  "responsibility": null
                },
                {
                  "id": "130",
                  "typeCode": "12B05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿火灾事故",
                  "responsibility": null
                },
                {
                  "id": "131",
                  "typeCode": "12B06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿机电事故",
                  "responsibility": null
                },
                {
                  "id": "132",
                  "typeCode": "12B07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿运输事故",
                  "responsibility": null
                },
                {
                  "id": "133",
                  "typeCode": "12B08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿放炮事故",
                  "responsibility": null
                },
                {
                  "id": "134",
                  "typeCode": "12B09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿火药爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "135",
                  "typeCode": "12B99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金属与非金属矿山其他事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "16",
              "typeCode": "12C00",
              "classifyId": null,
              "specialId": null,
              "label": "建筑业事故",
              "responsibility": null,
              "children": [
                {
                  "id": "136",
                  "typeCode": "12C01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "房屋建筑与市政工程施工安全施工",
                  "responsibility": null
                },
                {
                  "id": "137",
                  "typeCode": "12C02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他建筑施工安全事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "17",
              "typeCode": "12E00",
              "classifyId": null,
              "specialId": null,
              "label": "烟花爆竹和民用爆炸物事故",
              "responsibility": null,
              "children": [
                {
                  "id": "145",
                  "typeCode": "12E01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "烟花爆竹生产企业爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "146",
                  "typeCode": "12E02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "烟花爆竹运输爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "147",
                  "typeCode": "12E03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "民用爆炸物爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "148",
                  "typeCode": "12E99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他烟花爆竹事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "18",
              "typeCode": "12F00",
              "classifyId": null,
              "specialId": null,
              "label": "其他工矿商贸事故",
              "responsibility": null
            },
            {
              "id": "19",
              "typeCode": "12G00",
              "classifyId": null,
              "specialId": null,
              "label": "火灾事故",
              "responsibility": null,
              "children": [
                {
                  "id": "149",
                  "typeCode": "12G01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "一般工业建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "150",
                  "typeCode": "12G02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "特种工业建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "151",
                  "typeCode": "12G03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "一般民用建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "152",
                  "typeCode": "12G04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "高层民用建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "153",
                  "typeCode": "12G05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地下建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "154",
                  "typeCode": "12G06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "公用建筑火灾",
                  "responsibility": null
                },
                {
                  "id": "155",
                  "typeCode": "12G07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "隧道火灾",
                  "responsibility": null
                },
                {
                  "id": "156",
                  "typeCode": "12G99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他火灾事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "20",
              "typeCode": "12H00",
              "classifyId": null,
              "specialId": null,
              "label": "道路交通事故",
              "responsibility": null,
              "children": [
                {
                  "id": "157",
                  "typeCode": "12H01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "翻车事件",
                  "responsibility": null
                },
                {
                  "id": "158",
                  "typeCode": "12H02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "撞车事件",
                  "responsibility": null
                },
                {
                  "id": "159",
                  "typeCode": "12H03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "车辆坠水、坠沟事件",
                  "responsibility": null
                },
                {
                  "id": "160",
                  "typeCode": "12H04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "车辆起火事件",
                  "responsibility": null
                },
                {
                  "id": "161",
                  "typeCode": "12H05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "校车交通事件",
                  "responsibility": null
                },
                {
                  "id": "162",
                  "typeCode": "12H99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他道路交通事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "21",
              "typeCode": "12J00",
              "classifyId": null,
              "specialId": null,
              "label": "水上交通事故",
              "responsibility": null,
              "children": [
                {
                  "id": "163",
                  "typeCode": "12J01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶碰撞事故",
                  "responsibility": null
                },
                {
                  "id": "164",
                  "typeCode": "12J02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶触礁事故",
                  "responsibility": null
                },
                {
                  "id": "165",
                  "typeCode": "12J03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶触损事故",
                  "responsibility": null
                },
                {
                  "id": "166",
                  "typeCode": "12J04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶搁浅事故",
                  "responsibility": null
                },
                {
                  "id": "167",
                  "typeCode": "12J05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶遭受风灾事故",
                  "responsibility": null
                },
                {
                  "id": "168",
                  "typeCode": "12J06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶火灾事故",
                  "responsibility": null
                },
                {
                  "id": "169",
                  "typeCode": "12J07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶失踪事故",
                  "responsibility": null
                },
                {
                  "id": "170",
                  "typeCode": "12J08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "船舶海上遇险事故",
                  "responsibility": null
                },
                {
                  "id": "171",
                  "typeCode": "12J09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "水上保安事故",
                  "responsibility": null
                },
                {
                  "id": "172",
                  "typeCode": "12J10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "沿海渔业设施事故",
                  "responsibility": null
                },
                {
                  "id": "173",
                  "typeCode": "12J99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他水上交通事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "22",
              "typeCode": "12K00",
              "classifyId": null,
              "specialId": null,
              "label": "铁路交通事故",
              "responsibility": null,
              "children": [
                {
                  "id": "174",
                  "typeCode": "12K01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "列车脱轨事故",
                  "responsibility": null
                },
                {
                  "id": "175",
                  "typeCode": "12K02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "列车追尾事故",
                  "responsibility": null
                },
                {
                  "id": "176",
                  "typeCode": "12K03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "列车撞车事故",
                  "responsibility": null
                },
                {
                  "id": "177",
                  "typeCode": "12K04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "列车撞人事故",
                  "responsibility": null
                },
                {
                  "id": "178",
                  "typeCode": "12K05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "列车火灾、爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "179",
                  "typeCode": "12K99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他铁路交通事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "23",
              "typeCode": "12L00",
              "classifyId": null,
              "specialId": null,
              "label": "城市轨道交通事故",
              "responsibility": null,
              "children": [
                {
                  "id": "180",
                  "typeCode": "12L01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地铁、轻轨、单轨列车脱轨事故",
                  "responsibility": null
                },
                {
                  "id": "181",
                  "typeCode": "12L02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地铁、轻轨、单轨列车追尾事故",
                  "responsibility": null
                },
                {
                  "id": "182",
                  "typeCode": "12L03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地铁、轻轨、单轨列车撞车事故",
                  "responsibility": null
                },
                {
                  "id": "183",
                  "typeCode": "12L04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地铁、轻轨、单轨列车撞人事故",
                  "responsibility": null
                },
                {
                  "id": "184",
                  "typeCode": "12L05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "地铁、轻轨、单轨列车火灾、爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "185",
                  "typeCode": "12L99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他城市轨道交通事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "24",
              "typeCode": "12M00",
              "classifyId": null,
              "specialId": null,
              "label": "民用航空器飞行事故",
              "responsibility": null,
              "children": [
                {
                  "id": "186",
                  "typeCode": "12M01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "坠机事件",
                  "responsibility": null
                },
                {
                  "id": "187",
                  "typeCode": "12M02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "撞机事件",
                  "responsibility": null
                },
                {
                  "id": "188",
                  "typeCode": "12M99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他民用航空器飞行事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "25",
              "typeCode": "12N00",
              "classifyId": null,
              "specialId": null,
              "label": "特种设备事故",
              "responsibility": null,
              "children": [
                {
                  "id": "189",
                  "typeCode": "12N01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "锅炉事故",
                  "responsibility": null
                },
                {
                  "id": "190",
                  "typeCode": "12N02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "压力容器事故",
                  "responsibility": null
                },
                {
                  "id": "191",
                  "typeCode": "12N03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "压力管道事故",
                  "responsibility": null
                },
                {
                  "id": "192",
                  "typeCode": "12N04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "电梯事故",
                  "responsibility": null
                },
                {
                  "id": "193",
                  "typeCode": "12N05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "起重机械事故",
                  "responsibility": null
                },
                {
                  "id": "194",
                  "typeCode": "12N06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "客运索道事故",
                  "responsibility": null
                },
                {
                  "id": "195",
                  "typeCode": "12N07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "大型游乐设施事故",
                  "responsibility": null
                },
                {
                  "id": "196",
                  "typeCode": "12N99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他特种设备事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "26",
              "typeCode": "12P00",
              "classifyId": null,
              "specialId": null,
              "label": "基础设施和公用设施事故",
              "responsibility": null,
              "children": [
                {
                  "id": "197",
                  "typeCode": "12P01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "公路交通设施事故",
                  "responsibility": null
                },
                {
                  "id": "198",
                  "typeCode": "12P02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "铁路交通设施事故",
                  "responsibility": null
                },
                {
                  "id": "199",
                  "typeCode": "12P03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "城市轨道交通设施事故",
                  "responsibility": null
                },
                {
                  "id": "200",
                  "typeCode": "12P04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "城市桥梁隧道设施事故",
                  "responsibility": null
                },
                {
                  "id": "201",
                  "typeCode": "12P05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "水云交通设施事故",
                  "responsibility": null
                },
                {
                  "id": "202",
                  "typeCode": "12P06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "民航交通设施事故",
                  "responsibility": null
                },
                {
                  "id": "203",
                  "typeCode": "12P07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "水利设施事故",
                  "responsibility": null
                },
                {
                  "id": "204",
                  "typeCode": "12P08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "电力基础设施事故",
                  "responsibility": null
                },
                {
                  "id": "205",
                  "typeCode": "12P09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "石油天然气基础设施事故",
                  "responsibility": null
                },
                {
                  "id": "206",
                  "typeCode": "12P10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "通讯基础设施事故",
                  "responsibility": null
                },
                {
                  "id": "207",
                  "typeCode": "12P11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金融基础设施事故",
                  "responsibility": null
                },
                {
                  "id": "208",
                  "typeCode": "12P12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "城市生命线基础设施事故",
                  "responsibility": null
                },
                {
                  "id": "209",
                  "typeCode": "12P13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "建筑垮塌事故",
                  "responsibility": null
                },
                {
                  "id": "210",
                  "typeCode": "12P99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他公用设施和设备事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "27",
              "typeCode": "12Q00",
              "classifyId": null,
              "specialId": null,
              "label": "环境污染和生态破坏事件",
              "responsibility": null,
              "children": [
                {
                  "id": "211",
                  "typeCode": "12Q01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "水域污染事件",
                  "responsibility": null
                },
                {
                  "id": "212",
                  "typeCode": "12Q02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "空气污染事件",
                  "responsibility": null
                },
                {
                  "id": "213",
                  "typeCode": "12Q03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "土壤污染事件",
                  "responsibility": null
                },
                {
                  "id": "214",
                  "typeCode": "12Q04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "海上溢油事件",
                  "responsibility": null
                },
                {
                  "id": "215",
                  "typeCode": "12Q05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "污染导致城市水源供水中断事故",
                  "responsibility": null
                },
                {
                  "id": "216",
                  "typeCode": "12Q06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "转基因生物生态破坏事件",
                  "responsibility": null
                },
                {
                  "id": "217",
                  "typeCode": "12Q07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "盗伐、滥伐、哄抢森林事件",
                  "responsibility": null
                },
                {
                  "id": "218",
                  "typeCode": "12Q08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "毁林、乱占林地、非法改变林地用途事件",
                  "responsibility": null
                },
                {
                  "id": "219",
                  "typeCode": "12Q09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "濒危物种生存环境遭受环境污染事件",
                  "responsibility": null
                },
                {
                  "id": "220",
                  "typeCode": "12Q10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "野生动（植）物种群大批死亡事件",
                  "responsibility": null
                },
                {
                  "id": "221",
                  "typeCode": "12Q11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "自然保护区、风景名胜区生态破坏",
                  "responsibility": null
                },
                {
                  "id": "222",
                  "typeCode": "12Q12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "进口再生原料污染事件",
                  "responsibility": null
                },
                {
                  "id": "223",
                  "typeCode": "12Q13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "非法倾倒、埋藏剧毒危险废物事件",
                  "responsibility": null
                },
                {
                  "id": "224",
                  "typeCode": "12Q99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他环境污染和生态破坏事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "28",
              "typeCode": "12R00",
              "classifyId": null,
              "specialId": null,
              "label": "农业机械事故",
              "responsibility": null,
              "children": [
                {
                  "id": "225",
                  "typeCode": "12R01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械行驶事故",
                  "responsibility": null
                },
                {
                  "id": "226",
                  "typeCode": "12R02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械作业事故",
                  "responsibility": null
                },
                {
                  "id": "227",
                  "typeCode": "12R03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械碾压事件",
                  "responsibility": null
                },
                {
                  "id": "228",
                  "typeCode": "12R04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械碰撞事件",
                  "responsibility": null
                },
                {
                  "id": "229",
                  "typeCode": "12R05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械翻车事件",
                  "responsibility": null
                },
                {
                  "id": "230",
                  "typeCode": "12R06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械落车事件",
                  "responsibility": null
                },
                {
                  "id": "231",
                  "typeCode": "12R07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农业机械火灾事件",
                  "responsibility": null
                },
                {
                  "id": "232",
                  "typeCode": "12R99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他农业机械事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "29",
              "typeCode": "12S00",
              "classifyId": null,
              "specialId": null,
              "label": "踩踏事件",
              "responsibility": null,
              "children": [
                {
                  "id": "233",
                  "typeCode": "12S01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "公园组织大型群众性活动或聚会踩踏事件",
                  "responsibility": null
                },
                {
                  "id": "234",
                  "typeCode": "12S02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "校园踩踏事件",
                  "responsibility": null
                },
                {
                  "id": "235",
                  "typeCode": "12S99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他踩踏事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "30",
              "typeCode": "12T00",
              "classifyId": null,
              "specialId": null,
              "label": "核与辐射事故",
              "responsibility": null,
              "children": [
                {
                  "id": "236",
                  "typeCode": "12T01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "核设施事故",
                  "responsibility": null
                },
                {
                  "id": "237",
                  "typeCode": "12T02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "放射性物质运输事故",
                  "responsibility": null
                },
                {
                  "id": "238",
                  "typeCode": "12T03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "放射源事故",
                  "responsibility": null
                },
                {
                  "id": "239",
                  "typeCode": "12T99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "射线装置事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "31",
              "typeCode": "12U00",
              "classifyId": null,
              "specialId": null,
              "label": "能源供应中断事故",
              "responsibility": null
            },
            {
              "id": "32",
              "typeCode": "12Y00",
              "classifyId": null,
              "specialId": null,
              "label": "其他事故灾难",
              "responsibility": null
            },
            {
              "id": "138",
              "typeCode": "12D00",
              "classifyId": null,
              "specialId": null,
              "label": "危险化学品事故",
              "responsibility": null,
              "children": [
                {
                  "id": "139",
                  "typeCode": "12D01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品爆炸事故",
                  "responsibility": null
                },
                {
                  "id": "140",
                  "typeCode": "12D02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品泄露事故",
                  "responsibility": null
                },
                {
                  "id": "141",
                  "typeCode": "12D03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品火灾事故",
                  "responsibility": null
                },
                {
                  "id": "142",
                  "typeCode": "12D04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品中毒和窒息事故",
                  "responsibility": null
                },
                {
                  "id": "143",
                  "typeCode": "12D05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品灼烫事故",
                  "responsibility": null
                },
                {
                  "id": "144",
                  "typeCode": "12D99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "危险化学品其他事故",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "375",
              "typeCode": "12V00",
              "classifyId": null,
              "specialId": null,
              "label": "地下管线突发事件",
              "responsibility": null
            },
            {
              "id": "376",
              "typeCode": "12I00",
              "classifyId": null,
              "specialId": null,
              "label": "道路突发事件",
              "responsibility": null
            },
            {
              "id": "377",
              "typeCode": "12O00",
              "classifyId": null,
              "specialId": null,
              "label": "安全事故",
              "responsibility": null
            },
            {
              "id": "378",
              "typeCode": "12W00",
              "classifyId": null,
              "specialId": null,
              "label": "燃气突发事件",
              "responsibility": null
            },
            {
              "id": "379",
              "typeCode": "12X00",
              "classifyId": null,
              "specialId": null,
              "label": "有限空间突发事件",
              "responsibility": null
            }
          ]
        },
        {
          "id": "3",
          "typeCode": "13000",
          "classifyId": null,
          "specialId": null,
          "label": "公共卫生事件",
          "responsibility": null,
          "children": [
            {
              "id": "33",
              "typeCode": "13A00",
              "classifyId": null,
              "specialId": null,
              "label": "传染病事件",
              "responsibility": null,
              "children": [
                {
                  "id": "240",
                  "typeCode": "13A01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "鼠疫流行事件",
                  "responsibility": null
                },
                {
                  "id": "241",
                  "typeCode": "13A02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "霍乱流行事件",
                  "responsibility": null
                },
                {
                  "id": "242",
                  "typeCode": "13A03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "肺炭疽流行事件",
                  "responsibility": null
                },
                {
                  "id": "243",
                  "typeCode": "13A04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "传染性非典型肺炎流行事件",
                  "responsibility": null
                },
                {
                  "id": "244",
                  "typeCode": "13A05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "人感染高致病性禽流感流行事件",
                  "responsibility": null
                },
                {
                  "id": "245",
                  "typeCode": "13A06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他甲类或甲类管理传染病流行事件",
                  "responsibility": null
                },
                {
                  "id": "246",
                  "typeCode": "13A07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他乙类传染病流行事件",
                  "responsibility": null
                },
                {
                  "id": "247",
                  "typeCode": "13A08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "新传染病或我国上未发现的传染病传入事件",
                  "responsibility": null
                },
                {
                  "id": "248",
                  "typeCode": "13A09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "我国已消灭传染病重新流行事件",
                  "responsibility": null
                },
                {
                  "id": "249",
                  "typeCode": "13A99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他传染病事件流行事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "34",
              "typeCode": "13B00",
              "classifyId": null,
              "specialId": null,
              "label": "食品药品安全事件",
              "responsibility": null,
              "children": [
                {
                  "id": "250",
                  "typeCode": "13B01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "药品安全事件",
                  "responsibility": null
                },
                {
                  "id": "251",
                  "typeCode": "13B02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "群体性预防接种反应",
                  "responsibility": null
                },
                {
                  "id": "252",
                  "typeCode": "13B03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "食品安全事件",
                  "responsibility": null
                },
                {
                  "id": "253",
                  "typeCode": "13B04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "农产品质量安全事件",
                  "responsibility": null
                },
                {
                  "id": "254",
                  "typeCode": "13B99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他食品药品安全事件",
                  "responsibility": null
                },
                {
                  "id": "380",
                  "typeCode": "13B05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "疫苗质量安全事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "35",
              "typeCode": "13C00",
              "classifyId": null,
              "specialId": null,
              "label": "群体性中毒、感染事件",
              "responsibility": null,
              "children": [
                {
                  "id": "255",
                  "typeCode": "13C01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "急性职业中毒事件",
                  "responsibility": null
                },
                {
                  "id": "256",
                  "typeCode": "13C02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "重金属中毒事件",
                  "responsibility": null
                },
                {
                  "id": "257",
                  "typeCode": "13C03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "非职业性一氧化碳中毒事件",
                  "responsibility": null
                },
                {
                  "id": "258",
                  "typeCode": "13C99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他群体性中毒感染事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "36",
              "typeCode": "13D00",
              "classifyId": null,
              "specialId": null,
              "label": "病原微生物、菌毒株事件",
              "responsibility": null,
              "children": [
                {
                  "id": "259",
                  "typeCode": "13D01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "菌株、毒株、致病因子丢失事件",
                  "responsibility": null
                },
                {
                  "id": "260",
                  "typeCode": "13D02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "隐匿运输、邮寄病原体、生物毒素",
                  "responsibility": null
                },
                {
                  "id": "261",
                  "typeCode": "13D03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "医源性感染事件",
                  "responsibility": null
                },
                {
                  "id": "262",
                  "typeCode": "13D99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他病原微生物、病毒株事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "37",
              "typeCode": "13E00",
              "classifyId": null,
              "specialId": null,
              "label": "动物疫情事件",
              "responsibility": null,
              "children": [
                {
                  "id": "263",
                  "typeCode": "13E01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "高致病性禽流感",
                  "responsibility": null
                },
                {
                  "id": "264",
                  "typeCode": "13E02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "口蹄疫",
                  "responsibility": null
                },
                {
                  "id": "265",
                  "typeCode": "13E03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "疯牛病",
                  "responsibility": null
                },
                {
                  "id": "266",
                  "typeCode": "13E04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "猪瘟",
                  "responsibility": null
                },
                {
                  "id": "267",
                  "typeCode": "13E05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "新城疫",
                  "responsibility": null
                },
                {
                  "id": "268",
                  "typeCode": "13E06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "蓝舌病",
                  "responsibility": null
                },
                {
                  "id": "269",
                  "typeCode": "13E07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "动物布鲁氏菌病",
                  "responsibility": null
                },
                {
                  "id": "270",
                  "typeCode": "13E08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "动物结核病",
                  "responsibility": null
                },
                {
                  "id": "271",
                  "typeCode": "13E09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "狂犬病",
                  "responsibility": null
                },
                {
                  "id": "272",
                  "typeCode": "13E10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "动物炭疽病",
                  "responsibility": null
                },
                {
                  "id": "273",
                  "typeCode": "13E11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "小反刍兽疫",
                  "responsibility": null
                },
                {
                  "id": "274",
                  "typeCode": "13E12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "我国未发的动物疫病传入事件",
                  "responsibility": null
                },
                {
                  "id": "275",
                  "typeCode": "13E13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "我国已消灭动物疫病重新流行事件",
                  "responsibility": null
                },
                {
                  "id": "276",
                  "typeCode": "13E99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他动物疫情事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "38",
              "typeCode": "13F00",
              "classifyId": null,
              "specialId": null,
              "label": "群体性不明原因疾病",
              "responsibility": null
            },
            {
              "id": "39",
              "typeCode": "13G00",
              "classifyId": null,
              "specialId": null,
              "label": "其他严重影响公共健康和卫生安全事件",
              "responsibility": null
            },
            {
              "id": "40",
              "typeCode": "13Y00",
              "classifyId": null,
              "specialId": null,
              "label": "其他公共卫生事件",
              "responsibility": null
            }
          ]
        },
        {
          "id": "4",
          "typeCode": "14000",
          "classifyId": null,
          "specialId": null,
          "label": "社会安全事件",
          "responsibility": null,
          "children": [
            {
              "id": "41",
              "typeCode": "14A00",
              "classifyId": null,
              "specialId": null,
              "label": "群体性事件",
              "responsibility": null,
              "children": [
                {
                  "id": "277",
                  "typeCode": "14A01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "非法集会游行示威",
                  "responsibility": null
                },
                {
                  "id": "278",
                  "typeCode": "14A02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "集体上访请愿",
                  "responsibility": null
                },
                {
                  "id": "279",
                  "typeCode": "14A03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "冲击、围攻党政军机关和要害部门事件",
                  "responsibility": null
                },
                {
                  "id": "280",
                  "typeCode": "14A04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "大规模打、砸、抢、烧犯罪事件",
                  "responsibility": null
                },
                {
                  "id": "281",
                  "typeCode": "14A05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "群体性械斗、冲突事件",
                  "responsibility": null
                },
                {
                  "id": "282",
                  "typeCode": "14A06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "静坐事件",
                  "responsibility": null
                },
                {
                  "id": "283",
                  "typeCode": "14A07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "罢市、罢工",
                  "responsibility": null
                },
                {
                  "id": "284",
                  "typeCode": "14A08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "罢课",
                  "responsibility": null
                },
                {
                  "id": "285",
                  "typeCode": "14A09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "高校内聚集事件失控",
                  "responsibility": null
                },
                {
                  "id": "286",
                  "typeCode": "14A10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "高校校园网大范围串联、煽动和蛊惑信息事件",
                  "responsibility": null
                },
                {
                  "id": "287",
                  "typeCode": "14A11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "阻断交通事件",
                  "responsibility": null
                },
                {
                  "id": "288",
                  "typeCode": "14A12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "阻扰、妨碍国家重点建设工程施工事件",
                  "responsibility": null
                },
                {
                  "id": "289",
                  "typeCode": "14A13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "暴狱事件",
                  "responsibility": null
                },
                {
                  "id": "290",
                  "typeCode": "14A14",
                  "classifyId": null,
                  "specialId": null,
                  "label": "聚众闹事",
                  "responsibility": null
                },
                {
                  "id": "291",
                  "typeCode": "14A99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他群体事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "42",
              "typeCode": "14B00",
              "classifyId": null,
              "specialId": null,
              "label": "刑事案件",
              "responsibility": null,
              "children": [
                {
                  "id": "292",
                  "typeCode": "14B01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "杀人案件",
                  "responsibility": null
                },
                {
                  "id": "293",
                  "typeCode": "14B02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "爆炸案件",
                  "responsibility": null
                },
                {
                  "id": "294",
                  "typeCode": "14B03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "放火案件",
                  "responsibility": null
                },
                {
                  "id": "295",
                  "typeCode": "14B04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "投放危险物质案件",
                  "responsibility": null
                },
                {
                  "id": "296",
                  "typeCode": "14B05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "以危害方法危害公共安全案件",
                  "responsibility": null
                },
                {
                  "id": "297",
                  "typeCode": "14B06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "绑架案件",
                  "responsibility": null
                },
                {
                  "id": "298",
                  "typeCode": "14B07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "抢劫、盗窃金融机构或运钞车案件",
                  "responsibility": null
                },
                {
                  "id": "299",
                  "typeCode": "14B08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "抢劫、走私、盗窃军（警）用枪械案件",
                  "responsibility": null
                },
                {
                  "id": "300",
                  "typeCode": "14B09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "放射性材料被盗、丢失案件",
                  "responsibility": null
                },
                {
                  "id": "301",
                  "typeCode": "14B10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "炸药、雷管被盗、丢失案件",
                  "responsibility": null
                },
                {
                  "id": "302",
                  "typeCode": "14B11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "走私放射性材料案件",
                  "responsibility": null
                },
                {
                  "id": "303",
                  "typeCode": "14B12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "走私固体废物案件",
                  "responsibility": null
                },
                {
                  "id": "304",
                  "typeCode": "14B13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "制贩毒品案件",
                  "responsibility": null
                },
                {
                  "id": "305",
                  "typeCode": "14B14",
                  "classifyId": null,
                  "specialId": null,
                  "label": "盗窃、出卖、泄露及丢失国家秘密案件",
                  "responsibility": null
                },
                {
                  "id": "306",
                  "typeCode": "14B15",
                  "classifyId": null,
                  "specialId": null,
                  "label": "攻击破坏计算机网络案件",
                  "responsibility": null
                },
                {
                  "id": "307",
                  "typeCode": "14B16",
                  "classifyId": null,
                  "specialId": null,
                  "label": "攻击破坏卫星通信、广播电视传输系统案件",
                  "responsibility": null
                },
                {
                  "id": "308",
                  "typeCode": "14B17",
                  "classifyId": null,
                  "specialId": null,
                  "label": "制售假劣药品、医疗器械案件",
                  "responsibility": null
                },
                {
                  "id": "309",
                  "typeCode": "14B18",
                  "classifyId": null,
                  "specialId": null,
                  "label": "制售不符合卫生标准、有毒有害食品",
                  "responsibility": null
                },
                {
                  "id": "310",
                  "typeCode": "14B19",
                  "classifyId": null,
                  "specialId": null,
                  "label": "走私、骗汇、逃汇、洗钱案件",
                  "responsibility": null
                },
                {
                  "id": "311",
                  "typeCode": "14B20",
                  "classifyId": null,
                  "specialId": null,
                  "label": "金融诈骗案",
                  "responsibility": null
                },
                {
                  "id": "312",
                  "typeCode": "14B21",
                  "classifyId": null,
                  "specialId": null,
                  "label": "增值税发票及其他票证案",
                  "responsibility": null
                },
                {
                  "id": "313",
                  "typeCode": "14B22",
                  "classifyId": null,
                  "specialId": null,
                  "label": "假劣种子、化肥、农药坑农案件",
                  "responsibility": null
                },
                {
                  "id": "314",
                  "typeCode": "14B23",
                  "classifyId": null,
                  "specialId": null,
                  "label": "非法狩猎、采集保护野生动植物案件",
                  "responsibility": null
                },
                {
                  "id": "315",
                  "typeCode": "14B24",
                  "classifyId": null,
                  "specialId": null,
                  "label": "破坏物种资源案件",
                  "responsibility": null
                },
                {
                  "id": "316",
                  "typeCode": "14B25",
                  "classifyId": null,
                  "specialId": null,
                  "label": "偷渡案件",
                  "responsibility": null
                },
                {
                  "id": "317",
                  "typeCode": "14B99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他刑事案件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "43",
              "typeCode": "14C00",
              "classifyId": null,
              "specialId": null,
              "label": "金融突发事件",
              "responsibility": null,
              "children": [
                {
                  "id": "318",
                  "typeCode": "14C01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "银行业金融突发事件",
                  "responsibility": null
                },
                {
                  "id": "319",
                  "typeCode": "14C02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "证券业金融突发事件",
                  "responsibility": null
                },
                {
                  "id": "320",
                  "typeCode": "14C03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "保险业金融突发事件",
                  "responsibility": null
                },
                {
                  "id": "321",
                  "typeCode": "14C04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "外汇类突发事件",
                  "responsibility": null
                },
                {
                  "id": "322",
                  "typeCode": "14C05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "货币发行类突发事件",
                  "responsibility": null
                },
                {
                  "id": "323",
                  "typeCode": "14C06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "支付结算类突发事件",
                  "responsibility": null
                },
                {
                  "id": "324",
                  "typeCode": "14C99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他金融突发事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "44",
              "typeCode": "14D00",
              "classifyId": null,
              "specialId": null,
              "label": "影响市场稳定的突发事件",
              "responsibility": null,
              "children": [
                {
                  "id": "325",
                  "typeCode": "14D01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "粮食市场异常波动",
                  "responsibility": null
                },
                {
                  "id": "326",
                  "typeCode": "14D02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "生活必需品市场异常波动",
                  "responsibility": null
                },
                {
                  "id": "327",
                  "typeCode": "14D99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他影响市场稳定的突发事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "45",
              "typeCode": "14E00",
              "classifyId": null,
              "specialId": null,
              "label": "民族和宗教事件",
              "responsibility": null,
              "children": [
                {
                  "id": "328",
                  "typeCode": "14E01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "民族分裂活动",
                  "responsibility": null
                },
                {
                  "id": "329",
                  "typeCode": "14E02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "宗教大规模非法聚会",
                  "responsibility": null
                },
                {
                  "id": "330",
                  "typeCode": "14E03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "民族冲突事件",
                  "responsibility": null
                },
                {
                  "id": "331",
                  "typeCode": "14E04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "宗教冲突事件",
                  "responsibility": null
                },
                {
                  "id": "332",
                  "typeCode": "14E99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他民族宗教事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "46",
              "typeCode": "14F00",
              "classifyId": null,
              "specialId": null,
              "label": "恐怖袭击事件",
              "responsibility": null,
              "children": [
                {
                  "id": "333",
                  "typeCode": "14F01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击公共聚集场所事件",
                  "responsibility": null
                },
                {
                  "id": "334",
                  "typeCode": "14F02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击党政军首脑机关事件",
                  "responsibility": null
                },
                {
                  "id": "335",
                  "typeCode": "14F03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击城市标志性建筑物事件",
                  "responsibility": null
                },
                {
                  "id": "336",
                  "typeCode": "14F04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击国防设施事件",
                  "responsibility": null
                },
                {
                  "id": "337",
                  "typeCode": "14F05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击宗教场所事件",
                  "responsibility": null
                },
                {
                  "id": "338",
                  "typeCode": "14F06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击外交机构或国际组织事件",
                  "responsibility": null
                },
                {
                  "id": "339",
                  "typeCode": "14F07",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击重要经济目标事件",
                  "responsibility": null
                },
                {
                  "id": "340",
                  "typeCode": "14F08",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击重要基础设施事件",
                  "responsibility": null
                },
                {
                  "id": "341",
                  "typeCode": "14F09",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击城市基础设施事件",
                  "responsibility": null
                },
                {
                  "id": "342",
                  "typeCode": "14F10",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击交通工具事件",
                  "responsibility": null
                },
                {
                  "id": "343",
                  "typeCode": "14F11",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击重要计算机信息网络系统事件",
                  "responsibility": null
                },
                {
                  "id": "344",
                  "typeCode": "14F12",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击通信或新闻中枢事件",
                  "responsibility": null
                },
                {
                  "id": "345",
                  "typeCode": "14F13",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击重要核生化设施事件",
                  "responsibility": null
                },
                {
                  "id": "346",
                  "typeCode": "14F14",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击党政军要员事件",
                  "responsibility": null
                },
                {
                  "id": "347",
                  "typeCode": "14F15",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击外交人员事件",
                  "responsibility": null
                },
                {
                  "id": "348",
                  "typeCode": "14F16",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击平民事件",
                  "responsibility": null
                },
                {
                  "id": "349",
                  "typeCode": "14F17",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击宗教人士事件",
                  "responsibility": null
                },
                {
                  "id": "350",
                  "typeCode": "14F18",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击知名人士事件",
                  "responsibility": null
                },
                {
                  "id": "351",
                  "typeCode": "14F19",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击外国公民事件",
                  "responsibility": null
                },
                {
                  "id": "352",
                  "typeCode": "14F20",
                  "classifyId": null,
                  "specialId": null,
                  "label": "核生化战剂袭击事件",
                  "responsibility": null
                },
                {
                  "id": "353",
                  "typeCode": "14F21",
                  "classifyId": null,
                  "specialId": null,
                  "label": "劫持航空器事件",
                  "responsibility": null
                },
                {
                  "id": "354",
                  "typeCode": "14F22",
                  "classifyId": null,
                  "specialId": null,
                  "label": "劫持船舶事件",
                  "responsibility": null
                },
                {
                  "id": "355",
                  "typeCode": "14F23",
                  "classifyId": null,
                  "specialId": null,
                  "label": "劫持火车事件",
                  "responsibility": null
                },
                {
                  "id": "356",
                  "typeCode": "14F24",
                  "classifyId": null,
                  "specialId": null,
                  "label": "袭击警卫对象、警卫现场事件",
                  "responsibility": null
                },
                {
                  "id": "357",
                  "typeCode": "14F99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他恐怖袭击事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "47",
              "typeCode": "14G00",
              "classifyId": null,
              "specialId": null,
              "label": "涉外事件",
              "responsibility": null,
              "children": [
                {
                  "id": "358",
                  "typeCode": "14G01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "政治类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "359",
                  "typeCode": "14G02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "经济类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "360",
                  "typeCode": "14G03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "灾害事故卫生类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "361",
                  "typeCode": "14G04",
                  "classifyId": null,
                  "specialId": null,
                  "label": "恐怖暴力类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "362",
                  "typeCode": "14G05",
                  "classifyId": null,
                  "specialId": null,
                  "label": "境外敌对势力类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "363",
                  "typeCode": "14G06",
                  "classifyId": null,
                  "specialId": null,
                  "label": "社会安全类涉外事件",
                  "responsibility": null
                },
                {
                  "id": "364",
                  "typeCode": "14G99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他涉外事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "48",
              "typeCode": "14H00",
              "classifyId": null,
              "specialId": null,
              "label": "信息安全事件",
              "responsibility": null,
              "children": [
                {
                  "id": "365",
                  "typeCode": "14H01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "影响公共互联网骨干网的大规模网络病毒传播事件",
                  "responsibility": null
                },
                {
                  "id": "366",
                  "typeCode": "14H02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "针对公共互联网核心设备的网络攻击事件",
                  "responsibility": null
                },
                {
                  "id": "367",
                  "typeCode": "14H03",
                  "classifyId": null,
                  "specialId": null,
                  "label": "针对国家重要信息系统的网络攻击入侵事件",
                  "responsibility": null
                },
                {
                  "id": "368",
                  "typeCode": "14H99",
                  "classifyId": null,
                  "specialId": null,
                  "label": "其他信息安全事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "49",
              "typeCode": "14Y00",
              "classifyId": null,
              "specialId": null,
              "label": "其他社会安全事件",
              "responsibility": null,
              "children": [
                {
                  "id": "369",
                  "typeCode": "14Y01",
                  "classifyId": null,
                  "specialId": null,
                  "label": "行政区划界限纠纷事件",
                  "responsibility": null
                },
                {
                  "id": "370",
                  "typeCode": "14Y02",
                  "classifyId": null,
                  "specialId": null,
                  "label": "行政区划调整和地名变更引发事件",
                  "responsibility": null
                }
              ]
            },
            {
              "id": "386",
              "typeCode": "14I00",
              "classifyId": null,
              "specialId": null,
              "label": "城市供水突发事件",
              "responsibility": null
            },
            {
              "id": "387",
              "typeCode": "14J00",
              "classifyId": null,
              "specialId": null,
              "label": "停电突发事件",
              "responsibility": null
            },
            {
              "id": "388",
              "typeCode": "14K00",
              "classifyId": null,
              "specialId": null,
              "label": "供热突发事件",
              "responsibility": null
            },
            {
              "id": "389",
              "typeCode": "14L00",
              "classifyId": null,
              "specialId": null,
              "label": "排水突发事件",
              "responsibility": null
            }
          ]
        },
        {
          "id": "390",
          "typeCode": "18000",
          "classifyId": null,
          "specialId": null,
          "label": "应急保障类",
          "responsibility": null,
          "children": [
            {
              "id": "392",
              "typeCode": "18200",
              "classifyId": null,
              "specialId": null,
              "label": "通信应急保障",
              "responsibility": null
            },
            {
              "id": "391",
              "typeCode": "18100",
              "classifyId": null,
              "specialId": null,
              "label": "能源供应保障",
              "responsibility": null
            },
            {
              "id": "393",
              "typeCode": "18300",
              "classifyId": null,
              "specialId": null,
              "label": "气象应急保障",
              "responsibility": null
            },
            {
              "id": "395",
              "typeCode": "18500",
              "classifyId": null,
              "specialId": null,
              "label": "生活必需品供应",
              "responsibility": null
            },
            {
              "id": "396",
              "typeCode": "18600",
              "classifyId": null,
              "specialId": null,
              "label": "应急物资保障",
              "responsibility": null
            },
            {
              "id": "397",
              "typeCode": "18700",
              "classifyId": null,
              "specialId": null,
              "label": "交通运输保障",
              "responsibility": null
            },
            {
              "id": "398",
              "typeCode": "18800",
              "classifyId": null,
              "specialId": null,
              "label": "医学救援",
              "responsibility": null
            },
            {
              "id": "401",
              "typeCode": "18400",
              "classifyId": null,
              "specialId": null,
              "label": "应急救助",
              "responsibility": null
            }
          ]
        },
        {
          "id": "5",
          "typeCode": "19000",
          "classifyId": null,
          "specialId": null,
          "label": "其它",
          "responsibility": null,
          "children": [
            {
              "id": "381",
              "typeCode": "19A00",
              "classifyId": null,
              "specialId": null,
              "label": "一氧化碳中毒",
              "responsibility": null
            },
            {
              "id": "382",
              "typeCode": "19B00",
              "classifyId": null,
              "specialId": null,
              "label": "人口聚集活动",
              "responsibility": null
            },
            {
              "id": "383",
              "typeCode": "19C00",
              "classifyId": null,
              "specialId": null,
              "label": "溺亡",
              "responsibility": null
            },
            {
              "id": "384",
              "typeCode": "19D00",
              "classifyId": null,
              "specialId": null,
              "label": "空气重污染事件",
              "responsibility": null
            },
            {
              "id": "385",
              "typeCode": "19Z00",
              "classifyId": null,
              "specialId": null,
              "label": "其他情况",
              "responsibility": null
            }
          ]
        }
      ],//事件类型
      levelList: [],//级别
    };
  },
  mounted() {
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    isEditState: {
      get() {
        return this.isOperation;
      },
      set(val) {
        this.$emit("update:isOperation", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.resetForm();
        })
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    resetForm() {
      this.$nextTick(() => {
        this.form = {
          aa: '',
          bb: '',
          cc: '',
          dd: '',
          ee: '',
          ff: '',
          gg: '',
          hh: '',
          ii: '',
          jj: '',
          kk: '',
          ll: '',
          mm: ''
        }
        this.$refs['form'].resetFields();
      })
    },
    handleClose() {
      this.dialogVisible = false;
    },

  },
};
</script>

<style scoped>
.sort {
  width: 100%;
}

/* .dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
} */
</style>
