<template>
  <div class="el-input-tag">
    <div class="el-tags">
      <el-tag v-for="(tag, index) in modelValue"   :key="tag.name" closable :type="tag.name"
        @close="removeTag( index)">
        {{ tag.name }}
      </el-tag>
      <!-- <span v-for="(tag, index) in modelValue" :key="index" class="el-tag">
        {{ tag.name }}
        <i class="el-icon-close" @click.stop="removeTag(index)"></i>
      </span> -->
      <input ref="inputRef" v-model="inputValue" @keydown.enter.prevent="handleInputEnter" @keydown.tab="handleInputTab"
        @blur="handleInputBlur" @focus="handleInputFocus" :placeholder="placeholder" class="el-input"
        :class="{ 'is-disabled': disabled }" :disabled="disabled" />
    </div>
    <div v-if="errorMessage" class="el-input-tag__error">{{ errorMessage }}</div>
  </div>
</template>

<script>
export default {
  name: 'InputTag',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    maxTags: {
      type: Number,
      default: 0
    },
    validatePattern: {
      type: RegExp,
      default: null
    },
    duplicate: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请输入标签并按回车键添加'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: '',
      errorMessage: ''
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      }
    },
    canAddTag() {
      if (this.disabled) return false;
      if (this.maxTags > 0 && this.modelValue.length >= this.maxTags) {
        this.errorMessage = `最多只能添加 ${this.maxTags} 个标签`;
        return false;
      }
      return true;
    }
  },
  methods: {
    handleInputEnter(event) {
      this.addTag();
    },
    handleInputTab(event) {
      if (this.inputValue.trim()) {
        event.preventDefault();
        this.addTag();
      }
    },
    addTag() {
      const value = this.inputValue.trim();
      if (!value) return;

      if (!this.canAddTag) return;

      if (this.validatePattern && !this.validatePattern.test(value)) {
        this.errorMessage = '标签格式不符合要求';
        return;
      }

      if (!this.duplicate && this.modelValue.includes(value)) {
        this.errorMessage = '标签不能重复';
        return;
      }
      this.errorMessage = '';
      this.modelValue = this.modelValue.concat([{ name: value }])
      this.inputValue = '';
      // this.$emit('add', value);
    },
    removeTag(index) {
      if (this.disabled) return;
      const removedTag = this.modelValue[index];
      this.modelValue = this.modelValue.filter((_, i) => i !== index);
      this.$emit('remove', removedTag);
    },
    handleInputBlur() {
      // 失去焦点时是否添加标签取决于需求
      // this.addTag();
    },
    handleInputFocus() {
      this.errorMessage = '';
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.inputRef.focus();
    });
  }
}
</script>

<style scoped>
.el-input-tag {
  position: relative;
}

.el-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 32px;
  padding: 2px 4px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s;
}

.el-tags:hover {
  border-color: #c0c4cc;
}

.el-tag {
  display: inline-flex;
  align-items: center;
  height: 24px;
  margin: 2px;
  padding: 0 8px;
  /* background-color: #f0f2f5;
  color: #606266; */
  border-radius: 4px;
  font-size: 12px;
}

.el-icon-close {
  margin-left: 4px;
  cursor: pointer;
  font-size: 10px;
  transform: scale(0.9);
}

.el-input {
  flex: 1;
  min-width: 60px;
  height: 24px;
  margin: 2px;
  padding: 0 4px;
  border: none;
  outline: none;
  font-size: 12px;
}

.el-input-tag__error {
  margin-top: 4px;
  color: #f56c6c;
  font-size: 12px;
}

.el-tags.is-disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
}
</style>