<template>
  <general-dialog :dialog-visible="dialogVisible" general-dialog-title="提交报批" dialog-width="800px"
    @cancel="handleClose">
    <!-- class="add-form" -->
    <el-form ref="form" class="add-form" :model="form" :rules="rules" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="呈批单位" prop="aa">
            <el-input v-model="form.aa" placeholder="请输入呈批单位" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="请示名称" prop="bb">
            <el-input v-model="form.bb" placeholder="请输入请示名称" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情况说明" prop="cc">
            <el-input v-model="form.cc" placeholder="请输入情况说明" type="textarea" resize="none" :rows="5"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传附件" prop="dd">
            <el-upload class="upload-demo" drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">只能上传pdf文件，<em>点击上传</em></div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="请示领导" prop="ee">
            <el-select v-model="form.ee" placeholder="请选择请示领导" style="width: 100%" size="small">
              <el-option v-for="item in leadership" :value="item.itemValue" :label="item.itemName"
                :key="item.itemValue">
                {{ item.itemName }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
import { getItemList, textMessageTemplateType } from "@/utils/dictionary";
export default {
  name: "ContactForm",
  components: {
    GeneralDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        aa: '',
        bb: '',
        cc: '',
        dd: '',
        ee: '',
      },
      rules: {
        aa: [
          {
            required: true,
            message: '请输入预警事项',
            trigger: "blur"
          },
        ],
        bb: [
          {
            required: true,
            message: '请选择事件类型',
            trigger: "blur"
          },
        ],
        cc: [
          {
            required: true,
            message: '请输入发布单位',
            trigger: "blur"
          },
        ],
        dd: [
          {
            required: true,
            message: '请选择发布日期',
            trigger: "blur"
          },
        ],
        ee: [
          {
            required: true,
            message: '请输入预计持续时间',
            trigger: "blur"
          },
        ]
      },
      submitting: false,
      leadership: [],//级别
    };
  },
  mounted() {
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    isEditState: {
      get() {
        return this.isEdit;
      },
      set(val) {
        this.$emit("update:isEdit", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.resetForm();
        })
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    resetForm() {
      this.$nextTick(() => {
        this.form = {
          aa: '',
          bb: '',
          cc: '',
          dd: '',
          ee: '',
        }
        this.$refs['form'].resetFields();
      })
    },
    handleClose() {
      this.dialogVisible = false;
    },

  },
};
</script>

<style scoped>
.sort {
  width: 100%;
}

/* .dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
} */
</style>
