<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :dialog-width="'560px'"
    :general-dialog-title="dialogTitle"
    :show-footer="dialogType !== 'detail'"
    :set-component-name="$store.getters.componentName"
    @cancel="handleCancel"
    @confirm="handleSubmit"
  >
    <el-form
      ref="form"
      :model="form"
      class="add-form"
      :rules="rules"
      label-position="top"
      label-width="100px"
    >
      <el-form-item label="细则名称" prop="name">
        <el-input v-model="form.name" autocomplete="off" />
      </el-form-item>
      <!-- <el-form-item label="部门" prop="deptId">
        <el-input v-model="form.deptId" autocomplete="off" />
      </el-form-item> -->
      <el-form-item label="总分值" prop="score">
        <el-input v-model="form.score" autocomplete="off" />
      </el-form-item>
      <el-form-item label="评分方式" prop="scoreManner">
        <el-select v-model="form.scoreManner" placeholder="请选择评分方式">
          <el-option
            v-for="item in scoreMannerList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="type">
        <el-input v-model="form.type" autocomplete="off" />
      </el-form-item>
      <el-form-item label="评分标准" prop="criteria">
        <el-input v-model="form.criteria" autocomplete="off" />
      </el-form-item>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  components: {
    GeneralDialog,
  },
  data() {
    return {
      dialogType: "",
      dialogVisible: false,
      dialogTitle: "",
      scoreMannerList: [
        {
          label: "自动评分",
          value: "自评",
        },
        {
          label: "人工评分",
          value: "人工",
        },
        {
          label: "混合评分",
          value: "混合",
        },
      ],
      form: {
        name: "",
        criteria: "",
        deptId: "",
        score: "",
        scoreManner: "",
        type: "",
        criteriaId: "",
      },
      rules: {
        name: [{ required: true, message: "请输入细则名称", trigger: "blur" }],
        criteria: [
          { required: true, message: "请输入评分标准", trigger: "blur" },
        ],
        deptId: [{ required: true, message: "请输入部门", trigger: "blur" }],
        score: [
          { required: true, message: "请输入总分值", trigger: "blur" },
          { pattern: /^\d+$/, message: "只能输入数字", trigger: "blur" },
        ],
        scoreManner: [
          { required: true, message: "请输入评分方式", trigger: "blur" },
        ],
        type: [{ required: true, message: "请输入分类", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType === "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          comprehensiveAssessmentApi.createCriteria(this.form).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.$emit("refreshTableData");
            }
          });
        }
      });
    },
    editSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          comprehensiveAssessmentApi.updateCriteria(this.form).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "编辑成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.$emit("refreshTableData");
            }
          });
        }
      });
    },
    resetForm() {
      Object.assign(this.$data, this.$options.data());
    },
  },
};
</script>

<style>
</style>