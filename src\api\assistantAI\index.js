import request from "@/utils/request";
export default class assistantAI {

    //临时通知添加接报信息
    static linShiCreateReportInfo(data) {
        return request({
            url: "/ds/infoAggregation/linShiCreateReportInfo",
            method: "post",
            data,
        });
    }

    //添加接报信息
    static createReportInfo(data) {
        return request({
            url: "/ds/infoAggregation/createReportInfo",
            method: "post",
            data,
        });
    }

    //获取发送内容
    static querySendMessage(data) {
        return request({
            url: "/ds/infoAggregation/querySendMessage",
            method: "post",
            data,
        });
    }

    //信息同步
    static synchronization(data) {
        return request({
            url: "/ds/infoAggregation/synchronization",
            method: "post",
            data,
        });
    }
}
