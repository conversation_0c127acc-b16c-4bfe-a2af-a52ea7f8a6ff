<template>
    <general-dialog :dialog-visible="dialogVisible" :general-dialog-title="'关联事件'" dialog-width="800px"
        @confirm="handleSubmit" @cancel="handleClose">
        <el-form class="add-form" ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="短信主题">{{ form.theme }}</el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送单位"> {{ form.editor }} </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送人"> {{ form.reviewer }} </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送时间"> {{ form.planSendTime }} </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="短信内容"> {{ form.content }} </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="关联事件" prop="eventId">
                        <el-select v-model="form.eventId" clearable placeholder="请选择关联事件">
                            <el-option v-for="item in eventList" :value="item.id" :label="item.caption" :key="item.id">
                                {{ item.caption }}
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </general-dialog>

</template>

<script>
import { textMessageManagementApi } from '@/api/index.js'
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
    name: '',
    components: {
        GeneralDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        formData: {
            type: Object,
            default: () => ({}),
        },
    },
    watch: {
        formData: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.form = { ...this.form, ...val };
                }
            },
            deep: true,
            immediate: true,
        },
    },
    data() {
        return {
            form: {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                eventId: "",
            },
            rules: {
                eventId: [
                    {
                        required: true,
                        message: ' ',
                    },
                ],

            },
            eventList: [
                {
                    "searchValue": null,
                    "createBy": "somnus",
                    "createTime": "2025-05-13 09:34:41",
                    "updateBy": "somnus",
                    "updateTime": "2025-05-13 09:34:47",
                    "remark": null,
                    "delFlag": null,
                    "mapType": null,
                    "longitude": 116.29077720731276,
                    "latitude": 39.807031799261736,
                    "params": {},
                    "id": "b1a4f08c-410b-47e1-94d9-564ed45bebf4",
                    "planId": null,
                    "eventNumber": "2025|0019",
                    "caption": "新增轨道交通突发事件",
                    "content": "<p>突发轨道交通事故</p>",
                    "eventTime": "2025-05-13 00:00",
                    "queryTime": null,
                    "startEventTime": null,
                    "endEventTime": null,
                    "incidentSite": "北京市_110000,丰台区_110106,花乡街道_110106018",
                    "siteCode": null,
                    "siteDetail": "北京市丰台区花乡街道丰葆路268号",
                    "lcsLongitude": null,
                    "lcsLatitude": null,
                    "cgcs2000Longitude": 116.29077720731276,
                    "cgcs2000Latitude": 39.807031799261736,
                    "areaCode": "110106018",
                    "areaCodeName": null,
                    "primaryDept": null,
                    "unitCode": null,
                    "sysCode": 81,
                    "eventType": "12L00",
                    "effect": null,
                    "eventName": null,
                    "eventGrade": "24",
                    "eventGradeName": "一般",
                    "eventState": null,
                    "editor": null,
                    "proofreader": null,
                    "signer": null,
                    "editorName": null,
                    "proofreaderName": null,
                    "signerName": null,
                    "initialUnit": null,
                    "state": "2",
                    "openState": "1",
                    "openStateStr": "处置中",
                    "readState": null,
                    "send": null,
                    "submission": null,
                    "classifyId": null,
                    "specialId": null,
                    "parentId": null,
                    "receiveId": null,
                    "initialId": null,
                    "originFile": "\\eventInfo\\20250513095635eventInfo-flag.pdf",
                    "wordOriginFile": "\\eventInfo\\20250513095635eventInfo-flag.docx",
                    "accessoryList": null,
                    "fileUrl": null,
                    "originId": null,
                    "systemId": null,
                    "dataFrom": "PCVSM",
                    "incidentSiteStr": null,
                    "verificationCode": "9524",
                    "eventElement": {
                        "searchValue": null,
                        "createBy": null,
                        "createTime": null,
                        "updateBy": null,
                        "updateTime": null,
                        "remark": null,
                        "delFlag": null,
                        "mapType": null,
                        "longitude": null,
                        "latitude": null,
                        "params": {},
                        "id": null,
                        "weather": null,
                        "temperature": null,
                        "pneumatic": null,
                        "windPower": null,
                        "direction": null,
                        "humidity": null,
                        "deathNum": null,
                        "injuredNum": null,
                        "seriousNum": null,
                        "minorNum": null,
                        "disappearNum": null,
                        "trappedNum": null,
                        "directEconomyLost": 0,
                        "indirectEconomyLost": 0,
                        "eventId": null,
                        "initialId": null,
                        "type": null
                    },
                    "factorsList": [],
                    "eventFactorsList": [],
                    "bulletionCopyList": [],
                    "bulletionGiversList": [],
                    "eventTypeName": "城市轨道交通事故",
                    "continueShow": null,
                    "type": "30",
                    "icon": "http://113.200.212.27:9527/uploadPathnull",
                    "townLinkmanList": null,
                    "countyLinkmanList": null,
                    "street": null,
                    "incidentSiteWeather": null,
                    "fireSituation": null,
                    "name": "新增轨道交通突发事件",
                    "typeCodes": [
                        "b1a4f08c-410b-47e1-94d9-564ed45bebf4"
                    ],
                    "screenStatus": "1",
                    "searchDate": null,
                    "plottingId": null,
                    "fieldCommandSystemList": null,
                    "municipalCommandPost": null,
                    "districtCommandPost": null,
                    "fieldCommandSystem": null,
                    "eventNumberStr": "19"
                },
                {
                    "searchValue": null,
                    "createBy": "cs",
                    "createTime": "2025-04-22 14:08:54",
                    "updateBy": "yangyujing",
                    "updateTime": "2025-05-12 10:23:35",
                    "remark": null,
                    "delFlag": null,
                    "mapType": null,
                    "longitude": 116.20668318866893,
                    "latitude": 39.90750033457017,
                    "params": {},
                    "id": "0905c8b7-9f19-414d-817e-0a7c4c990298",
                    "planId": null,
                    "eventNumber": "2025|0018",
                    "caption": "1600年前古墓壁画遭网友群嘲",
                    "content": "<p><span style=\"color: rgb(51, 51, 51);\">2019年，英国考古界迎来震撼发现：考古学家于英格兰西南部博克斯福特村，发掘出一幅距今</span><span style=\"color: rgb(247, 49, 49);\">1600年</span><span style=\"color: rgb(51, 51, 51);\">的古罗马马赛克</span><span style=\"color: rgb(247, 49, 49);\">壁画</span><span style=\"color: rgb(51, 51, 51);\">。尤为惊奇的是，于这幅珍稀壁画之侧，考古人员意外发掘出一...</span></p>",
                    "eventTime": "2025-04-22 14:29",
                    "queryTime": null,
                    "startEventTime": null,
                    "endEventTime": null,
                    "incidentSite": "北京市_110000,海淀区_110108",
                    "siteCode": null,
                    "siteDetail": "北京市石景山区八角街道石景山路石景山游乐园",
                    "lcsLongitude": null,
                    "lcsLatitude": null,
                    "cgcs2000Longitude": 116.20668318866893,
                    "cgcs2000Latitude": 39.90750033457017,
                    "areaCode": "110108",
                    "areaCodeName": null,
                    "primaryDept": null,
                    "unitCode": null,
                    "sysCode": 81,
                    "eventType": "11000",
                    "effect": "2019年，英国考古界迎来震撼发现：考古学家于英格兰西南部博克斯福特村，发掘出一幅距今1600年的古罗马马赛克壁画。尤为惊奇的是，于这幅珍稀壁画之侧，考古人员意外发掘出一...",
                    "eventName": null,
                    "eventGrade": "21",
                    "eventGradeName": "特别重大",
                    "eventState": null,
                    "editor": null,
                    "proofreader": null,
                    "signer": null,
                    "editorName": null,
                    "proofreaderName": null,
                    "signerName": null,
                    "initialUnit": null,
                    "state": "2",
                    "openState": "2",
                    "openStateStr": "已处置",
                    "readState": null,
                    "send": null,
                    "submission": null,
                    "classifyId": null,
                    "specialId": null,
                    "parentId": null,
                    "receiveId": null,
                    "initialId": null,
                    "originFile": "\\eventInfo\\20250422143025eventInfo-flag.pdf",
                    "wordOriginFile": "\\eventInfo\\20250422143025eventInfo-flag.docx",
                    "accessoryList": null,
                    "fileUrl": null,
                    "originId": null,
                    "systemId": null,
                    "dataFrom": "PCVSM",
                    "incidentSiteStr": null,
                    "verificationCode": null,
                    "eventElement": {
                        "searchValue": null,
                        "createBy": null,
                        "createTime": null,
                        "updateBy": null,
                        "updateTime": null,
                        "remark": null,
                        "delFlag": null,
                        "mapType": null,
                        "longitude": null,
                        "latitude": null,
                        "params": {},
                        "id": null,
                        "weather": null,
                        "temperature": null,
                        "pneumatic": null,
                        "windPower": null,
                        "direction": null,
                        "humidity": null,
                        "deathNum": null,
                        "injuredNum": null,
                        "seriousNum": null,
                        "minorNum": null,
                        "disappearNum": null,
                        "trappedNum": null,
                        "directEconomyLost": 0,
                        "indirectEconomyLost": 0,
                        "eventId": null,
                        "initialId": null,
                        "type": null
                    },
                    "factorsList": [],
                    "eventFactorsList": [],
                    "bulletionCopyList": [],
                    "bulletionGiversList": [],
                    "eventTypeName": "自然灾害",
                    "continueShow": null,
                    "type": "30",
                    "icon": "http://113.200.212.27:9527/uploadPathnull",
                    "townLinkmanList": null,
                    "countyLinkmanList": null,
                    "street": null,
                    "incidentSiteWeather": null,
                    "fireSituation": null,
                    "name": "1600年前古墓壁画遭网友群嘲",
                    "typeCodes": [
                        "0905c8b7-9f19-414d-817e-0a7c4c990298"
                    ],
                    "screenStatus": "0",
                    "searchDate": null,
                    "plottingId": null,
                    "fieldCommandSystemList": null,
                    "municipalCommandPost": null,
                    "districtCommandPost": null,
                    "fieldCommandSystem": null,
                    "eventNumberStr": "18"
                },
            ]
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
    },
    methods: {
        resetForm() {
            this.form = {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                eventId: "",
            }
            this.$refs['form'].resetFields();
        },
        // 保存
        handleSubmit() {
            this.$refs['form'].validate(async (valid) => {
                if (valid) {
                    try {
                        const formData = { ...this.form, ...this.formData };
                        let data = {
                            taskId: formData.taskId,
                            eventId: formData.eventId
                        }
                        let response = await textMessageManagementApi.associatedEvent(data)
                        if (response.code == 0) {
                            this.resetForm();
                            this.$emit("submit");
                            this.$message.success('关联事件成功')
                        }
                    } finally {
                        this.submitting = false;
                    }
                } else {

                    return false;
                }
            });
        },
        // 关闭
        handleClose() {
            this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.dialogVisible = false;
                    this.resetForm();
                })
                .catch(() => {
                    // 取消关闭
                })
        }
    },
    mounted() {
    }
}
</script>

<style scoped></style>