<template>
  <div class="add-uers-dialog-container" v-if="addUersDialogVisible">
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <portal-table
        style="width: 100%"
        :tableHeight="400"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="id"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @handle-selection-change="handleSelectionChange"
      />
    </general-dialog>
  </div>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import { systemManagementApi } from "@/api";
export default {
  name: "AddUersDialog",
  data() {
    return {
      addUersDialogVisible: false,
      dialogVisible: false,
      dialogWidth: "750px",
      generalDialogTitle: "添加人员",
      tableData: [],
      columns: [
        {
          text: true,
          prop: "userName",
          label: "用户名称",
        },
        {
          text: true,
          prop: "orgName",
          label: "所属机构",
        },
        {
          text: true,
          prop: "phone",
          label: "联系电话(手机)",
        },
        { text: true, prop: "phoneTel", label: "联系电话(座机)" },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      searchItems: [
        {
          prop: "userName",
          label: "用户名称",
          type: "input",
          placeholder: "请输入用户名称",
          width: "700px",
        },
      ],
      form: {
        groupName: "",
        id: "",
        ids: [],
      },
      rules: {
        userName: [
          { required: true, message: "请输入用户名称", trigger: "blur" },
        ],
      },
      searchKeyword: "",
      selectedRows: new Map(),
    };
  },
  components: {
    GeneralDialog,
    PortalTable,
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      systemManagementApi.addUsers(this.form).then((res) => {
        this.dialogVisible = false;
        this.addUersDialogVisible = false;
        this.$message({
          type: "success",
          message: "添加成功",
        });
      });
    },
    handleSearch(data) {
      this.pagination.currentPage = 1;
      this.searchKeyword = data.userName || "";
      this.queryAddressBookList();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.queryAddressBookList();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.queryAddressBookList();
    },
    handleSelectionChange(val) {
      console.log(val, "val");

      this.form.ids = val.map((item) => item.id);
    },
    // 查询人员(用户)列表
    queryAddressBookList() {
      try {
        systemManagementApi
          .queryAddressBookList({
            page: this.pagination.currentPage,
            count: this.pagination.pageSize,
            userName: this.searchKeyword,
          })
          .then((res) => {
            this.tableData = res?.data.items || [];
            this.pagination.total = res.data.total;
          });
      } catch (error) {
        console.log(error, "error");
      }
    },
  },
  mounted() {
    this.queryAddressBookList();
  },
};
</script>

<style lang="scss" scoped>
.add-uers-dialog-container {
  height: 100%;
}
::v-deep .portal-table {
  width: 700px !important;
  margin: 100px 30px 0 30px;
}
</style>