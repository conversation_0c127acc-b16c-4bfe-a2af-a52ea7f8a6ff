@import "./variables.scss";
// @import './mixin.scss';
// @import './transition.scss';
// @import "./element-ui.scss";
// @import './sidebar.scss';
// @import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  background: #f4f9fd;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  -webkit-filter: var(--decolorization);
  -moz-filter: var(--decolorization);
  -ms-filter: var(--decolorization);
  -o-filter: var(--decolorization);
  filter: var(--decolorization);
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.pending-icon {
  display: inline-block;
  width: 30px;
  height: 20px;
  background-image: url("@/assets/images/alarm.gif");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 2px;
  margin-left: 6px;
  vertical-align: middle;
  position: relative;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.blueColor {
  color: #457be2;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    color: #0765fc;
  }
}

.yellowColor {
  color: #726d00;
  cursor: pointer;

  &:hover {
    color: #726d00;
  }
}

.shenpiColor {
  color: #004360;
  cursor: pointer;

  &:hover {
    color: #004360;
  }
}

.redText {
  color: #f31111;
}

.redColor {
  color: #f31111;
  cursor: pointer;
}

.title {
  font-size: 15px;
  font-weight: bold;
}

.title-text {
  float: right;
  font-size: 14px;
  font-weight: 500;
}

.table-box {
  padding: 20px;
}

.show-pwd {
  position: absolute;
  right: 70px;
  top: 2px;
  font-size: 16px;
  color: #889aa4;
  cursor: pointer;
  user-select: none;
}

.pagination-box {
  float: right;
  margin: 10px 0 10px 0;
}

.checkTitle {
  font-weight: 600;
  font-size: 16px;
}

.el-table--enable-row-transition .el-table__body td {
  background-color: ease;
}

// 添加编辑表单样式
.add-form {
  padding: 30px 60px 5px 60px;

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  .el-select {
    width: 100%;
  }
}

//出现省略号
.ellipsis-customize-one {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

//出现省略号
.ellipsis-customize {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 显示行数 :ml-search[-webkit-line-clamp] */
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
