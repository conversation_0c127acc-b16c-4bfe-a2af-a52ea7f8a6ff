import { warningPublish<PERSON>pi } from "@/api";
// 全局混入
export default {
    data() {
        return {
            // 短信模版列表
            contactList: [],
            // 分页信息
            pagination: {
                current: 1,
                pageSize: 10,
                total: 0,
            },
            // 搜索条件
            searchParams: {},
            // 加载状态
            loading: false,
        }
    },
    methods: {
        SET_CONTACT_LIST(list) {
            this.contactList = list || [];
        },
        SET_PAGINATION(pagination) {
            this.pagination = { ...this.pagination, ...pagination };
        },
        SET_SEARCH_PARAMS(params) {
            this.searchParams = params;
        },
        SET_LOADING(loading) {
            this.loading = loading;
        },
        // 获取预警接入列表列表
        async getContactList(params = {}) {
            this.SET_LOADING(true)
            try {
                const requestParams = {
                    pageNum: this.pagination.current,
                    pageSize: this.pagination.pageSize,
                    ...this.searchParams,
                    ...params,
                };

                const response = await warningPublishApi.getWarningPublishList(requestParams);
                this.SET_CONTACT_LIST(response.data.items)
                this.SET_PAGINATION({
                    total: response.data.total,
                    current: response.data.page,
                })
                this.SET_LOADING(false)
                return (
                    response?.data || {
                        list: this.contactList,
                        total: this.pagination.total,
                    }
                );
            } catch (error) {
                this.SET_LOADING(false)
                throw error;
            }
        },
        // 搜索
        async seachContactList(params = {}) {
            this.SET_SEARCH_PARAMS(params)
        },
        // 新增预警接入
        async createTextMessageTemplate(params = {}) {
            try {
                const requestParams = {
                    ...params,
                };
                await warningPublishApi.createWarningAccess(requestParams);

            } catch (error) {

                throw error;
            }
        },
        // 更新预警接入
        async updateTextMessageTemplate(params = {}) {
            try {
                const requestParams = {
                    ...params,
                };
                await warningPublishApi.updateWarningAccess(requestParams);

            } catch (error) {

                throw error;
            }
        },
        //删除预警接入
        async removeTextMessageTemplate(params = {}) {
            try {
                const requestParams = {
                    id: params.templateId
                };
                await warningPublishApi.removeWarningAccess(requestParams);

            } catch (error) {

                throw error;
            }
        },
        // 获取预警接入详情
        async queryTextMessageTemplateById(params = {}) {
            try {
                const requestParams = {
                    id: params.templateId
                };
                const response = await warningPublishApi.queryWarningAccessById(requestParams);
                return (
                    {
                        ...response.data
                    }
                );

            } catch (error) {

                throw error;
            }
        },
        // 分页
        async setCurrentChange(params = {}) {
            try {
                this.SET_PAGINATION(params)
            } catch (error) {
                throw error;
            }
        },
    },
}




