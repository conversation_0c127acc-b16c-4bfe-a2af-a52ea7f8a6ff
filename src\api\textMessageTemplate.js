/**
 * 短信模版相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";

export default {
  // 获取短信模板分页列表
  async getTextMessageTemplateList(params = {}) {
    return request({
      url: '/ds/smsTemplate/queryList',
      method: 'get',
      params: params
    })
  },
  // 不分页
  async getTextMessageTemplateListAll(params = {}) {
    return request({
      url: '/ds/smsTemplate/queryAll',
      method: 'get',
      params: params
    })
  },
  // 短信模板搜索
  async searchTextMessageTemplate(params = {}) {
    // 搜索功能，复用getList的逻辑
    return this.getList({ ...params });
  },
  // 新增短信模板
  async createTextMessageTemplate(params = {}) {
    return request({
      url: '/ds/smsTemplate/create',
      method: 'post',
      data: params
    })
  },
  // 更新短信模板
  async updateTextMessageTemplate(params = {}) {
    return request({
      url: '/ds/smsTemplate/update',
      method: 'post',
      data: params
    })
  },
    // 获取短信模板详情
  async queryTextMessageTemplateById(params = {}) {
    return request({
      url: '/ds/smsTemplate/queryById',
      method: 'post',
      data: params
    })
  },
  // 删除短信模板
  async removeTextMessageTemplate(params = {}) {
    return request({
      url: '/ds/smsTemplate/deleteById',
      method: 'post',
      data: params
    })
  },
};
