<template>
  <div class="leadership-directives-container">
    <!-- 领导批示 - Page -->
    <portal-table
      ref="portalTableRef"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
      @add="handleAdd"
    />

    <!-- 详情查看对话框 -->
    <leadership-directives-detail
      :dialog-visible="detailDialogVisible"
      :detail-id="currentDetailId"
      :detail-title="currentDetailTitle"
      @close="handleDetailClose"
    />

    <!-- 新增对话框 -->
    <leadership-directives-add-dialog
      :dialog-visible="addDialogVisible"
      @close="handleAddDialogClose"
      @success="handleAddSuccess"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import LeadershipDirectivesDetail from "./components/index.vue";
import LeadershipDirectivesAddDialog from "./components/AddDialog.vue";
import { leadershipDirectivesApi } from "@/api";

export default {
  name: "LeadershipDirectives",
  components: {
    PortalTable,
    LeadershipDirectivesDetail,
    LeadershipDirectivesAddDialog,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [], // 存储选中的行
      currentSearchParams: {}, // 存储当前搜索参数
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 详情查看相关
      detailDialogVisible: false,
      currentDetailId: null,
      currentDetailTitle: "",

      // 新增对话框相关
      addDialogVisible: false,

      searchItems: [
        {
          type: "input",
          prop: "infoTitle",
          label: "事件标题",
          placeholder: "请输入事件标题",
        },
        {
          type: "startEndPicker",
          prop: "directiveTime",
          label: "批示时间",
          placeholder: "请选择批示时间范围",
        },
        {
          type: "select",
          prop: "status",
          label: "状态",
          placeholder: "请选择状态",
          options: [
            { label: "全部", value: "" },
            { label: "待批示", value: "0" },
            { label: "待下发", value: "1" },
            { label: "待反馈", value: "2" },
            { label: "已反馈", value: "3" },
          ],
        },
      ],
      columns: [
        {
          prop: "eventTitle",
          label: "事件标题",
          text: true,
        },
        {
          prop: "directiveContent",
          label: "批示信息",
          text: true,
        },
        {
          prop: "directiveTime",
          label: "批示时间",
          text: true,
        },
        {
          prop: "issueTime",
          label: "下发时间",
          text: true,
        },
        {
          prop: "feedbackTime",
          label: "反馈时间",
          text: true,
        },
        {
          prop: "directiveUnit",
          label: "批示单位",
          text: true,
        },
        {
          prop: "directiveLeader",
          label: "批示领导",
          text: true,
        },
        {
          prop: "statusText",
          label: "状态",
          text: true,
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "详情",
              permission: "leadership:directives:view",
              buttonClick: this.handleView,
              isShow: () => {
                return true;
              },
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.fetchData();

    this.$store.commit("generalEvent/registerEventHandler", {
      type: "export_top",
      handler: this.handleExport,
    });

    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: this.handleAdd,
    });
  },
  beforeDestroy() {
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "export_top",
      handler: null,
    });

    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: null,
    });
  },
  methods: {
    async fetchData(searchParams = {}) {
      this.loading = true;
      try {
        // 构建API请求参数
        const params = {
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          ...searchParams,
        };

        // 处理时间范围参数
        if (
          searchParams.directiveTime &&
          searchParams.directiveTime.length === 2
        ) {
          params.startTime = searchParams.directiveTime[0];
          params.endTime = searchParams.directiveTime[1];
          delete params.directiveTime;
        }

        const response = await leadershipDirectivesApi.queryLeadershipPage(
          params
        );

        if (response.code === 0 && response.data) {
          // 处理响应数据，映射字段名
          const processedData =
            response.data.items?.map((item) => ({
              id: item.id,
              eventTitle: item.infoTitle, // API字段映射
              directiveContent: item.approvalInfo, // API字段映射
              directiveTime: item.approvalTime, // API字段映射
              issueTime: item.sendTime, // API字段映射
              feedbackTime: item.feedbackTime, // API字段映射
              directiveUnit: item.orgName, // API字段映射
              directiveLeader: item.userName, // API字段映射
              status: item.status,
              statusText: item.status, // API已返回中文状态，直接使用
              // 保留原始数据以备后用
              ...item,
            })) || [];

          this.tableData = processedData;
          this.pagination.total = response.data.total || 0;
        } else {
          throw new Error(response.message || "数据获取失败");
        }
      } catch (error) {
        console.error("API调用失败:", error);
        this.handleQueryError();
      } finally {
        this.loading = false;
      }
    },

    handleQueryError() {
      this.$message.error("数据加载失败");
      this.tableData = [];
      this.pagination.total = 0;
    },

    handleView(row) {
      this.currentDetailId = row.id;
      this.currentDetailTitle = row.eventTitle;
      this.detailDialogVisible = true;
    },

    handleSearch(searchData) {
      this.currentSearchParams = searchData; // 保存搜索参数
      this.pagination.currentPage = 1;
      this.fetchData(searchData);
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    handleSelectionChange(selection) {
      // 存储选中的行
      this.selectedRows = selection;
    },

    async handleExport() {
      try {
        // 添加加载状态
        const loading = this.$loading({
          lock: true,
          text: "正在导出数据...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });

        // 构建导出参数
        const exportParams = {
          ...this.currentSearchParams,
        };

        // 处理时间范围参数
        if (
          exportParams.directiveTime &&
          exportParams.directiveTime.length === 2
        ) {
          exportParams.startTime = exportParams.directiveTime[0];
          exportParams.endTime = exportParams.directiveTime[1];
          delete exportParams.directiveTime;
        }

        const response = await leadershipDirectivesApi.exportList(exportParams);

        // 创建下载链接
        const blob = new Blob([response], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `领导批示数据_${new Date()
          .toISOString()
          .slice(0, 10)}.xlsx`;

        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);

        this.$message.success("导出成功！");

        loading.close();
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败，请重试");
      }
    },

    handleAdd() {
      this.addDialogVisible = true;
    },

    handleDetailClose() {
      this.detailDialogVisible = false;
      this.currentDetailId = null;
      this.currentDetailTitle = "";
    },

    handleAddDialogClose() {
      this.addDialogVisible = false;
    },

    handleAddSuccess() {
      this.addDialogVisible = false;
      this.fetchData(this.currentSearchParams);
      this.$message.success("新增成功");
    },
  },
};
</script>

<style scoped>
.leadership-directives-container {
  padding: 20px;
}
</style>
