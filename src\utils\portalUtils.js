// 处理数据：让每个有children子级的添加openLeaves: false，标识为是否打开了子级
// 发请求，后端返回数据直接处理完再赋值到this.data.xxx中
export const processingData = (tableData, ids = "") => {
  tableData &&
    tableData.forEach((item, idx) => {
      item.isSelected = false; // 是否勾选
      if (item.children) {
        processingData(item.children, "");
      }
    });
};

// 得到表格data中的全部数据，包括children里的
// 此方法也要作用于每个环节的审批的金额是否已经填写
export const getTableAllData = (tableData, arr = []) => {
  tableData &&
    tableData.forEach((item) => {
      arr.push(item);
      getTableAllData(item.children, arr);
    });
  return arr;
};

export const isCamelCase = (str) => {
  // 驼峰格式规则：至少包含一个大写字母且不在开头，或全小写无分隔符
  return /^[a-z]+(?:[A-Z][a-z]*)*$/.test(str);
};

//截取路由转化为驼峰
export const getComponentName = (componentName) => {
  if (!isCamelCase(componentName)) {
    const pathParts = componentName.split("/").filter((p) => p); // 分割路径并过滤空值
    return pathParts[0];
    //驼峰转换逻辑
    // return pathParts
    //   .map((part, index) => {
    //     // 首段小写，后续段落首字母大写
    //     return index === 0
    //       ? part.toLowerCase()
    //       : part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
    //   })
    //   .join('')
  } else {
    return componentName;
  }
};

//获取菜单列表
export const getMenuList = (componentName) => {
  let menuChildrenList = [];
  let menuData = localStorage.getItem("userMenu");
  if (menuData) {
    let menuList = JSON.parse(menuData);
    menuList.forEach((item) => {
      item.path = getComponentName(item.path);
      if (item.path === componentName) {
        item.children.forEach((itemChildren) => {
          itemChildren.label = itemChildren.name;
          itemChildren.name = itemChildren.path;
          itemChildren.iconName = itemChildren.icon;
        });
        menuChildrenList = item.children;
      }
    });
  }
  return menuChildrenList;
};

// 新增角色验证专用方法
export const hasRole = (role) => {
  // 使用auth工具类获取解密后的用户信息
  const auth = require("./auth").default;
  const userInfo = auth.getUserInfo();

  // 检查用户角色，支持多种格式
  const roleList =
    userInfo?.userRole?.map((r) => r.roleName) || userInfo?.roles || [];

  return roleList.includes(role);
};

//公共调用权限方法（全局权限检查）
export const hasPermission = (permissionCode) => {
  // 使用权限管理器进行实际权限检查
  const { hasPermission: checkPermission } = require("./permissionManager");
  return checkPermission(permissionCode);
};

//页面级权限检查方法
export const hasPagePermission = (permissionCode, currentPagePath = "") => {
  const {
    hasPagePermission: checkPagePermission,
  } = require("./permissionManager");
  return checkPagePermission(permissionCode, currentPagePath);
};

//获取文件后缀
export const getFileExtension = (filename) => {
  // 匹配最后一个点之后的非点字符，或者整个字符串都没有点时返回空字符串
  const match = /\.([^.]+)$/.exec(filename);
  return match ? match[1] : "";
};

//判断是否为图片
export const isImageType = (type) => {
  const imageTypes = ["png", "jpg", "jpeg", "gif"];
  return imageTypes.includes(type);
};
