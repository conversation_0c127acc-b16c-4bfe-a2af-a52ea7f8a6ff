<template>
  <div class="user-index-container">
    <portal-table
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :show-pagination="false"
      :showSelection="false"
      :table-data="tableData"
      row-key="id"
      @add="handleAdd"
      @search="fetchData"
      @switch-change="handleSwitchChange"
      @handle-selection-change="handleSelectionChange"
    />
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :showFooter="false"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <addEidt ref="addEditClient" @ok="loadData" />
    </general-dialog>
  </div>
</template>

<script>
import { systemManagementApi } from "@/api";
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import { conversionDate } from "@/utils/publicMethod";

import addEidt from "./components/addEidt.vue";

export default {
  name: "UserIndex",
  components: { PortalTable, GeneralDialog, addEidt },
  data() {
    return {
      columns: [
        { text: true, align: "left", prop: "name", label: "菜单名称" },
        { text: true, prop: "path", label: "访问路径" },
        { text: true, prop: "menuTypeName", label: "类型" },
        { text: true, prop: "sort", label: "菜单排序" },
        {
          action: true, //是否显示操作
          label: "操作",
          operationList: [
            {
              label: "编辑",
              permission: "menu:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "menu:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      searchItems: [
        {
          prop: "orgName",
          label: "资源菜单名称",
          type: "input",
          placeholder: "请输入",
        },
      ],
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "",
    };
  },
  created() {},
  mounted() {
    this.registerHandlers();
    this.tableDataFn();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    async tableDataFn() {
      //获取所有菜单
      let params = {
        id: "",
        menuType: "",
        roleId: "",
      };
      const res = await systemManagementApi.getMenu(params);
      const { code, data, error } = res;
      if (code === 0) {
        this.tableData = data;
        this.updateTreeArray(this.tableData);
      } else {
        this.$message.error(error);
      }
    },
    updateTreeArray(nodes) {
      let _this = this;
      nodes.forEach((node) => {
        node.active = false;
        if (node.menuType === 0) {
          node.menuTypeName = "模块";
        } else if (node.menuType === 1) {
          node.menuTypeName = "一级菜单";
        } else if (node.menuType === 2) {
          node.menuTypeName = "二级菜单";
        } else {
          node.menuTypeName = "按钮";
        }
        if (node.children) {
          node.children.forEach((child) => {
            if (typeof child === "object") {
              // 如果已经是对象，则可以递归处理或直接修改其属性
              _this.updateTreeArray([child]); // 对子对象递归调用，这里可能需要调整以适应实际情况，例如直接在原数组上操作可能不需要额外的数组包裹。
            }
          });
        }
      });
    },
    //查询数据
    fetchData() {},

    //选中数据
    handleSelectionChange(selection) {
      console.log(selection);
    },

    //开关
    handleSwitchChange(row, $index) {
      //console.log(row, $index);
    },

    loadData(e) {
      this.dialogVisible = false;
      if (e === 1) {
        return;
      }
      this.tableDataFn();
    },

    //新增
    handleAdd() {
      this.generalDialogTitle = "新增菜单";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.addFormFn();
      });
    },
    //编辑
    handleEdit(row) {
      this.generalDialogTitle = "修改菜单";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.edit(row);
      });
    },

    //提交
    handleSubmit() {},

    handleCancel() {
      this.dialogVisible = false;
    },

    //删除
    handleDelete(row) {
      let txt = "此操作将永久删除该条数据, 是否继续?";
      if (row.children.length > 0) {
        txt = "此操作将永久删除该条数据和子级数据，是否继续？";
      }
      this.$confirm(txt, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        systemManagementApi.deleteMenu({ id: [row.id] }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success("删除成功");
          this.tableDataFn();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-index-container {
  padding: 20px;
}
</style>
