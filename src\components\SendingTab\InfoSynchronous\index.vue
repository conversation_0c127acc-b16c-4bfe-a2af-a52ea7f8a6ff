<template>
  <!-- 信息同步 -->
  <div class="info-synchronous">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-position="top"
      label-width="100px"
    >
      <el-form-item label="同步单位" prop="matter">
        <el-cascader
          ref="orgCascaderRef"
          class="matter-cascader"
          v-model="form.matter"
          :options="options"
          :props="cascaderProps"
          :show-all-levels="false"
          clearable
          @change="handleChange"
        ></el-cascader>
      </el-form-item>

      <el-form-item label="事件标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入事件标题"
          size="small"
          clearable
        />
      </el-form-item>

      <el-form-item label="同步内容" prop="content">
        <el-checkbox-group v-model="form.content">
          <el-checkbox v-for="item in dictionaryTypeList" :label="item.id">
            {{ item.itemName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {orgApi} from "@/api";
import {getItemList, infoSyncType,} from "@/utils/dictionary";
import assistantAI from "@/api/assistantAI";

export default {
  name: "InfoSynchronous",
  props: {
    componentProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        matter: [],
        title: "",
        content: [],
      },
      rules: {
        matter: [
          { required: true, message: "请输入同步单位", trigger: "blur" },
        ],
        title: [{ required: true, message: "请输入事件标题", trigger: "blur" }],
        content: [
          { required: true, message: "请选择同步内容", trigger: "change" },
        ],
      },

      options: [],
      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        multiple: true,
        emitPath: false
      },
      dictionaryTypeList: [],
    };
  },
  watch: {
    componentProps: {
      handler(newVal, oldVal) {
        this.form.id = newVal?.id;
        this.queryDictionaryType();
        this.queryOrgTreeDataList();
        this.form.title = newVal.infoTitle;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    async queryDictionaryType() {
      try {
        this.dictionaryTypeList = await getItemList(infoSyncType);
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      if (code === 0) {
        this.options = this.handleOrgTreeData(data);
      } else {
        this.$message.error(message || error);
      }
    },

    handleOrgTreeData(orgData) {
      return orgData.map((item) => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = { ...item };

        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    handleChange() {
      // 选择的接收对象集合
      const checkedNodes = this.$refs.orgCascaderRef.getCheckedNodes(true);
      const selectOrgData = checkedNodes.map((node) => node.data);
      this.form.matter = selectOrgData.map((node) => node.id);
    },

    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          let params = {
            id: this.form.id,
            orgId: this.form.matter,
            reportSynType: this.form.content,
          };
          console.log(params);
          const { code, message, error } = await assistantAI.synchronization(
            params
          );
          if (code === 0) {
            this.$message({
              message: "信息同步成功",
              type: "success",
            });
          } else {
            this.$message({
              message: error || message,
              type: "error",
            });
          }
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      matter: "",
      title: "",
      content: [],
    };
  },
};
</script>

<style scoped lang="scss">
.matter-cascader {
  width: 100%;
  height: 100px;
  ::v-deep {
    .el-input {
      height: 100%;
      input {
        height: 100% !important;
      }
    }

    .el-cascader__tags {
      top: 5px;
      transform: none;
    }
  }
}
</style>
