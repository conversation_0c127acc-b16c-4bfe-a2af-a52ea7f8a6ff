<!--
  接报信息详情主组件 - Component

  主要功能：
  - 全屏显示接报信息详情
  - 集成核心信息展示、步骤条、过程时间轴、过程详情弹框等子组件
  - 处理组件间的数据传递和事件通信
-->

<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      fullscreen
      :modal="false"
      custom-class="receive-information-detail-dialog"
    >
      <div slot="title" class="dialog-header">
        <div class="header-top">
          <span class="dialog-title">{{ detailTitle }}</span>

          <el-button
            type="success"
            size="small"
            icon="el-icon-document"
            @click="handleCreateKeyIntel"
            :loading="exportLoading"
            style="margin-right: 30px"
          >
            生成要情
          </el-button>
        </div>
      </div>
      <EventStepComponent
        v-if="detailData && detailData.id"
        :event-id="detailData.id"
        :current-status="detailData.infoStatus"
        @status-changed="handleStatusChanged"
      />
      <div v-loading="loading" class="detail-content">
        <el-row :gutter="20" class="main-layout">
          <el-col :span="14" class="left-content">
            <CoreInfoSection :detail-data="detailData" />
            <ProcessTimeline
              :process-data="processData"
              :process-type-options="processTypeOptions"
              :loading="loading"
              @show-detail="handleShowProcessDetail"
              @select-all="handleSelectAll"
              @item-select="handleItemSelect"
              @type-change="handleProcessTypeChange"
            />
          </el-col>

          <el-col :span="10" class="right-content">
            <div class="reserved-section">
              <sending-tab :component-props-data="detailData" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 过程详情弹框组件 -->
    <ProcessDetailDialog
      ref="processDetailRef"
      :visible.sync="processDetailDialogVisible"
      :current-process-item="currentProcessItem"
      :loading="detailLoading"
      @close="closeProcessDetailDialog"
    />
  </div>
</template>

<script>
import CoreInfoSection from "./CoreInfoSection.vue";
import ProcessTimeline from "./ProcessTimeline.vue";
import ProcessDetailDialog from "./ProcessDetailDialog.vue";
import EventStepComponent from "./EventStepComponent.vue";
import SendingTab from "@/components/SendingTab/index.vue";
import detailDataMixin from "./mixins/detailDataMixin.js";
import { mapState } from "vuex";
import { receiveInformationApi } from "@/api";

export default {
  name: "ReceiveInformationDetail",
  components: {
    SendingTab,
    CoreInfoSection,
    ProcessTimeline,
    ProcessDetailDialog,
    EventStepComponent,
  },
  mixins: [detailDataMixin],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
    detailTitle: {
      type: String,
      default: "接报信息详情",
    },
  },
  data() {
    return {
      processDetailDialogVisible: false,
      currentProcessItem: null,
      exportLoading: false,
      componentPropsData: {},
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val && this.detailId && !this.dataLoaded && !this.loading) {
          this.loadDetailData(this.detailId);
        } else if (!val) {
          this.resetDataState();
        }
      },
      immediate: false,
    },
    detailId: {
      handler(val, oldVal) {
        this.componentPropsData = {
          id: val,
        };
        if (this.dialogVisible && val && val !== oldVal && !this.loading) {
          this.resetDataState();
          this.loadDetailData(val);
        }
      },
      immediate: false,
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),

    currentUserName() {
      return this.userInfo?.name || "";
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    async handleShowProcessDetail(item) {
      if (!item) return;

      const otherId = item.originalData?.otherId;
      const otherType = item.originalData?.otherType;

      if (!otherId || !otherType) {
        this.$message.warning("该过程信息缺少必要参数，无法查看详情");
        return;
      }

      this.currentProcessItem = null;
      this.processDetailDialogVisible = true;

      try {
        const processItem = await this.showProcessDetail(item, this.detailId);
        if (processItem) {
          this.currentProcessItem = processItem;
          this.$refs.processDetailRef.mapMarkLatAndLng();
        } else {
          this.processDetailDialogVisible = false;
        }
      } catch (error) {
        console.error("加载过程详情失败:", error);
        this.processDetailDialogVisible = false;
        this.$message.error("加载过程详情失败");
      }
    },

    closeProcessDetailDialog() {
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;
    },

    handleSelectAll(value) {
      console.log("全选状态:", value);
    },

    handleItemSelect({ item, value }) {
      console.log("单项选择:", item, value);
    },

    async handleProcessTypeChange(selectedType) {
      try {
        this.loading = true;
        await this.fetchProcessList(this.detailId, selectedType);
      } catch (error) {
        console.error("过程类型过滤失败:", error);
      } finally {
        this.loading = false;
      }
    },

    async handleStatusChanged(newStatus) {
      try {
        // 更新本地数据状态
        if (this.detailData) {
          this.detailData.infoStatus = newStatus;
        }

        // 刷新过程信息列表
        await this.fetchProcessList(this.detailId);

        // 重新加载详情数据以确保状态同步
        await this.loadDetailData(this.detailId);
      } catch (error) {
        console.error("刷新数据失败:", error);
        this.$message.error("数据刷新失败，请重试");
      }
    },

    async handleCreateKeyIntel() {
      if (!this.detailId) {
        this.$message.warning("请先选择要生成要情的接报信息");
        return;
      }

      try {
        this.exportLoading = true;

        const response = await receiveInformationApi.createKeyIntel({
          id: this.detailId,
        });

        let fileName = "要情报告.docx";

        if (response.headers && response.headers["content-disposition"]) {
          const contentDisposition = response.headers["content-disposition"];

          try {
            if (contentDisposition.includes("filename*=UTF-8''")) {
              const encodedFileName =
                contentDisposition.split("filename*=UTF-8''")[1];
              if (encodedFileName) {
                fileName = decodeURIComponent(encodedFileName);
              }
            } else if (contentDisposition.includes('filename="')) {
              const match = contentDisposition.match(/filename="([^"]+)"/);
              if (match && match[1]) {
                fileName = decodeURIComponent(match[1]);
              }
            } else if (contentDisposition.includes("filename=")) {
              const match = contentDisposition.match(/filename=([^;]+)/);
              if (match && match[1]) {
                fileName = decodeURIComponent(match[1].replace(/"/g, ""));
              }
            }

            if (fileName.toLowerCase().startsWith("utf-8")) {
              fileName = fileName.replace(/^utf-8/i, "");
            }
          } catch (e) {
            console.warn("文件名解码失败，使用默认文件名:", e);
          }
        }

        const blob = new Blob([response.data || response], {
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success("要情报告生成成功");
      } catch (error) {
        console.error("生成要情失败:", error);
        this.$message.error("生成要情失败，请重试");
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep .dialog-header {
  display: flex;
  flex-direction: column;

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .dialog-title {
      font-size: 18px;
      margin-left: 20px;
    }
  }
}

.detail-content {
  height: calc(100vh - 20px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main-layout {
    display: flex;
    overflow: auto;
    flex: 1;
    height: 100%;

    .left-content,
    .right-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .left-content {
      min-width: 600px;
    }

    .right-content {
      min-width: 600px;

      .reserved-section {
        flex: 1;
        //padding: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        height: calc(100% - 40px);
        margin-bottom: 20px;
      }
    }
  }
}
</style>
