import request from "@/utils/publicSentimentRequest";

export default {
  //预警信息列表
  queryWarningInfoList(params) {
    return request({
      url: "/dw/tw/warningInfoDataPage",
      method: "post",
      data: params,
    });
  },

  //舆情数量统计分析
  querySubjectAttitudeTrend() {
    return request({
      url: "/dw/tw/thematicSubjectAttitudeTrend",
      method: "post",
    });
  },

  //舆情倾向性分析
  queryThematicSubjectAttitude(){
    return request({
      url: "/dw/tw/thematicSubjectAttitude",
      method: "post",
    });
  },

  //舆情媒体类型分析
  querySubjectMediaTrend() {
    return request({
      url: "/dw/tw/thematicSubjectMediaTrend",
      method: "post",
    });
  },

  //舆情重点媒体分布
  queryWarningMedia() {
    return request({
      url: "/dw/tw/warningMedia",
      method: "post",
    });
  },

  //舆情涉事分类分析
  querySubjectTagIndustry() {
    return request({
      url: "/dw/tw/thematicSubjectTagIndustry",
      method: "post",
    });
  },

  //舆情活跃媒体
  querySubjectSite() {
    return request({
      url: "/dw/tw/thematicSubjectSite",
      method: "post",
    });
  },

  //舆情属地分析
  querySubjectPublishRegion() {
    return request({
      url: "/dw/tw/thematicSubjectPublishRegion",
      method: "post",
    });
  },



};
