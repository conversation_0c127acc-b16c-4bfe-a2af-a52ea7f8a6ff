<template>
  <div class="third-home-index">
    <iframe class="third-home-iframe" :src="getIframeSrc"></iframe>
  </div>
</template>
<script>
import { mapGetters } from "vuex";

export default {
  name: "ThirdHome",
  data() {
    return {
      iframeSrc: "",
    };
  },
  watch: {
    getIframeSrc: {
      handler(newVal) {
        if (newVal) {
          this.iframeSrc = newVal;
        }
      },
      //deep: true, // 深度监听（适合对象/数组类型）
      immediate: true,  // 立即执行（适合需要初始化逻辑）
    },
  },
  computed: {
    ...mapGetters({
      getIframeSrc: "publicStore/getIframeSrc",
    }),
  },
  mounted() {},
  methods: {},
};
</script>
<style scoped lang="scss">
.third-home-index {
  height: 99.7%;

  .third-home-iframe {
    width: 100%;
    height: 100%;
    border: 0 solid transparent;
  }
}
</style>
