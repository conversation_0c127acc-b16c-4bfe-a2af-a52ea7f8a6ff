/**
 *  短信管理相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";
// 短信分析
export default {
    // 保存
    textMessageSave(params = {}) {
        return request({
            url: '/ds/smsTask/save',
            method: 'post',
            data: params
        })

    },
    // 发送
    textMessageSend(params = {}) {
        return request({
            url: '/ds/smsTask/createSendTask',
            method: 'post',
            data: params
        })

    },
    // 获取组
    queryGroupManageList(params = {}) {
        return request({
            url: '/ds/groupManage/queryGroupManageList',
            method: 'get',
            params: params
        })

    },
    // 根据组获取组人员
    queryUserInfo(params = {}) {
        return request({
            url: '/ds/groupManage/queryUserInfo',
            method: 'post',
            data: params
        })
    },
    //查询启用的文字后缀内容
    queryEnable(params = {}) {
        return request({
            url: '/ds/smsCharactersSuffix/queryEnable',
            method: 'get',
            params: params
        })
    },
    //文本纠错
    textCorrection(params = {}) {
        return request({
            url: '/ds/smsIntelligent/textCorrection',
            method: 'post',
            data: params
        })
    },
    //获取短信详情
    queryTextMessageDetails(params = {}) {
        return request({
            url: '/ds/smsTask/queryById',
            method: 'post',
            data: params
        })
    },
    
    
};
