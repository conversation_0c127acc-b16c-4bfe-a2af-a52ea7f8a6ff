<!-- 交接班管理 - Page -->
<template>
  <div style="padding: 20px">
     <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="值班值守人员" name="0">
          <dutyShiftTab></dutyShiftTab>
        </el-tab-pane>

        <el-tab-pane 
          v-for="tab in generalTabs" 
          :key="tab.name"
          :label="tab.label" 
          :name="tab.name"
        >
          <dutyShiftGeneralTab :ref="`generalTab_${tab.name}`" v-show="activeName === tab.name" :tabType="tab.name"></dutyShiftGeneralTab>
        </el-tab-pane>

      </el-tabs>

      <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
        <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
      </el-dialog>
  </div>
</template>

<script>

import dutyShiftTab from './components/dutyShiftTab.vue';
import dutyShiftGeneralTab from './components/dutyShiftGeneralTab.vue';
import { dutyManagementApi } from "@/api";
import { getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "HandoverManagement",
  components: {
    dutyShiftTab,
    dutyShiftGeneralTab,
  },
  data() {
    return {
      activeName: '0',
      generalTabs: [
        { name: '1', label: '预警发布及天气情况' },
        { name: '2', label: '风险分析' },
        { name: '3', label: '指挥调度及相关会议' },
        { name: '4', label: '批示接收与办理' },
        { name: '5', label: '突发情况接报与处置' },
        { name: '6', label: '防汛工作' },
        { name: '7', label: '高危监测' },
        { name: '8', label: '信息化保障' },
        { name: '9', label: '视频调取和实战演练' },
        { name: '10', label: '注意事项' },
      ],
      showIframe: false,
      preFileUrl: "",
    };
  },
  mounted() {
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.handlePreviewInfo,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.handleExport,
      });
    },
    handlePreviewInfo() {
      dutyManagementApi.shiftingDutyUrl().then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.handlePreview(data, false);
      });
    },
    handleExport() {
      dutyManagementApi.shiftingDutyUrl().then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.handlePreview(data, true);
      });
    },
    handlePreview(file, isDownload) {
      let fileUrl = auth.getFileBaseUrl() + file;;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!isDownload && !previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(fileUrl);
      } 
      if (isDownload) {
        window.open(fileUrl, '_blank');
      } else {
        this.preFileUrl = fileUrl;
        this.showIframe = true;
      }
    },
    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },
    handleClick(tab, event) {
      // console.log(this.$refs[`generalTab_${tab.name}`]);
      // 通过ref调用组件方法
      this.$nextTick(() => {
        if (this.$refs[`generalTab_${tab.name}`] && this.$refs[`generalTab_${tab.name}`][0]) {
          this.$refs[`generalTab_${tab.name}`][0].loadData(tab.name);
        }
      });
    }
  }
};
</script>


<style lang="scss" scoped>

</style>