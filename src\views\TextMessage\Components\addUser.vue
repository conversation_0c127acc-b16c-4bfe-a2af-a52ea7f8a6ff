<template>
    <general-dialog 
        :dialog-visible="dialogVisible" 
        :general-dialog-title="'添加人员'" 
        dialog-width="800px" 
        @confirm="handleSubmit"
        @cancel="handleClose">
        <el-form class="add-form" ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="form.name" minlength="2" maxlength="10" placeholder="请输入" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="手机号" prop="mobile">
                        <el-input v-model="form.mobile" maxlength="11" placeholder="请输入" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
    name: '',
    components: {
        GeneralDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            form: {
                name: '',
                mobile: ""
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: ' ',
                    },
                ],
                mobile: [
                    {
                        required: true,
                        message: ' ',
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: '请输入正确的号码',
                    },
                ],
            }
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
    },
    methods: {
        resetForm() {
            this.form.name = ''
            this.form.mobile = ''
            this.$refs['form'].resetFields();
        },
        // 保存
        handleSubmit() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    let data = this.modelValue.concat([{name:this.form.name,mobile:this.form.mobile}]);
                    this.$emit('update:modelValue',data)
                    this.dialogVisible = false
                    this.resetForm()
                } else {
                    
                    return false;
                }
            });
        },
        // 关闭
        handleClose() {
            this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.dialogVisible = false;
                    this.resetForm();
                })
                .catch(() => {
                    // 取消关闭
                })
        },
    },
    mounted() {
    }
}
</script>

<style scoped></style>