import request from "@/utils/request";

/**
 * 领导批示相关API接口
 */
export default {
  /**
   * 查询领导指示列表
   * @param {Object} params - 查询参数
   * @param {number} params.count - 数量限制 (1-999)
   * @param {string} params.endTime - 批示结束时间 (date-time格式)
   * @param {string} params.infoTitle - 事件标题
   * @param {number} params.page - 页码 (从0开始)
   * @param {string} params.startTime - 批示开始时间 (date-time格式)
   * @param {string} params.status - 状态
   * @returns {Promise} API响应
   */
  queryLeadershipPage(params) {
    return request({
      url: "/ds/leadership/queryLeadershipPage",
      method: "post",
      data: params,
    });
  },

  /**
   * 导出领导批示列表
   * @param {Object} params - 导出参数
   * @returns {Promise} 文件流
   */
  exportList(params) {
    return request({
      url: "/ds/leadership/exportList",
      method: "post",
      data: params,
      responseType: "blob",
    });
  },

  /**
   * 新增领导批示
   * @param {Object} params - 新增参数
   * @param {string} params.reportInfoId - 事件标题ID
   * @param {string} params.recordMessage - 事件详情
   * @param {string} params.approvalInfo - 批示信息
   * @param {string} params.approvalTime - 批示时间
   * @param {string} params.leaderUserId - 批示领导ID
   * @param {string} params.leaderOrgId - 批示领导组织机构ID
   * @returns {Promise} API响应
   */
  createLeadership(params) {
    return request({
      url: "/ds/leadership/createLeadership",
      method: "post",
      data: params,
    });
  },

  /**
   * 查询事件标题列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  queryReportTitleList(params) {
    return request({
      url: "/ds/leadership/queryReportTitleList",
      method: "post",
      data: params,
    });
  },

  /**
   * 查询组织架构通讯录
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  queryOrgAddressBook(params) {
    return request({
      url: "/ds/addressBook/queryOrgAddressBook",
      method: "post",
      data: params,
    });
  },

  /**
   * 查询领导指示详情信息
   * @param {Object} params - 查询参数
   * @param {string|number} params.id - 批示ID
   * @returns {Promise} API响应
   */
  queryLeadershipInfo(params) {
    return request({
      url: "/ds/leadership/queryLeadershipInfo",
      method: "post",
      data: params,
    });
  },
};
