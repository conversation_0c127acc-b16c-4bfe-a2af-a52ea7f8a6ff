<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="级别" prop="level">
          <el-select
            v-model="formData.level"
            :disabled="styleType === 2"
            placeholder="请选择" 
            @change="changeLevelType">
            <el-option
              v-for="data in levelList"
              :key="data.id"
              :label="data.name"
              :value="data.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="岗位名称" prop="positionName">
          <el-input
            v-model="formData.positionName"
            autocomplete="off"
            placeholder="请输入"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="上级名称" prop="parentId">
          <el-select
            v-model="formData.parentId"
            :disabled="formData.level === 1"
            clearable
            filterable
            placeholder="请选择"
          >
            <!-- <el-option label="全部" :value="''"></el-option> -->
            <el-option
              v-for="data in parentList"
              :key="data.id"
              :label="data.positionName"
              :value="data.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="值班人数" prop="num">
          <el-input
            v-model="formData.num"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入"
            type="tel"
            @input="handlePhoneInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="formData.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="tel"
            @input="handleSortInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>

import { dutyManagementApi } from '@/api/index'

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 1新增; 2修改
      templateId: '',
      levelList: [
        {
          id:1,
          name:"一级名称"
        },
        {
          id:2,
          name:"二级名称"
        },
        {
          id:3,
          name:"三级名称"
        }
      ],
      parentList: [],
      formData: {
        level: null,
        positionName: '',
        parentId: '',
        num: 0,
        sort: 0,
      },
      rules: {
        level: [
          {required: true, message: '所属级别不能为空', trigger: 'blur'}
        ],
        parentId: [
          {required: true, message: '上级名称不能为空', trigger: 'blur'}
        ],
        positionName: [
          {required: true, message: '岗位名称不能为空', trigger: 'blur'}
        ],
        num: [
          {required: true, message: '值班人数不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    handlePhoneInput(value) {
      // 只允许输入数字 使用正则表达式移除非数字字符
      this.formData.num = value.replace(/\D/g, '')
    },
    handleSortInput(value) {
      this.formData.sort = value.replace(/\D/g, '')
    },
    changeLevelType(levelId) {
      if (levelId === 1) {
        this.formData.parentId = '0';
        this.parentList = [{positionName: '无', id: '0'}];
        // this.parentList = [{positionName: '无', id: 0}];
      } else {
        this.getParentList(levelId);
      }
    },

    addFormFn(templateId) {
      this.reset();
      this.styleType = 1;
      this.templateId = templateId;
    },
    edit(data) {
      this.styleType = 2
      this.getPositionById(data.id);
      this.getParentList(data.level);
    },
    
    async getPositionById(id) {
      const res = await dutyManagementApi.queryPositionById({id});
      const {code, data, error} = res;
      if (code === 0) {
        this.formData = data;
        if (data.level === 1) {
          this.formData.parentId = '0';
          this.parentList = [{positionName: '无', id: '0'}];
        }
      } else {
        this.$message.error(error);
      }
    },
    async getParentList(level) {
      const res = await dutyManagementApi.queryPositionByLevel({level});
      const {code, data, error} = res;
      if (code === 0) {
        this.parentList = data;
      } else {
        this.$message.error(error);
      }
    },

    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            this.formData.templateId = this.templateId;
            const res = await dutyManagementApi.createPosition(this.formData);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功');
              this.$emit('ok');
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          } else {
            const res = await dutyManagementApi.updatePosition(this.formData);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功');
              this.$emit('ok');
              this.close();
            } else {
              this.$message.error(error);
            }
          }
        } else {
          return false;
        }
      })
    },
    close() {
      this.reset();
      this.$emit('ok', 1);
    },
    reset() {
      this.formData = {
        level: null,
        positionName: '',
        parentId: '',
        num: null,
        sort: null,
      }
      this.menuTypeList = [];
      this.parentMenuList = [];
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
