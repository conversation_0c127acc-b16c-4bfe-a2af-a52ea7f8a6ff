<template>
  <div class="org-form-container">
    <!-- 组织机构新增表单 - Component -->
    <el-form
      ref="orgForm"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="org-form"
    >
      <el-form-item label="机构名称" prop="orgName">
        <el-input
          v-model="formData.orgName"
          placeholder="请输入组织机构名称"
          maxlength="50"
          clearable
        />
      </el-form-item>

      <el-form-item label="上级机构" prop="parentId">
        <el-cascader
          v-model="formData.parentIdPath"
          placeholder="请选择上级机构"
          :options="cascaderOptions"
          :props="cascaderProps"
          :show-all-levels="false"
          clearable
          filterable
          style="width: 100%"
          @change="handleCascaderChange"
        />
      </el-form-item>

      <el-form-item label="排序" prop="orderNo">
        <el-input-number
          v-model="formData.orderNo"
          :min="0"
          :max="9999"
          placeholder="请输入排序号"
          style="width: 100%"
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { orgApi } from "@/api/index.js";

export default {
  name: "OrgForm",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orgTreeData: {
      type: Array,
      default: () => [],
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      formData: {
        id: "",
        orgName: "",
        parentId: "",
        parentIdPath: [], // 级联选择器的值
        orderNo: 0,
        isDel: 0,
      },
      formRules: {
        orgName: [
          { required: true, message: "请输入机构名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "机构名称长度在2到50个字符",
            trigger: "blur",
          },
        ],
        orderNo: [{ required: true, message: "请输入排序", trigger: "blur" }],
      },
      submitLoading: false,
      // 级联选择器配置
      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        checkStrictly: true, // 允许选择任意级别
      },
    };
  },
  computed: {
    orgTreeOptions() {
      return this.orgTreeData || [];
    },
    // 级联选择器选项
    cascaderOptions() {
      return this.orgTreeData || [];
    },
    // 是否为编辑模式
    isEditMode() {
      return this.editData && this.editData.id;
    },
    // 按钮文本
    submitButtonText() {
      return this.isEditMode ? "保存" : "新增";
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm();
        // 使用 $nextTick 确保在下一个DOM更新周期后填充数据
        this.$nextTick(() => {
          if (this.isEditMode) {
            this.fillFormData();
          }
        });
      }
    },
    editData: {
      handler(newVal) {
        if (newVal && this.visible) {
          this.$nextTick(() => {
            this.fillFormData();
          });
        }
      },
      deep: true,
      immediate: true, // 立即执行一次
    },
  },
  methods: {
    resetForm() {
      this.formData = {
        id: "",
        orgName: "",
        parentId: "",
        parentIdPath: [],
        orderNo: 0,
        isDel: 0,
      };
      if (this.$refs.orgForm) {
        this.$refs.orgForm.clearValidate();
      }
    },

    // 填充编辑数据
    fillFormData() {
      if (this.editData && this.editData.id) {
        this.formData = {
          id: this.editData.id, // 当前编辑行的ID
          orgName: this.editData.orgName || "",
          parentId: this.editData.parentId || "", // 上级机构ID
          parentIdPath: this.buildParentIdPath(this.editData.parentId),
          orderNo: this.editData.orderNo || 0,
          isDel: this.editData.isDel || 0,
        };
      }
    },

    // 构建级联选择器路径
    buildParentIdPath(parentId) {
      if (!parentId || !this.orgTreeData) return [];

      const findPath = (nodes, targetId, path = []) => {
        for (const node of nodes) {
          const currentPath = [...path, node.id];
          if (node.id === targetId) {
            return currentPath;
          }
          if (node.children && node.children.length > 0) {
            const result = findPath(node.children, targetId, currentPath);
            if (result) return result;
          }
        }
        return null;
      };

      return findPath(this.orgTreeData, parentId) || [];
    },

    // 级联选择器变化处理
    handleCascaderChange(value) {
      if (value && value.length > 0) {
        // 取最后一个值作为parentId
        this.formData.parentId = value[value.length - 1];
      } else {
        this.formData.parentId = "";
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.orgForm.validate();
        this.submitLoading = true;

        const params = {
          orgName: this.formData.orgName,
          parentId: this.formData.parentId || "",
          orderNo: this.formData.orderNo,
          isDel: 0,
        };

        let response;
        if (this.isEditMode) {
          // 编辑模式 - 调用更新API
          params.id = this.formData.id;
          response = await orgApi.updateOrg(params);
        } else {
          response = await orgApi.createOrg(params);
        }

        if (response && response.code === 0) {
          this.$message.success(this.isEditMode ? "修改成功" : "新增成功");
          this.$emit("success");
        } else {
          this.$message.error(
            response?.message || (this.isEditMode ? "修改失败" : "新增失败")
          );
        }
      } catch (error) {
        if (error !== false) {
          this.$message.error(
            (this.isEditMode ? "修改失败" : "新增失败") +
              ": " +
              (error.message || "未知错误")
          );
        }
      } finally {
        this.submitLoading = false;
      }
    },

    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.org-form-container {
  padding: 30px;
  background: #fff;
}

.org-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;

  .el-button {
    width: 100px;
    margin: 0 15px;
  }
}

::v-deep .el-form-item {
  margin-bottom: 25px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

::v-deep .el-input__inner {
  border-radius: 6px;
}

::v-deep .el-input-number {
  width: 100%;
}
</style>
