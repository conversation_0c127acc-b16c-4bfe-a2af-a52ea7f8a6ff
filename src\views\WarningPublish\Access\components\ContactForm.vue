<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :showFooter="isEditState !== 'view'"
    :general-dialog-title="
      isEditState == 'edit'
        ? '编辑预警信息'
        : isEditState == 'add'
        ? '新增预警信息'
        : '查看预警信息'
    "
    dialog-width="800px"
    @cancel="handleClose"
    @confirm="handleSubmit"
  >
    <el-form
      ref="form"
      class="add-form"
      :model="form"
      :rules="rules"
      label-width="110px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警事项" prop="matter">
            <el-input
              v-model="form.matter"
              placeholder="请输入预警事项"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="eventType">
            <el-select
              v-model="form.eventType"
              placeholder="请选择事件类型"
              style="width: 100%"
              size="small"
              v-removeAriaHidden
            >
              <el-option
                v-for="item in eventTypeOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接收单位" prop="pushDeptId">
            <el-cascader
              size="small"
              v-model="form.pushDeptId"
              :options="orgOptions"
              :props="orgProps"
              @change="handleDeptChange"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布日期" prop="pushTime">
            <el-date-picker
              v-model="form.pushTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="发布日期"
              size="small"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="预计持续时间" prop="duration">
            <div style="display: flex; align-items: center; gap: 10px">
              <el-input
                v-model.number="form.duration"
                style="flex: 3"
                type="number"
                size="small"
                :min="0"
              ></el-input>
              <el-select
                size="small"
                style="flex: 2"
                v-model="durationUnit"
                placeholder="请选择"
              >
                <el-option key="天" label="天" value="天"> </el-option>
                <el-option key="小时" label="小时" value="小时"> </el-option>
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="起始日期"
              size="small"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="影响范围" prop="areaList">
            <el-cascader
              ref="areaCascaderRef"
              v-model="areaListProp"
              collapse-tags
              :options="scopeList"
              :props="{
                multiple: true,
                children: 'child',
                label: 'townName',
                value: 'code',
                emitPath: false,
              }"
              @change="handleAreaChange"
              clearable
              size="small"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警级别" prop="level">
            <el-select
              v-model="form.level"
              placeholder="请选择预警级别"
              style="width: 100%"
              size="small"
              v-removeAriaHidden
            >
              <el-option
                v-for="item in levelList"
                :value="Number(item.itemValue)"
                :label="item.itemName"
                :key="item.itemValue"
              >
                {{ item.itemName }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情况说明" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              maxlength="200"
              :rows="10"
              resize="none"
              placeholder="请输入情况说明"
              size="small"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="咨询人" prop="consultant">
            <el-input
              v-model="form.consultant"
              placeholder="请输入咨询人"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="咨询电话" prop="consultantMobile">
            <el-input
              v-model="form.consultantMobile"
              placeholder="请输入咨询电话"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人" prop="reportUser">
            <el-input
              v-model="form.reportUser"
              placeholder="请输入上报人"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人联系方式" prop="reportUserMobile">
            <el-input
              v-model="form.reportUserMobile"
              placeholder="请输入上报人联系方式"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { warningPublishApi } from "@/api";
import { getItemList, alertLevel } from "@/utils/dictionary";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  name: "ContactForm",
  components: {
    GeneralDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: String,
      default: "add",
    },
    eventTypeOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      areaListProp: [],
      form: {
        matter: "",
        eventType: "",
        pushDeptName: "",
        pushTime: "",
        duration: "",
        startDate: "",
        areaList: [],
        level: "",
        description: "",
        consultant: "",
        consultantMobile: "",
        reportUser: "",
        reportUserMobile: "",
      },
      rules: {
        matter: [
          {
            required: true,
            message: "请输入预警事项",
            trigger: "blur",
          },
        ],
        eventType: [
          {
            required: true,
            message: "请选择事件类型",
            trigger: "blur",
          },
        ],
        pushDeptId: [
          {
            required: true,
            message: "请输入发布单位",
            trigger: "blur",
          },
        ],
        pushTime: [
          {
            required: true,
            message: "请选择发布日期",
            trigger: "blur",
          },
        ],
        duration: [
          {
            required: true,
            message: "请输入预计持续时间",
            trigger: "blur",
          },
          {
            type: "number",
            min: 0,
            message: "持续时间不能小于0",
            trigger: "blur",
          },
        ],
        startDate: [
          {
            required: true,
            message: "请选择起始日期",
            trigger: "blur",
          },
        ],
        areaList: [
          {
            required: true,
            message: "请选择影响范围",
            trigger: "blur",
          },
        ],
        level: [
          {
            required: true,
            message: "请选择预警级别",
            trigger: "blur",
          },
        ],
        description: [
          {
            required: true,
            message: "请输入情况说明",
            trigger: "blur",
          },
        ],
        consultant: [
          {
            required: true,
            message: "请输入咨询人",
            trigger: "blur",
          },
        ],
        consultantMobile: [
          {
            required: true,
            message: "请输入咨询电话",
            trigger: "blur",
            validator: (rule, value, callback) => {
              const mobileReg = /^1[3-9]\d{9}$/;
              const phoneReg = /^(\d{3,4}-)?\d{7,8}$/;
              if (!value) {
                return callback(new Error("请输入联系方式"));
              }
              if (!mobileReg.test(value) && !phoneReg.test(value)) {
                callback(new Error("请输入正确的手机号或电话号码"));
              } else {
                callback();
              }
            },
          },
        ],
      },
      submitting: false,
      eventList: [],
      scopeList: [], //影响范围
      levelList: [], //级别
      orgOptions: [],
      orgProps: {
        value: "id",
        label: "orgName",
        children: "children",
        // 只保留选中叶子节点
        emitPath: false,
        // 多选，父节点可以选中
        // multiple: true,
      },
      durationUnit: "天",
    };
  },
  mounted() {
    this.registerHandlers();
    this.queryRegion();
    this.queryAlertLevel();
    this.queryOrg();
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    isEditState: {
      get() {
        return this.isEdit;
      },
      set(val) {
        this.$emit("update:isEdit", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
        this.$nextTick(() => {
          this.resetForm();
        });
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleDeptChange(value) {
      const selectedNode = this.findNodeById(this.orgOptions, value);
      if (selectedNode) {
        this.form.pushDeptName = selectedNode.orgName; // 部门名称
        this.form.pushDeptId = selectedNode.id; // 部门ID
      }
    },
    handleAreaChange(value) {
      this.$nextTick(() => {
        if (this.$refs.areaCascaderRef) {
          const nodes = this.$refs.areaCascaderRef.getCheckedNodes(true);
          this.form.areaList = nodes.map((node) => ({
            areaId: node.data.code,
            areaName: node.data.townName,
          }));
        }
      });
    },
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },
    // 查询组织机构树形结构
    async queryOrg() {
      const res = await comprehensiveAssessmentApi.queryOrg({
        id: "",
        orderRule: 0,
        orgName: "",
        parentId: "",
      });
      if (res.code === 0) {
        this.orgOptions = res.data;
        this.removeEmptyChildren(this.orgOptions);
      }
    },
    // 去掉空children
    removeEmptyChildren(nodes) {
      if (!nodes || !Array.isArray(nodes)) return;
      nodes.forEach((node) => {
        if (node.children && node.children.length === 0) {
          delete node.children;
        } else {
          this.removeEmptyChildren(node.children);
        }
      });
    },
    addHandler() {
      this.dialogVisible = true;
      this.isEditState = "add";
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
    },
    initForm() {},
    resetForm() {
      this.$nextTick(() => {
        this.form = {
          matter: "",
          eventType: "",
          pushDeptName: "",
          pushTime: "",
          duration: "",
          startDate: "",
          areaList: [],
          level: "",
          description: "",
          consultant: "",
          consultantMobile: "",
          reportUser: "",
          reportUserMobile: "",
        };
        this.$refs["form"].resetFields();
        this.areaListProp = [];
      });
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const formData = { ...this.form };
            formData.duration = formData.duration + this.durationUnit;
            if (this.isEdit) {
              formData.id = this.formData.id;
            }

            this.$emit("submit", formData, this.isEdit);
          } finally {
            this.submitting = false;
          }
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    // 获取北京区域
    queryRegion(params = {}) {
      try {
        warningPublishApi.queryRegion(params).then((res) => {
          this.scopeList = res.data;
        });
      } catch (error) {
        throw error;
      }
    },
    // 获取预警类型(红...)
    async queryAlertLevel() {
      let data = await getItemList(alertLevel);
      this.levelList = data;
    },
  },
};
</script>

<style scoped>
.sort {
  width: 100%;
}

/* .dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
} */
</style>
