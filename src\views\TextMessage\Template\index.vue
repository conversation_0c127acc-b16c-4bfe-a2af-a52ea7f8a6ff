<template>
    <!-- 右侧内容区域 -->
    <div class="contact-book-container">
        <div class="contact-book-main">
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <SearchForm :form-config="searchConfig" :tips="searchTips" :show-tips="false" @search="handleSearch"
                    @reset="handleReset" ref="searchFormRef" />
            </div>
            <!-- 表格区域 -->
            <div class="table-wrapper">
                <DataTable :table-data="contactList || []" :columns="responsiveTableColumns || []"
                    :tableHeight="600"
                    :total="pagination?.total || 0" :current-page="pagination?.current || 1"
                    :page-size="pagination?.pageSize || 10" :row-actions="tableActions" :action-column-width="200"
                    :loading="loading" :stripe="true" :border="true" :show-pagination="true" :show-index="true"
                    :key="tableKey" @row-action="handleTableRowAction" @current-change="handleCurrentChange" @size-change="handleSizeChange"/>
            </div>
        </div>
        <!-- 联系人表单对话框 -->
        <ContactForm :visible.sync="formVisible" :form-data="currentContact" :isEdit="isEdit"
            @update:isEdit="isEdit = $event" @submit="handleFormSubmit" />
    </div>
</template>

<script name="TextMessageTemplate">
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import ContactForm from "./components/ContactForm.vue";
import { getItemList, textMessageTemplateType } from "@/utils/dictionary";
import mixinTable from './mixinTable.js'
export default {
    mixins: [mixinTable],
    components: {
        SearchForm,
        DataTable,
        ContactForm
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "templateTheme",
                    label: "短信主题",
                    type: "input",
                    placeholder: "输入短信主题",
                    width: "200px",
                },
                {
                    //sms_tem_type 字典
                    prop: "templateType",
                    label: "模版类型",
                    type: "select",
                    placeholder: "模版类型",
                    width: "200px",
                    options: [],
                },
                {
                    prop: "isCommonlyUsed",
                    label: "是否常用",
                    type: "select",
                    placeholder: "是否常用",
                    width: "200px",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
            ],
            searchTips: [

            ],
            // 表格列配置
            tableColumns: [
                { prop: "templateTheme", label: "短信主题", width: 320, sortable: false },
                { prop: "templateContent", label: "短信内容", minWidth: 400, sortable: false },
                {
                    prop: "templateType", label: "模板类型", width: 150, sortable: false, render: (row, index) => {
                        let options = this.searchConfig[1].options;
                        let data = options.filter(v => v.value == row.templateType)[0]
                        return data ? data.label : ''
                    }
                },
                {
                    prop: "status", label: "模板状态", width: 150, sortable: false, render: (row, index) => {
                        return ['禁用', '启用'][row.status]
                    }
                },
                {
                    prop: "isCommonlyUsed", label: "是否常用", width: 150, sortable: false, render: (row, index) => {
                        return ['否', '是'][row.isCommonlyUsed]
                    }
                },

            ],
            tableActions: [
                {
                    key: "edit",
                    label: '编辑',
                    type: "text",
                    size: "mini",

                },
                {
                    key: "remove",
                    label: '删除',
                    type: "text",
                    size: "mini",
                },

            ],
            // 表格刷新key
            tableKey: 0,
            // 表单相关状态
            formVisible: false,
            currentContact: {},
            isEdit: false,
        };
    },
    computed: {
        // 响应式表格列配置
        responsiveTableColumns() {
            return this.tableColumns;
        },
    },
    methods: {
        // 搜索相关方法
        async handleSearch(formData) {
            // 保持当前的组织过滤状态
            const searchParams = { ...formData };
            await this.seachContactList(searchParams);
            await this.getContactList();
            this.$message.success("搜索完成");
        },
        async initData() {
            try {
                await this.getContactList();
            } catch (error) {
                console.error("短信模版页面初始化失败:", error);
                this.$message.error("页面初始化失败，请刷新重试");
            }
        },
        async handleReset() {
            await this.seachContactList({});
            await this.setCurrentChange({ current: 1 })
            await this.getContactList();
        },
        async handleFormSubmit(formData, isEdit) {
            if (isEdit) {
                await this.updateTextMessageTemplate(formData)
            } else {
                await this.createTextMessageTemplate(formData)
            }
            this.initData()
            this.formVisible = false
        },
        async handleTableRowAction(action, row, index) {
            let type = action.label
            switch (type) {
                case '查看':
                    this.formVisible = true
                    break;
                case '编辑':
                    this.formVisible = true
                    this.isEdit = true
                    let response = await this.queryTextMessageTemplateById(row)
                    this.currentContact = response

                    console.log(response)
                    break;
                case '删除':
                    this.$confirm("确定要删除吗", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        await this.removeTextMessageTemplate(row)
                        this.initData()
                    }).catch(() => {
                    });

                    break;
                default:
                    break;
            }
        },
        // 分页
        async handleCurrentChange(page) {
            console.log('--------1')
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ current: page })
            await this.getContactList();
        },
        async handleSizeChange(size) {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ pageSize: size })
            await this.getContactList();
        },
        
        // 获取短信类型
        async queryTemplateType() {
            let data = await getItemList(textMessageTemplateType)
            let list = data.map((row) => {
                return {
                    label: row.itemName,
                    value: row.itemValue
                }
            })
            this.searchConfig[1].options = list
        }
    },
    mounted() {
        this.queryTemplateType()
        this.initData();
    }
}
</script>

<style scoped>
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}

.search-wrapper {
    margin-bottom: 8px;
}

.table-wrapper {
    flex: 1;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-wrapper>>>.el-button--text:nth-child(1) {
    color: #409eff !important;
}

.table-wrapper>>>.el-button--text:nth-child(2) {
    color: #67c23a !important;
}

.table-wrapper>>>.el-button--text:nth-child(3) {
    color: #e6a23c !important;
}
</style>