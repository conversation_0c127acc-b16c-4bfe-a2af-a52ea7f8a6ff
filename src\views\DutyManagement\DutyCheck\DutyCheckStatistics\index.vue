<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { dutyManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType } from "@/utils/dictionary";

import { conversionDateNotTime } from "@/utils/publicMethod";


export default {
  name: "DutyCheckStatisticsManagement",
  components: {
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "inspectionTime",
          label: "统计时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        // {
        //   label: "检查方式",
        //   prop: "inspectionType",
        //   type: "select",
        //   placeholder: "请选择",
        //   width: "120",
        //   options: [],
        // }
      ],
      columns: [
        {
          prop: "inspectionUnit",
          label: "被检查单位",
          text: true
        },
        {
          label: "检查次数",
          prop: "inspectionCount",
          text: true
        },
        {
          prop: "examinationRateStr",
          label: "参检率",
          text: true
        },
        {
          prop: "examinationRateOrder",
          label: "参检率排名",
          text: true
        },
        {
          prop: "passRateStr",
          label: "合格率",
          text: true
        },
        {
          prop: "passRateOrder",
          label: "合格率排名",
          text: true
        },
      ],
      tableData: [],

      inspectionTypeList: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  mounted() {
    this.getTableDataList();
    // this.queryDictionaryType();
  },
  methods: {
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.inspectionTypeList = await getItemList(inspectionDictionaryType);
        this.searchItems[1].options = this.inspectionTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryInspectionStatisticsList(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotTime(row.inspectionTime[0])
        row.endTime = conversionDateNotTime(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
