<!-- 
author：ghl
time:2025-01-23

应急演练分析统计
-->
<template>
    <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    props: {
        chartsFourList: {
            type: Array,
            default: [],
        },
    },
    name: 'rehearsal',
    mounted() {
        this.initChart();
    },
    methods: {
        initChart() {
            var xData = []
            var yData = []
            this.chartsFourList.map((item, index) => {
                xData.push(item.item_name)
                yData.push(item.groupCount)
            })
            const chartDom = this.$refs.chart;
            const myChart = echarts.init(chartDom);
            const option = {
                tooltip: {
                    trigger: "axis",
                },
                
                legend: {
                    data: ["数量"],
                    right: 20,
                    top: 0,
                    textStyle: {
                    color: "#000000",
                    },
                    itemWidth: 12,
                    itemHeight: 10,
                    // itemGap: 35
                },
                grid: {
                    left: "2%",
                    top: "15%",
                    right: "2%",
                    bottom: "2%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    data: xData,
                    axisLine: {
                    lineStyle: {
                        color: "#000000",
                    },
                    },
                    axisLabel: {
                        formatter:function(value) {
                            return value.split("").join("\n")
                        },
                        textStyle: {
                        color: '#000000',
                        fontSize: 11,
                        },
                    },
                },

                yAxis: {
                    name:'单位(件)',
                    nameTextStyle: {
                        color: "#000000",
                        fontSize: 12,
                    },
                    type: "value",
                    axisLine: {
                        show: false,
                    lineStyle: {
                        color: "#000000",
                    },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: "rgba(255,255,255,0.3)",
                        },
                    },
                    axisLabel: {},
                },
                series: [
                // {
                //     name: "需求数量",
                //     type: "bar",
                //     barWidth: "15%",
                //     itemStyle: {
                //         normal: {
                //         color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //             {
                //             offset: 0,
                //             color: "#248ff7",
                //             },
                //             {
                //             offset: 1,
                //             color: "#6851f1",
                //             },
                //         ]),
                //         },
                //     },
                //     data: [400, 600, 700, 700, 1000, 400, 400, 600, 700],
                //     },
                    {
                        name: "数量",
                        type: "bar",
                        barWidth: 15,
                        itemStyle: {
                            normal: {
                                borderWidth:2,
                                borderColor: "rgba(26,205,253,1)",
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                offset: 0,
                                color: "rgba(26,205,253,1)",
                                },
                                {
                                offset: 1,
                                color: "rgba(26,205,253,.1)",
                                },
                            ]),
                            },
                        },
                        data: yData,
                    },
                    
                ],
            };


            myChart.setOption(option);
        }
    }
}

</script>
<style>
</style>