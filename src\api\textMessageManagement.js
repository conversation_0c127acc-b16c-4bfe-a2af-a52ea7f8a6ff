/**
 *  短信管理相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";
// 短信管理模拟数据
export default {
    // 列表
    async getTextMessageManagementList(params = {}) {
        return request({
            url: '/ds/smsTask/queryList',
            method: 'get',
            params: params
        })

    },
    // 删除
    async removeTextMessageManagement(params = {}) {
        return request({
            url: '/ds/smsTask/deleteById',
            method: 'post',
            data: params
        })

    },
    // 关联事件
    async associatedEvent(params = {}) {
        return request({
            url: '/ds/smsTask/associatedEvent',
            method: 'post',
            data: params
        })

    },
    // 转发
    async forward(params = {}) {
        return request({
            url: '/ds/smsTask/forward',
            method: 'post',
            data: params
        })

    },
    // 详情
    async queryGroupInfoById(params = {}) {
        return request({
            url: '/ds/smsTask/queryGroupInfoById',
            method: 'post',
            data: params
        })

    },
    // 重新发送
    async resend(params = {}) {
        return request({
            url: '/ds/smsTask/resend',
            method: 'post',
            data: params
        })

    },
    // 取消关联事件
    async cancelEvent(params = {}) {
        return request({
            url: '/ds/smsTask/cancelEvent',
            method: 'post',
            data: params
        })

    }
};
