<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item v-if="styleType != 4" label="名称" prop="name">
          <el-input
            :disabled="styleType === 3"
            v-model="formData.name"
            autocomplete="off"
            placeholder="请输入事件类型名称"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="类型代码" prop="code">
          <el-input
            :disabled="styleType === 3"
            v-model="formData.code"
            autocomplete="off"
            placeholder="请输入事件类型代码"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="上级类型" prop="parentId">
          <el-cascader
            :disabled="styleType != 1"
            v-model="formData.parentId"
            :options="parentEmergencyTree"
            :props="cascaderProps"
            :show-all-levels="true"
            clearable
            filterable
            placeholder="请选择上级类型"
            style="width: 100%"
            @change="handleParentMenuChange"
          />
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="排序" prop="sortOrder">
          <el-input
            :disabled="styleType === 3"
            v-model="formData.sortOrder"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="number"
            @input="handlePhoneInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="备注" prop="remark">
          <el-input
            :disabled="styleType === 3"
            v-model="formData.remark"
            placeholder="请输入"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5}"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType === 4" label="周边情况" prop="resClassIds">
          <el-cascader
            v-model="resClassIds"
            :options="nearResourceList"
            :props="nearResourceProps"
            :show-all-levels="true"
            clearable
            filterable
            placeholder="请选择周边资源"
            style="width: 100%"
            @change="handleNearResChange"
          />
        </el-form-item>

        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { systemConfigApi } from "@/api";

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 1新增; 2修改; 3查看
      
      formData: {
        name: "",
        code: "",
        parentId: "",
        sortOrder: "",
        remark: "",
      },
      // 级联选择器配置
      cascaderProps: {
        value: "id",
        label: "name",
        children: "children",
        checkStrictly: true, // 允许选择任意级别
        emitPath: false
      },
      parentEmergencyTree: [], // 级联选择器的树形数据

      // 周边资源
      nearResourceProps: {
        multiple: true,
        value: "id",
        label: "name",
        children: "children",
        // checkStrictly: true, 
        emitPath: false
      },
      nearResourceList: [], 
      // 已经选择的周边资源情况集合
      resClassIds: [],
      currentEventTypeId: "",

      rules: {
        name: [
          { required: true, message: "事件类型名称不能为空", trigger: "blur" },
        ],
        code: [
          { required: true, message: "类型代码不能为空", trigger: "blur" },
        ],
        // parentId: [
        //   { required: true, message: "上级类型不能为空", trigger: "blur" },
        // ],
        // path: [
        //   { required: true, message: "访问路径不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  mounted() {},
  methods: {
    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (!this.formData.parentId || this.formData.parentId.length === 0) {
            this.formData.parentId = "0";
          }
          if (this.styleType === 1) {
            const res = await systemConfigApi.createEmergencyType(this.formData);
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("新增成功");
              this.$emit("ok");
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          } else if (this.styleType === 2) {
            const res = await systemConfigApi.updateEmergencyType(this.formData);
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("修改成功");
              this.$emit("ok");
              this.close();
            } else {
              this.$message.error(error);
            }
          } else if (this.styleType === 4) {
            const params = {
              eventTypeId: this.currentEventTypeId,
              resClassIds: this.resClassIds,
            };
            const res = await systemConfigApi.saveMapResource(params);
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("绑定成功");
              this.$emit("ok");
              this.close();
            } else {
              this.$message.error(error);
            }
          } else {
            this.close();
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.reset();
      this.$emit("ok", 1);
    },
    reset() {
      this.formData = {
        name: "",
        code: "",
        parentId: "",
        sortOrder: "",
        remark: "",
      };
      this.model = {};
      this.parentEmergencyTree = [];
      this.parentMenuList = [];
      this.parentMenuTree = [];
    },
    addFormFn() {
      this.reset();
      this.styleType = 1;
      this.getEmergencyTypeList();
    },
    edit(data) {
      this.styleType = 2;
      this.getEmergencyTypeList();
      this.getEmergencyTypeDetail(data.id);
    },
    review(data) {
      this.styleType = 3;
      this.getEmergencyTypeList();
      this.getEmergencyTypeDetail(data.id);
    },

    async getEmergencyTypeDetail(id) {
      const res = await systemConfigApi.queryEmergencyTypeById({id});
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.formData = data;
    },

    async getEmergencyTypeList() {
      const res = await systemConfigApi.queryEmergencyTypeList();
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.parentEmergencyTree = data;
    },
    handlePhoneInput(value) {
      // 只允许输入数字 使用正则表达式移除非数字字符
      // this.formData.sort = value.replace(/\D/g, "");
    },
    
    // 级联选择器值变化处理
    handleParentMenuChange(value) {
      // debugger
      // this.formData.parentId = value[value.length - 1];
      // 更新parentMenuModel以保持兼容性
      // if (value && value.length > 0) {
      //   const selectedId = value[value.length - 1];
      //   this.formData.parentMenuModel = { id: selectedId };
      // } else {
      //   this.formData.parentMenuModel = null;
      // }
    },

    bindRes(row) {
      this.styleType = 4;
      this.currentEventTypeId = row.id;
      this.getNearResourceList();
      this.getBindNearResource(row.id);
    },

    // 获取已经绑定的周边资源情况
    async getBindNearResource(id) {
      this.resClassIds = [];
      const res = await systemConfigApi.queryResClassEventTypeRefInfo(id);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.resClassIds = data.resClassIds;
    },

    // 获取所有资源分类和资源列表
    async getNearResourceList() {
      this.nearResourceList = [];
      const res = await systemConfigApi.queryClassResList();
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.nearResourceList = data;
    },

    handleNearResChange(value) {
      // debugger
      console.log('xc-------',value);
      
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
