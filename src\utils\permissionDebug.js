/**
 * 权限调试工具
 * 用于调试和分析权限数据
 */
import permissionManager from "./permissionManager";

/**
 * 调试权限数据
 */
export function debugPermissions() {
  console.group("🔐 权限系统调试信息");

  // 获取权限统计
  const stats = permissionManager.getPermissionStats();
  console.log("📊 权限统计:", stats);

  // 导出所有权限数据
  const permissions = permissionManager.exportPermissions();
  console.log("📋 路由权限:", permissions.routes);
  console.log("🔘 按钮权限:", permissions.buttons);
  console.log("✅ 用户权限:", permissions.userPermissions);

  console.groupEnd();
}

/**
 * 测试特定权限
 * @param {string} permission - 权限标识
 * @param {string} parentPath - 父级路径
 */
export function testPermission(permission, parentPath = "") {
  console.group(`🧪 测试权限: ${permission}`);

  const { hasPermission } = require("./permissionManager");
  const result = hasPermission(permission, parentPath);

  console.log(`权限标识: ${permission}`);
  console.log(`父级路径: ${parentPath}`);
  console.log(`检查结果: ${result ? "✅ 有权限" : "❌ 无权限"}`);

  // 显示可能的匹配键
  const possibleKeys = [
    permission,
    `${parentPath}:${permission}`,
    `${parentPath}:${permission.split(":").pop()}`,
  ];
  console.log("🔍 尝试的匹配键:", possibleKeys);

  // 检查每个键是否存在
  const permissions = permissionManager.exportPermissions();
  possibleKeys.forEach((key) => {
    const exists = permissions.buttons[key] !== undefined;
    console.log(`  ${key}: ${exists ? "✅ 存在" : "❌ 不存在"}`);
  });

  console.groupEnd();
}

/**
 * 分析按钮权限数据
 * @param {Array} buttonData - 按钮数据
 */
export function analyzeButtonData(buttonData) {
  console.group("🔘 按钮权限数据分析");

  if (!Array.isArray(buttonData)) {
    console.warn("按钮数据不是数组:", buttonData);
    console.groupEnd();
    return;
  }

  console.log(`按钮总数: ${buttonData.length}`);

  // 按权限字段分组
  const byPermission = {};
  const byParentPath = {};

  buttonData.forEach((button) => {
    const permission = button.permission || "无权限字段";
    const parentPath = button.parentPath || "无父路径";

    if (!byPermission[permission]) byPermission[permission] = [];
    if (!byParentPath[parentPath]) byParentPath[parentPath] = [];

    byPermission[permission].push(button);
    byParentPath[parentPath].push(button);
  });

  console.log("按权限标识分组:", byPermission);
  console.log("按父路径分组:", byParentPath);
  console.groupEnd();
}

/**
 * 调试页面级权限检查
 * @param {string} permission - 权限标识
 * @param {string} currentPagePath - 当前页面路径
 */
export function debugPagePermission(permission, currentPagePath) {
  console.group(`🔍 页面级权限调试: ${permission} @ ${currentPagePath}`);

  const {
    default: permissionManager,
    getPageButtons,
  } = require("./permissionManager");

  // 获取当前页面的按钮权限
  const pageButtons = getPageButtons(currentPagePath);
  console.log("当前页面按钮权限:", pageButtons);

  // 检查全局权限
  const hasGlobalPermission = permissionManager.hasButtonPermission(permission);
  console.log("全局权限检查结果:", hasGlobalPermission);

  // 检查页面级权限
  const hasPagePermission = permissionManager.hasPageButtonPermission(
    permission,
    currentPagePath
  );
  console.log("页面级权限检查结果:", hasPagePermission);

  // 分析匹配的按钮
  const matchingButtons = pageButtons.filter((button) => {
    return (
      button.permission === permission ||
      button.path === permission ||
      button.name === permission
    );
  });
  console.log("匹配的按钮:", matchingButtons);

  console.groupEnd();
}

/**
 * 在控制台显示权限帮助信息
 */
export function showPermissionHelp() {
  console.group("📖 权限系统使用帮助");

  console.log("🔧 调试方法:");
  console.log("  debugPermissions() - 显示权限系统概览");
  console.log("  testPermission(permission, parentPath) - 测试特定权限");
  console.log("  analyzeButtonData(buttonData) - 分析按钮权限数据");

  console.log("\n🎯 权限检查方式:");
  console.log("  1. 指令方式: v-permission=\"'menu:add'\"");
  console.log("  2. 方法方式: hasPermission('menu:add')");
  console.log("  3. 混入方式: this.hasPermission('menu:add')");

  console.log("\n📝 权限标识格式:");
  console.log('  - 直接权限: "menu:add", "user:edit"');
  console.log('  - 路径权限: "/system/userManagement"');
  console.log('  - 操作权限: "add", "edit", "delete"');

  console.groupEnd();
}

// 将调试方法挂载到 window 对象，方便在控制台使用
if (typeof window !== "undefined") {
  window.debugPermissions = debugPermissions;
  window.testPermission = testPermission;
  window.analyzeButtonData = analyzeButtonData;
  window.showPermissionHelp = showPermissionHelp;
}
