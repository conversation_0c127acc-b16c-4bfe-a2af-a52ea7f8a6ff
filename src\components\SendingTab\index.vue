<template>
  <div class="sending-tab">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.id"
        :label="item.label"
        :name="item.name"
      >
        <component
          v-if="item.name === activeName"
          :is="item.component"
          :componentProps="componentPropsData"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SendingSms from "./SendingSms/index.vue"; // 发送短信
import SendingGb from "./SendingGb/index.vue"; // 发送京办
import InfoSynchronous from "./InfoSynchronous/index.vue"; // 信息同步
import InfoTransfer from "./InfoTransfer/index.vue"; // 信息转办
import InfoRecord from "./InfoRecord/index.vue"; // 信息记录
import InfoBack from "./InfoBack/index.vue"; // 信息退报

export default {
  name: "SendingTab",
  components: {
    SendingSms,
    SendingGb,
    InfoTransfer,
    InfoSynchronous,
    InfoRecord,
    InfoBack,
  },
  props: {
    componentPropsData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeName: "sendingSms",
      tabList: [
        {
          id: 1,
          label: "发送短信",
          name: "sendingSms",
          component: "SendingSms",
        },
        {
          id: 2,
          label: "发送京办",
          name: "sendingGb",
          component: "SendingGb",
        },
        {
          id: 3,
          label: "信息同步",
          name: "infoSynchronous",
          component: "InfoSynchronous",
        },
        {
          id: 4,
          label: "信息转办",
          name: "infoTransfer",
          component: "InfoTransfer",
        },
        {
          id: 5,
          label: "信息记录",
          name: "infoRecord",
          component: "InfoRecord",
        },
        {
          id: 6,
          label: "信息退报",
          name: "infoBack",
          component: "InfoBack",
        },
      ],
    };
  },
  mounted() {},
  methods: {
    handleClick(tab) {
      console.log(tab);
    },
  },
};
</script>

<style scoped lang="scss">
.sending-tab {
  ::v-deep {
    .el-tabs {
      padding: 20px 0 20px 20px;
    }
    .el-tabs__header {
      margin-right: 20px;
    }
    .el-tabs__content {
      padding-right: 20px;
      height: calc(100vh - 195px);
      overflow: auto;
    }

    .add-form {
      padding: 0 10px 0 10px;
    }
  }
}
</style>
