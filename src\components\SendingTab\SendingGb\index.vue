<template>
  <!-- 发送京办 -->
  <div class="sending-gb">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-position="top"
      label-width="100px"
    >
      <el-form-item label="京办群" prop="matter">
        <el-cascader
          class="matter-cascader"
          v-model="form.matter"
          :options="options"
          :props="cascaderProps"
          :show-all-levels="false"
          clearable
        ></el-cascader>
      </el-form-item>
      <el-form-item label="事件标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入事件标题"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item label="发送内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请输入发送内容"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item label="调度指令" prop="command">
        <el-input
          v-model="form.command"
          type="textarea"
          :rows="6"
          placeholder="请输入调度指令"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { orgApi, systemManagementApi } from "@/api";
import assistantAI from "@/api/assistantAI";

export default {
  name: "SendingGb",
  props: {
    componentProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        //京办群
        matter: "",
        //事件标题
        title: "",
        //发送内容
        content: "",
        //调度指令
        command: "",
      },
      rules: {
        matter: [{ required: true, message: "请输入京办群", trigger: "blur" }],
        title: [{ required: true, message: "请输入事件标题", trigger: "blur" }],
        content: [
          { required: true, message: "请输入发送内容", trigger: "blur" },
        ],
        command: [
          { required: true, message: "请输入调度指令", trigger: "blur" },
        ],
      },

      options: [], //queryOrg
      nodeMap: new Map(),
      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        multiple: true,
        checkStrictly: true, // 允许选择任意级
        lazy: true, // 启用懒加载
        emitPath: false,
        lazyLoad: this.loadChildren,
      },
    };
  },
  watch: {
    componentProps: {
      handler(newVal, oldVal) {
        console.log(this.componentProps);
        this.getSendMessage();
        this.form.title = newVal.infoTitle;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map((item) => ({
            ...item,
            disabled:
              item.type === 0 && item.children && item.children.length === 0, // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({
          orgId: data?.id || "0",
        });
        const nodes = response.data.map((item) => {
          this.nodeMap.set(item.id, item); // 存储映射关系
          // 用户节点
          if (item.type === 1) {
            return {
              ...item,
              orgName: item.userName || "未命名用户",
              isLeaf: true,
            };
          }
          // 部门节点
          return {
            ...item,
            //disabled: item.type === 0 && (!item.children || item.children.length === 0) // 空部门禁用
          };
        });
        resolve(nodes);
      } catch (error) {
        console.error("加载子节点失败:", error);
        resolve([]);
      }
    },

    //获取发送内容
    async getSendMessage() {
      const { data } = await assistantAI.querySendMessage({
        id: this.componentProps.id,
      });
      this.form.content = data;
    },

    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          let params = {
            title: this.form.title,
            toUsers: this.form.matter,
            bodyFields: [
              {
                name: "事件内容",
                content: this.form.content,
              },
              {
                name: "事件调度指令",
                content: this.form.command,
              },
            ],
          };
          const res = await orgApi.sendJbUserKP(params);
          const { code, error } = res;
          if (code === 0) {
            this.$message({
              message: "京办发送成功",
              type: "success",
            });
          } else {
            this.$message({
              message: error,
              type: "error",
            });
          }
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      matter: "",
      title: "",
      content: "",
      command: "",
    };
  },
};
</script>

<style scoped lang="scss">
.matter-cascader {
  width: 100%;
  height: 100px;
  ::v-deep {
    .el-input {
      height: 100%;
      input {
        height: 100% !important;
      }
    }

    .el-cascader__tags {
      top: 5px;
      transform: none;
    }
  }
}
</style>
