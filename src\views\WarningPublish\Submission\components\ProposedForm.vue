<template>
    <general-dialog :dialog-visible="dialogVisible" general-dialog-title="拟办" dialog-width="800px" @cancel="handleClose"
        @confirm="handleSubmit">
        <div style="padding: 30px;">
            <el-timeline class="el-review-timeline">
                <el-timeline-item timestamp="2018/4/12" placement="top">
                    <div class="timeline-card">
                        <div class="timeline-card-item">机构名称：北京市空气重污染急急指挥部办公室</div>
                        <div class="timeline-card-item">
                            <p>操作人：张三</p>
                            <p class="timeline-card-item-status">操 作：提交审核</p>
                        </div>

                        <div class="timeline-card-item">审核意见：关于发布本市空气重污染橙色预警的请示</div>
                    </div>
                </el-timeline-item>
                <el-timeline-item timestamp="2018/4/3" placement="top">
                    <div class="timeline-card">
                        <div class="timeline-card-item">机构名称：北京市空气重污染急急指挥部办公室</div>
                        <div class="timeline-card-item">
                            <p>操作人：张三</p>
                            <p class="timeline-card-item-status">操 作：提交审核</p>
                        </div>

                        <div class="timeline-card-item">审核意见：关于发布本市空气重污染橙色预警的请示</div>
                    </div>
                </el-timeline-item>
            </el-timeline>
            <el-form ref="form" class="add-form" :model="form" label-width="110px">
                <el-form-item label="拟办意见" prop="aa">
                    <el-radio-group v-model="form.aa">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">不通过</el-radio>
                    </el-radio-group>
                    <el-input v-model="form.bb" placeholder="请输入拟办意见" type="textarea" class="content_textarea" resize="none" />
                </el-form-item>
                
            </el-form>
        </div>

    </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
    components: {
        GeneralDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        formData: {
            type: Object,
            default: () => ({}),
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            submitting: false,
            form: {
                aa: 1,
                bb:''
            }
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
        isEditState: {
            get() {
                return this.isEdit;
            },
            set(val) {
                this.$emit("update:isEdit", val);
            },
        },
    },
    watch: {
        visible(val) {
            if (val) {
                // this.initForm();
                // // this.$nextTick(() => {
                // //     this.resetForm();
                // // })
            }
        },
        formData: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.form = { ...this.form, ...val };
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    this.submitting = true;
                    try {
                        // const formData = { ...this.form };
                        // if (this.isEdit) {
                        //     formData.id = this.formData.id;
                        // }
                        // this.$emit("submit", formData, this.isEdit);
                    } finally {
                        this.submitting = false;
                    }
                } else {
                    this.$message.error("请检查表单填写是否正确");
                }
            });
        },
        handleClose() {
            this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.dialogVisible = false;
            }).catch(() => {
            });
        },
    }
}
</script>

<style scoped lang='scss'>
::v-deep(.el-review-timeline) {
    overflow-y: auto;
    max-height: 450px;
    padding-top: 5px;
    .timeline-card {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        background: #fff;
        padding: 10px;

        .timeline-card-item {
            display: flex;

            p {
                width: 50%;
            }
        }
    }

    .el-timeline-item__tail {
        background: #507ac2;
        border-left: 2px solid #507ac2;
    }

    .el-timeline-item__node {
        background-color: #507ac2;
    }

    .el-timeline-item__timestamp {
        margin-bottom: 7px;
        padding-top: 0px;
        font-size: 16px;
        color: #313131;
    }

    .timeline-card-item-status {
        color: #0057ec;
    }
}
.add-form{
    padding: 0px;
}
</style>