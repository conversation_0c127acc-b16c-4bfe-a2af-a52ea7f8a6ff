/**
 * 预警信息发布相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";
export default {
  // 查询预警信息列表分页
  async getWarningPublishList(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/queryWarningInfoList',
      method: 'post',
      data: params
    })
  },
  // 添加预警信息
  async createWarningPublish(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/createWarningInfo',
      method: 'post',
      data: params
    })
  },
  // 编辑预警信息
  async updateWarningPublish(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/updateWarningInfo',
      method: 'post',
      data: params
    })
  },
  // 删除预警信息
  async removeWarningPublish(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/deleteWarningInfo',
      method: 'post',
      data: params
    })
  },
   // 获取预警信息详情
  async queryWarningPublish(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/queryWarningInfoById',
      method: 'post',
      data: params
    })
  },
 

  // 获取北京区/县和街道接口
  async queryRegion(params = {}) {
    return request({
      url: '/ds/townsBeijing/query',
      method: 'post',
      data: params
    })
  },

  // 发布申请
  async submitApplication(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/submitApplication',
      method: 'post',
      data: params
    })
  },
  // 查询审批流程记录
  async queryProcess(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/queryProcess',
      method: 'post',
      data: params
    })
  },
  // 预警信息拟办
  async proposed(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/proposed',
      method: 'post',
      data: params
    })
  },
  // 预警信息提交报批
  async submitForApproval(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/submitForApproval',
      method: 'post',
      data: params
    })
  },
  // 预警信息审批
  async approve(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/approve',
      method: 'post',
      data: params
    })
  },
  // 预警事件统计表
  async queryNumberByAreaCode(params = {}) {
    return request({
      url: '/ds/earlyWarning/statistics/queryNumberByAreaCode',
      method: 'post',
      data: params
    })
  },
  // 预警信息发布(最后一步)
  async publish(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/publish',
      method: 'post',
      data: params
    })
  },
  // 智能辅助查询
  async assistantQuery(params = {}) {
    return request({
      url: '/ds/earlyWarning/access/assistantQuery',
      method: 'post',
      data: params
    })
  },

};
