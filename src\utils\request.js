import axios from "axios";
import { Message, MessageBox } from "element-ui";
import auth from "@/utils/auth";
import logoutUtils from "@/utils/logoutUtils";

// 认证错误消息常量
const AUTH_ERROR_MESSAGES = [
  "暂未登录或token已失效或无操作自动退出",
  "暂未登录",
  "token已失效",
  "无操作自动退出",
  "登录状态已过期",
  "未授权，请登录",
];

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 30000,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = auth.getToken();
    if (token) {
      const tokenType = auth.getTokenType();
      config.headers["Authorization"] = `${tokenType} ${token}`;

      // 只有在没有明确设置Content-Type且不是FormData时才设置默认的Content-Type
      if (
        !config.headers["Content-Type"] &&
        !(config.data instanceof FormData)
      ) {
        config.headers["Content-Type"] = "application/json;charset=utf-8";
      }
    }
    // get请求时添加一个data参数，解决get请求无法设置content-type的问题
    if (config.method === "get") config.data = { unused: 0 };
    // 如果data不存在，则添加一个参数用来设置content-type
    if (!config.data) config.data = { unused: 0 };
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    if (response.config.responseType === "blob") {
      return response;
    }

    if (res.code !== undefined && res.code !== 0) {
      // 检查是否是认证相关错误
      const isAuthError =
        res.code === 401 ||
        (res.message && AUTH_ERROR_MESSAGES.includes(res.message));

      if (isAuthError) {
        // console.log("检测到认证失效，立即清除本地数据:", res.message);
        auth.clearAuth();

        // 避免重复弹窗
        if (!window.isShowingAuthDialog) {
          window.isShowingAuthDialog = true;
          MessageBox.confirm(
            "登录状态已过期，您可以继续留在该页面，或者重新登录",
            "确定登出",
            {
              confirmButtonText: "重新登录",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              logoutUtils.performLogout(false, false).then(() => {
                window.isShowingAuthDialog = false;
                location.reload();
              });
            })
            .catch(() => {
              window.isShowingAuthDialog = false;
            });
        }
      } else {
        // 非认证错误才显示错误消息
        Message({
          message: res.message || "Error",
          type: "error",
          duration: 2000,
          showClose: true,
        });
      }

      return Promise.reject(new Error(res.message || "Error"));
    } else {
      return res;
    }
  },
  (error) => {
    let message = error.message;
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = "请求错误";
          break;
        case 401:
          message = "未授权，请登录";
          // 避免重复处理401错误
          if (!window.isShowingAuthDialog) {
            window.isShowingAuthDialog = true;
            setTimeout(() => {
              const { logoutUtils } = require("@/utils");
              logoutUtils.performLogout(true, false).then(() => {
                window.isShowingAuthDialog = false;
                location.reload();
              });
            }, 1000);
          }
          break;
        case 403:
          message = "拒绝访问";
          break;
        case 404:
          message = "请求地址出错";
          break;
        case 408:
          message = "请求超时";
          break;
        case 500:
          message = "服务器内部错误";
          break;
        case 501:
          message = "服务未实现";
          break;
        case 502:
          message = "网关错误";
          break;
        case 503:
          message = "服务不可用";
          break;
        case 504:
          message = "网关超时";
          break;
        case 505:
          message = "HTTP版本不受支持";
          break;
        default:
          message = `连接错误${error.response.status}`;
      }
    } else if (error.code === "ECONNABORTED") {
      message = "请求超时";
    } else if (error.message.includes("Network Error")) {
      message = "网络连接异常";
    }

    Message({
      message,
      type: "error",
      duration: 2000,
      showClose: true,
    });

    return Promise.reject(error);
  }
);

export default service;
