const state = {
  // 联系人列表
  contactList: [],
  // 组织架构树
  orgTree: [],
  // 分页信息
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  // 搜索条件
  searchParams: {},
  // 当前选中的组织
  selectedOrg: null,
  // 加载状态
  loading: false,
};

const mutations = {
  SET_CONTACT_LIST: (state, list) => {
    state.contactList = list || [];
  },
  SET_ORG_TREE: (state, tree) => {
    state.orgTree = tree;
  },
  SET_PAGINATION: (state, pagination) => {
    state.pagination = { ...state.pagination, ...pagination };
  },
  SET_SEARCH_PARAMS: (state, params) => {
    state.searchParams = params;
  },
  SET_SELECTED_ORG: (state, org) => {
    state.selectedOrg = org;
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading;
  },
};

import { contactApi, orgApi } from "@/api";

const actions = {
  // 获取联系人列表
  async getContactList({ commit, state }, params = {}) {
    console.log("Store: 开始获取联系人列表", {
      currentPage: state.pagination.current,
      pageSize: state.pagination.pageSize,
      searchParams: state.searchParams,
      additionalParams: params,
    });

    commit("SET_LOADING", true);
    try {
      const requestParams = {
        page: state.pagination.current,
        pageSize: state.pagination.pageSize,
        ...state.searchParams,
        ...params,
      };

      const response = await contactApi.getList(requestParams);
      commit("SET_CONTACT_LIST", response.data.list);
      commit("SET_PAGINATION", {
        total: response.data.total,
        current: response.data.current,
      });
      commit("SET_LOADING", false);
      return (
        response?.data || {
          list: state.contactList,
          total: state.pagination.total,
        }
      );
    } catch (error) {
      commit("SET_LOADING", false);
      throw error;
    }
  },

  // 获取组织架构树
  async getOrgTree({ commit }) {
    try {
      const response = await orgApi.queryOrgTree();
      commit("SET_ORG_TREE", response.data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // 搜索联系人
  async searchContacts({ commit }, params) {
    commit("SET_SEARCH_PARAMS", params);
    commit("SET_LOADING", true);
    try {
      const response = await contactApi.search(params.keyword, params);
      commit("SET_CONTACT_LIST", response.data.list || response.data);
      commit("SET_PAGINATION", {
        total: response.data.total || response.data.length,
      });
      commit("SET_LOADING", false);
    } catch (error) {
      commit("SET_LOADING", false);
      throw error;
    }
  },
};

const getters = {};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
