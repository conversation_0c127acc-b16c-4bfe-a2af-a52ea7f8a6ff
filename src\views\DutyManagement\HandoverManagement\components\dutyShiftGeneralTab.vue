<!-- 值班值守人员 - Page -->
<template>

  <div class="shift-change-container" :style="{height: heightTable+'px'}">
    <!-- <div class="warning-header">
      <h2>气象预警信息</h2>
    </div> -->
    
    <div v-if="parseInt(shiftType) < 6" class="warning-content"> 
      {{ contentData.content }} 
    </div>

    <div v-if="parseInt(shiftType) > 5" class="warning-content"> 
      <el-input
        type="textarea"
        :rows="25"
        placeholder="请输入内容"
        v-model="contentData.content">
      </el-input>
    </div>

    <!-- 风险分析表格 -->
     <portal-table
      v-if="parseInt(shiftType) === 2"
      style="padding: 0 20px"
      :showAddButton="false"
      :showSelection="false"
      :show-pagination="false"
      :isTableBorder="true"
      :columns="columns"
      :table-data="tableData"
      row-key="name"
    />
    
    <div class="warning-footer">
      {{ contentSource }}
    </div>

    <el-button class="save-button"
      v-if="parseInt(shiftType) > 5"
      type="primary"
      size="small"
      @click="handleSaveClick()"
    >
      保存
    </el-button>
  </div>
  
</template>

<script>

import PortalTable from "@/components/PortalTable/index.vue";
import { dutyManagementApi } from "@/api";

export default {
  name: "dutyShiftGeneralTab",
  components: {
    PortalTable,
  },
  data() {
    return {
			heightTable: 700,
			contentData: {
        id: "",
        dat: "",
        shiftType: "",
        content: "",
        // content: "6月15日白天，阴，有阵雨转多云，偏北风3级左右，阵风6级（西部北部7、8级），最高气温29℃；\n夜间，多云转阴，大部地区有雷阵雨或阵雨，偏北风2、3间4级，最低气温19℃。\n市水务局与市气象局于6月14日10时发布积水内涝蓝色预警，19时解除积水内涝蓝色预警。\n市气象局于6月14日19时09:00解除暴雨蓝色预警。",
      },
      shiftType: "",
      // content: "",
      contentSource: "",
      // editContent: "",
      columns: [
        { prop: "eventTime", label: "活动日期", text: true },
        { prop: "eventName", label: "活动名称", text: true },
        { prop: "eventLocation", label: "活动地点", text: true },
        { prop: "enentType", label: "活动类型", text: true, width: 150 },
        { prop: "eventNum", label: "预计客流（人次）", text: true, width: 150 },
      ],
      tableData: [
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        },
        {
          eventTime: "4月20日至10月28日\n（每周一闭馆，节假日除外，10时至17时）",
          eventName: "“如是莫高”敦煌艺术大展“莫高精神”红色主题展",
          eventLocation: "北京展览馆报告厅2号馆二层",
          enentType: "展览展销",
          eventNum: "2500",
        }
      ],
    };
  },
	mounted() {
		const gaping = 50 + 40 + 12 + 20 + 45 + 50;
		this.heightTable = window.innerHeight - gaping;
	},
  methods: {
    formattedContent() {
      // 将换行符转换为<br>标签
      return this.weatherWarning.content.replace(/\n/g, '<br>');
    },
		loadData(shiftType) {
      this.shiftType = shiftType;
      this.setContentSource(shiftType);
			dutyManagementApi.queryShiftData(shiftType).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        // if (parseInt(shiftType) > 5) {
        //   this.editContent = data?.content;
        // } else {
        //   this.content = data?.content;
        // }
        this.contentData = data;

        if (this.shiftType === '2') {
           this.contentData.content = "今日全市大型活动共7项，需重点关注各项大型活动举办地点周边交通管控和现场秩序";
        }
      });
		},
    
    handleSaveClick() {
      // 保存编辑的内容
      // this.contentData.content = this.editContent;
      this.contentData.shiftType = this.shiftType;
      dutyManagementApi.updateShiftData(this.contentData).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success('保存成功');
      });
    },

    setContentSource(shiftType) {
      switch (shiftType) {
        case '1':
          this.contentSource = "数据来源于市气象局的预警信息";
          break;
        case '2':
          this.contentSource = "数据来源于市公安局";
          break;
        case '3':
          this.contentSource = "数据来源于会议通知内容";
          break;
        case '4':
          this.contentSource = "数据来源于领导批示内容";
          break;
        case '5':
          this.contentSource = "数据来源于信息报送管理（事件详情）";
          break;
        case '6':
          this.contentSource = "数据来源于防汛处录入";
          break;
        case '7':
          this.contentSource = "数据来源于防火处录入";
          break;
        case '8':
          this.contentSource = "数据来源于信息保障中心录入";
          break;
        case '9':
          this.contentSource = "数据来源于本系统录入";
          break;
        case '10':
          this.contentSource = "数据来源于本系统录入";
          break;
      
        default:
          break;
      }
    }

  }
};
</script>


<style lang="scss" scoped>
.shift-change-container {
  font-family: "Microsoft YaHei", SimHei, sans-serif;
  // padding: 20px;
  // max-width: 800px;
  width: 100%;
  margin: 0 auto;
  color: #333;
	border: 1px solid var(--themeColor);
	border-radius: 5px;

  overflow-y: auto; 
  padding-right: 10px; 
  scrollbar-width: thin; 
  scrollbar-color: #ccc #f5f5f5; 

  .portal-table {
    .text-ellipsis {
      white-space: pre-line !important; // 保留换行符
    }
  }
}


.warning-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.warning-header h2 {
  color: #333;
  font-size: 20px;
  margin: 0;
}

.warning-content {
  // margin-left: 20px;
  // margin-top: 20px;
  margin: 20px;
  // margin-bottom: 40px;
  line-height: 2.2;
  font-size: 15px;
  color: #444;
  white-space: pre-line; // 保留换行符 

  max-height: calc(100% - 100px);
  overflow-y: auto; 
  padding-right: 10px; 
  
  scrollbar-width: thin; 
  scrollbar-color: #ccc #f5f5f5; 
}

.warning-footer {
  position: absolute;
  bottom: 0px;
  font-size: 14px;
  color: red;
  // width: 300px;
  // padding-top: 10px;
  padding: 0 60px;
  line-height: 40px;
  text-align: center;
  border-top: 1px solid #444;
  border-right: 1px solid #444;
  // border: 1px solid #444;
  // border-radius: 5px;
  border-bottom-left-radius: 5px;
}

.save-button {
  position: absolute;
  bottom: 10px;
  right: 20px;
  font-size: 14px;
}

</style>