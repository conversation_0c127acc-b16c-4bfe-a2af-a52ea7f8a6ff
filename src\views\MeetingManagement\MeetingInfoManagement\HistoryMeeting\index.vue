<!-- 历史会议---HistoryMeeting -->
<template>
  <div class="user-index-container">
    <portal-table
      v-if="showMeetingType === 1"
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="会议名称" prop="meetingName">
          <el-input :disabled="true" style="width: 281px;" v-model="form.meetingName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="会议日期" prop="meetingDate">
          <el-date-picker
            :disabled="true"
            v-model="form.meetingDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeMeetingDate">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="调试时间" prop="deTime">
          <el-time-picker
            :disabled="true"
            style="width: 281px;"
            v-model="form.deTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择调试时间">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="召开时间" prop="holdTime">
          <el-time-picker
            :disabled="true"
            style="width: 281px;"
            v-model="form.holdTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择召开时间">
          </el-time-picker>
        </el-form-item>

        <el-form-item label="报备截止时间" prop="deadlineDate">
          <el-date-picker
            :disabled="true"
            v-model="form.deadlineDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeDeadline">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否使用会控" prop="meetingControls">
          <el-select :disabled="true" v-model="form.meetingControls" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议周期" prop="meetingCycle">
          <el-select :disabled="true" v-model="form.meetingCycle" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingCycleList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="会议终端类型" prop="meetingTerminalType">
          <el-select :disabled="true" v-model="form.meetingTerminalType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTerminalTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议类别" prop="meetingType">
          <el-select :disabled="true" v-model="form.meetingType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="主会场信息" prop="venueInfo">
          <el-select :disabled="true" v-model="form.venueInfo" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueInfoList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主会场需求" prop="venueRequirement">
          <el-select :disabled="true" v-model="form.venueRequirement" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueRequirementList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="本地使用终端" prop="locallyTerminal">
          <el-select :disabled="true" v-model="form.locallyTerminal" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in locallyTerminalList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议级别" prop="meetingLevel">
          <el-select :disabled="true" v-model="form.meetingLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分会场发言" prop="subVenueSpeeches">
          <el-select :disabled="true" v-model="form.subVenueSpeeches" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in subVenueSpeechesList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会单位" prop="attendUnitList">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 600px;"
            v-model="form.attendUnitList"
            :options="orgTree"
            :props="props"
            :disabled="true"
            :collapse-tags="styleType === 1"
            clearable
            :show-all-levels="false"
            @change="handleAttendUnitChange">
          </el-cascader>
        </el-form-item>


        <el-form-item label="领导级别" prop="leadershipLevel">
          <el-select :disabled="true" v-model="form.leadershipLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in leadershipLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会领导" prop="attendLeader">
          <el-input :disabled="true" style="width: 281px;" v-model="form.attendLeader" placeholder="请输入" />
        </el-form-item>


        <el-form-item label="承办单位" prop="organUnit">
          <el-cascader
            style="width: 281px;"
            v-model="form.organUnit"
            :options="orgTree"
            :props="organUnitProps"
            :disabled="true"
            :collapse-tags="styleType === 1"
            clearable
            :show-all-levels="false"
            @change="handleArganUnitChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input :disabled="true" style="width: 281px;" v-model="form.phone" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="保障人员" prop="supportPersonnel">
          <el-select :disabled="true" v-model="form.supportPersonnel" placeholder="请选择" style="width: 600px;">
            <el-option
              v-for="item in supportPersonnelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通知附件" prop="fileList">
          <el-upload
            style="width: 600px;"
            :disabled="true"
            ref="uploadRef"
            class="upload-demo"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :http-request="uploadFile"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
          </el-upload>
        </el-form-item>

        <el-form-item label="会议说明" prop="meetingNotes">
          <el-input 
            :disabled="true" 
            style="width: 600px;" 
            v-model="form.meetingNotes" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input 
            :disabled="true" 
            style="width: 600px;" 
            v-model="form.remark" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>

      </el-form>
    </general-dialog>

    <el-dialog 
      title="预览" 
      :custom-class="'preview-dialog'"
      :visible.sync="showIframe" 
      v-if="showIframe" 
      width="80%"
      :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="100%"></iframe>
    </el-dialog>

    <general-dialog
      :dialog-visible="materialsDialogVisible"
      :dialog-width="'760px'"
      :general-dialog-title="'会议材料'"
      :set-component-name="$store.getters.componentName"
      :show-footer="false"
      @cancel="handleMaterialsCancel"
    >
      <div class="preview-div">
        <div class="preview-list-content" v-for="item in fileList">
          <div class="file-icon-name">
            <img :src="getFileIcon(item.url)" class="file-icon" alt="" />
            <span class="file-icon-name">{{ item.name }}</span>
          </div>
          <div class="button-operate">
            <el-button type="primary" size="mini" @click="handleDownload(item)">
              下载
            </el-button>
            <el-button
              type="primary"
              size="mini"
              @click="handlePreview({url : item.url})"
            >
              预览
            </el-button>
          </div>
        </div>
      </div>
    </general-dialog>

    <reportMeeting 
      ref="reportMeetingRef"
      @buttonBackClick="handleReportBack()"> 
    </reportMeeting>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, orgApi, meetingManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond, getCurrentDate, getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils"; 
import reportMeeting from "@/views/MeetingManagement/MeetingReadyManagement/MeetingNotice/components/reportMeeting.vue"

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
    reportMeeting,
  },
  data() {
    return {
      showMeetingType: 1,
      searchItems: [
        {
          prop: "meetingName",
          label: "会议名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingTime",
          label: "会议日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "meetingDescribe",
          label: "会议说明",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingLoction",
          label: "主会场信息",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "meetingType",
          label: "会议终端类型",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "meetingGuaranteeUser",
          label: "保障人员",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "inspectionType",
          label: "会议类别",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "meetingName", label: "会议名称", text: true },
        { prop: "meetingDate", label: "会议日期", text: true },
        { prop: "holdTime", label: "召开时间", text: true },
        { prop: "deadlineDate", label: "截止时间", text: true },
        { prop: "meetingNotes", label: "会议说明", text: true },
        { prop: "venueInfo", label: "主会场信息", text: true },
        { prop: "meetingTerminalType", label: "会议终端类型", text: true },
        { prop: "supportPersonnel", label: "保障人员", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '260px',
          operationList: [
            {
              label: '详情',
              permission: 'historyMeeting:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '会议纪要',
              permission: 'historyMeeting:minutes',
              buttonClick: this.handleViewMinutes,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '会议材料',
              permission: 'historyMeeting:materials',
              buttonClick: this.handleViewMaterials,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '重新发起',
              permission: 'historyMeeting:againSend',
              buttonClick: this.handleAgainSend,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '回放录播',
              permission: 'historyMeeting:replayer',
              buttonClick: this.handleReplayer,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '报送信息',
              permission: 'historyMeeting:report',
              buttonClick: this.handleApproval,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看

      trueOrFalseTypeList:[
        {itemName: "是", id: 1 },
        {itemName: "否", id: 0 }
      ],
      meetingTerminalTypeList: [
        {itemName: "测试应急终端", id: "测试应急终端"},
        {itemName: "测试指挥终端", id: "测试指挥终端"},
      ],
      venueInfoList: [
        {itemName: "测试主会场1", id: "测试主会场1"},
        {itemName: "测试主会场2", id: "测试主会场2"},
      ],
      venueRequirementList: [
        {itemName: "测试主会场需求1", id: "测试主会场需求1"},
        {itemName: "测试主会场需求2", id: "测试主会场需求2"},
      ],
      locallyTerminalList: [
        {itemName: "172.2.1.10", id: "172.2.1.10"},
        {itemName: "172.2.1.11", id: "172.2.1.11"},
      ],
      meetingLevelList: [
        {itemName: "一类会议", id: "1"},
        {itemName: "二类会议", id: "2"},
        {itemName: "三类会议", id: "3"},
        {itemName: "四类会议", id: "4"},
      ],
      subVenueSpeechesList: [
        {itemName: "市应急指挥中心", id: "市应急指挥中心"},
        {itemName: "丰台区应急指挥中心", id: "丰台区应急指挥中心"},
        {itemName: "西城区应急管理局", id: "西城区应急管理局"},
        {itemName: "东城区应急管理局", id: "东城区应急管理局"},
      ],
      leadershipLevelList: [
        {itemName: "局级", id: "局级"},
        {itemName: "副局级", id: "副局级"},
        {itemName: "处级", id: "处级"},
        {itemName: "副处级", id: "副处级"},
        {itemName: "正科级", id: "正科级"},
        {itemName: "副科级", id: "副科级"},
      ],
      supportPersonnelList: [
        {itemName: "张三（18090900909）", id: "张三（18090900909）"},
        {itemName: "李四（18090900909）", id: "李四（18090900909）"},
        {itemName: "王五（18090900909）", id: "王五（18090900909）"},
        {itemName: "赵六（18090900909）", id: "赵六（18090900909）"},
      ],
      setSendingTimeList: [
        {itemName: "审批后立即发送", id: "0"},
        {itemName: "审批后选择时间发送", id: "1"},
      ],

      meetingCycleList:[
        {itemName: "1天", id: "1"},
        {itemName: "7天", id: "7"},
        {itemName: "15天", id: "15"},
        {itemName: "一个月", id: "30"},
      ],
      noticeMethodTypeList: [
        {itemName: "系统消息", id: "1"},
        {itemName: "短信", id: "2"},
        {itemName: "语音广播", id: "3"},
        {itemName: "京办", id: "4"},
      ],
      meetingReminderList: [
        {itemName: "提前5分钟", id: "5"},
        {itemName: "提前10分钟", id: "10"},
        {itemName: "提前15分钟", id: "15"},
        {itemName: "提前30分钟", id: "30"},
        {itemName: "自定义时间", id: "0"},
      ],
      orgTree: [],
      organUnitProps: {
        multiple: false,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      meetingTypeList: [],

      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "760px",
      generalDialogTitle: "会议详情",

      materialsDialogVisible: false,

      form: {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        sendingTime: "",
        setSendingTime: "",
        fileList:[],
      },
      rules: {
        // inspectionTime: [
        //   {required: true, message: '检查时间不能为空', trigger: 'blur'}
        // ],
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    // this.registerHandlers();
  },
  methods: {
    // registerHandlers() {
    //   this.$store.commit("generalEvent/registerEventHandler", {
    //     type: "add_top",
    //     handler: this.handleAdd,
    //   });
    // },
    handleChangeMeetingDate(value) {
      this.form.meetingDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleChangeDeadline(value) {
      this.form.deadlineDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleArganUnitChange(value) {
      // 选择的承办单位
    },
    handleAttendUnitChange(value) {
      // 选择的参会单位集合
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          this.searchItems[6].options = this.meetingTypeList.map((item) => ({
            label: item.itemName,
            value: item.id
          }))
        }
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionResultList = await getItemList(inspectionResultType);
      //   this.searchItems[2].options = this.inspectionResultList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      meetingManagementApi.queryMeetingNoticeDetail({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        this.form = {
          ...data,
        };
      });
    },

    //新增
    // handleAdd() {
    //   this.styleType = 1;
    //   this.dialogVisible = true;
    //   this.form = {};
    //   this.fileList = [];
    //   this.form.fileList = [];
    //   this.generalDialogTitle = "新增历史会议";
    // },

    //编辑
    // handleEdit(row) {
    //   this.styleType = 2;
    //   this.dialogVisible = true;
    //   this.getRowDataInfo(row);
    //   this.generalDialogTitle = "编辑历史会议";
    // },

    //查看
    handleReview(row) {
      this.queryOrgTreeDataList();
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看历史会议";
    },
    handleViewMaterials(row) {
      this.fileList = [];
      this.form.fileList = [];
      meetingManagementApi.queryMeetingNoticeDetail({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        if (!this.fileList || this.fileList.length === 0) {
          this.$message.warning('此会议暂无会议材料！');
          return;
        }
        if (this.fileList.length > 1 ){
          this.materialsDialogVisible = true;
        }else{
          this.handlePreview({url : this.fileList[0].url});
        }
      });
    },
    handleViewMinutes() {
      this.$message.warning('此会议暂无会议纪要！');
    },
    handleAgainSend() {
      this.$confirm('确定重新发起此会议通知, 是否继续?', '重新发起', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // meetingManagementApi.auditMeeting({ id: row.id, auditFlag: 1 }).then((res) => {
        //   const { code, message, error } = res;
        //   if (code !== 0) return this.$message.error(message || error);
          this.$message.success('重新发起成功！');
        //   this.handSubmitSuccess();
        // })
      })
    },
    handleReplayer() {
      this.$message.warning('此会议暂无会议录播！');
    },
    // 报送信息
    handleApproval(row) {
      this.showMeetingType = 2;
      this.$refs.reportMeetingRef.showReportInfoList(row.id, row.meetingName);
    },
    handleReportBack() {
      this.showMeetingType = 1;
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await meetingManagementApi.queryMeetingHistoryPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.resetFormData();
    },

    handleMaterialsCancel() {
      this.materialsDialogVisible = false;
    },
    
    // 提交表单
    handleSubmit() {
      if (this.styleType === 3) {
        this.handleCancel();
        return;
      }
      // this.$refs.addForm.validate(async (valid) => {
      //   if (valid) {
      //     if (this.styleType === 1) {
      //       const res = await dutyManagementApi.createInspection(this.form);
      //       const {code, error} = res;
      //       if (code === 0) {
      //         this.$message.success('新增成功')
      //         this.handSubmitSuccess();
      //       } else {
      //         this.$message.error(error)
      //       }
      //     } else {
      //       const res = await dutyManagementApi.updateInspection(this.form);
      //       const {code, error} = res;
      //       if (code === 0) {
      //         this.$message.success('修改成功')
      //         this.handSubmitSuccess();
      //       } else {
      //         this.$message.error(error)
      //       }
      //     }
      //   } else {
      //     return false;
      //   }
      // });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.resetFormData();
    },

    resetFormData() {
      this.form = {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        setSendingTime: "",
        sendingTime: "",
        fileList:[],
      };
    },

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(file.url);
      } 
      // window.open(fileUrl, '_blank');
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    handleDownload(row) {
      console.log(row)
      const loading = this.$loading({
        lock: true,
        text: "正在下载...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {

        if (!row.url) {
          this.$message.warning("文件链接无效");
          loading.close();
          return;
        }

        fetch(row.url)
          .then((response) => {
            if (!response.ok)
              throw new Error(`HTTP error! status: ${response.status}`);
            return response.blob();
          })
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = row.name; // 使用服务器返回的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          })
          .catch((error) => {
            console.error("文件下载失败:", error);
            this.$message.error("文件下载失败，请检查文件是否存在");
          })
          .finally(() => {
            loading.close();
          });
      } catch (error) {
        console.error("下载异常:", error);
        loading.close();
        this.$message.error("下载过程中发生错误");
      }
    },

    getFileIcon(url) {
      const extension = url?.split(".").pop().toLowerCase() || "";
      const iconMap = {
        xlsx: "excel",
        xls: "excel",
        doc: "word",
        docx: "word",
        pdf: "pdf",
      };
      return require(`@/assets/images/icons/${
        iconMap[extension] || "unknown"
      }.svg`);
    },

    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}

.preview-div {
  padding: 30px 60px 5px 60px;

  .preview-list-content {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 20px;

    .file-icon-name {
      display: flex;
      align-items: center;
      .file-icon {
        width: 30px;
        height: 30px;
        margin-right: 10px;
      }

      .file-icon-name {
        color: #0a1629;
        font-size: 18px;
        font-weight: 700;
        width: 360px;
      }
    }

    .button-operate {
      .el-button {
        width: 100px;
        height: 40px;
        font-size: 16px;
        margin-right: 10px;
        margin-left: 10px
      }
    }
  }
}

::v-deep{
  .preview-dialog{
    height: 90%;
    margin-top: 35px !important;

    .el-dialog__body{
      height: 93%;
    }
  }
}
</style>
