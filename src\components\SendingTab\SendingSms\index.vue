<template>
  <!-- 发送短信 -->
  <div class="sending-sms">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-position="top"
      label-width="100px"
    >
      <el-form-item
        label="收件人"
        prop="detailList"
        @click="openPersonnelGrouping"
      >
        <InputTag
          placeholder="请选择收件人"
          v-model="form.detailList"
          :disabled="false"
          @click.native.stop="openPersonnelGrouping"
        ></InputTag>
      </el-form-item>

      <el-form-item label="短信标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入短信标题"
          size="small"
          clearable
        />
      </el-form-item>

      <el-form-item label="短信内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请输入短信内容"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>

    <!-- 选择分组弹框 -->
    <PersonnelGrouping
      ref="personnelGroupingRef"
      :visible.sync="personnelGroupingVisible"
      :modelValue="form.detailList"
      @update:modelValue="handlePersonnelGroupingUpdate"
    />
  </div>
</template>

<script>
import { systemManagementApi } from "@/api";
import assistantAI from "@/api/assistantAI";
import { textMessageSendApi } from "@/api";
import InputTag from "@/components/InputTag.vue";
import PersonnelGrouping from "@/views/TextMessage/Components/personnelGrouping.vue";

export default {
  name: "SendingSms",
  components: {
    InputTag,
    PersonnelGrouping,
  },
  props: {
    componentProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      options: [], //queryOrg
      nodeMap: new Map(),
      personnelGroupingVisible: false, // 选择分组弹框状态

      form: {
        //收件人
        detailList: [],
        //短信标题
        title: "",
        //短信内容
        content: "",
      },
      rules: {
        detailList: [
          { required: true, message: "请选择收件人", trigger: "blur" },
        ],
        title: [{ required: true, message: "请输入短信标题", trigger: "blur" }],
        content: [
          { required: true, message: "请输入短信内容", trigger: "blur" },
        ],
      },

      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        multiple: true,
        checkStrictly: true, // 允许选择任意级
        lazy: true, // 启用懒加载
        emitPath: false,
        lazyLoad: this.loadChildren,
      },
    };
  },
  watch: {
    componentProps: {
      handler(newVal, oldVal) {
        console.log(this.componentProps);
        this.getSendMessage();
        this.form.title = newVal.infoTitle;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    // 打开人员分组选择弹框
    openPersonnelGrouping() {
      console.log("openPersonnelGrouping 方法被调用");
      this.handleAction("", "personnelGrouping");
    },
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map((item) => ({
            ...item,
            disabled:
              item.type === 0 && item.children && item.children.length === 0, // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({
          orgId: data?.id || "0",
        });
        const nodes = response.data.map((item) => {
          this.nodeMap.set(item.id, item); // 存储映射关系
          // 用户节点
          if (item.type === 1) {
            return {
              ...item,
              orgName: item.userName || "未命名用户",
              isLeaf: true,
            };
          }
          // 部门节点
          return {
            ...item,
            //disabled: item.type === 0 && (!item.children || item.children.length === 0) // 空部门禁用
          };
        });
        resolve(nodes);
      } catch (error) {
        console.error("加载子节点失败:", error);
        resolve([]);
      }
    },

    //获取发送内容
    async getSendMessage() {
      const { data } = await assistantAI.querySendMessage({
        id: this.componentProps.id,
      });
      this.form.content = data;
    },

    // 处理选择分组等操作
    handleAction(row, type) {
      switch (type) {
        case "personnelGrouping":
          this.personnelGroupingVisible = true;
          this.$nextTick(() => {
            this.$refs.personnelGroupingRef.incoming(this.form.detailList);
          });
          break;
        default:
          break;
      }
    },

    // 处理分组选择结果
    handlePersonnelGroupingUpdate(selectedPersons) {
      // 将选中的人员按分组聚合，生成特定格式的显示数据
      const groupMap = new Map();

      selectedPersons.forEach((person) => {
        const groupId = person.groupId;
        const groupName = person.groupName;

        if (!groupMap.has(groupId)) {
          groupMap.set(groupId, {
            groupId,
            groupName,
            selectedCount: 0,
            totalCount: 0,
            persons: [],
          });
        }

        const group = groupMap.get(groupId);
        group.selectedCount++;
        group.persons.push(person);
      });

      // 获取每个分组的总人数（需要调用API）
      this.updateGroupTotalCounts(groupMap).then(() => {
        // 生成显示格式：分组名称(已选人数/总人数)
        const displayList = Array.from(groupMap.values()).map((group) => ({
          name: `${group.groupName}(${group.selectedCount}/${group.totalCount})`,
          groupId: group.groupId,
          groupName: group.groupName,
          selectedCount: group.selectedCount,
          totalCount: group.totalCount,
          persons: group.persons,
        }));

        this.form.detailList = displayList;
      });
    },

    // 获取分组总人数
    async updateGroupTotalCounts(groupMap) {
      const promises = Array.from(groupMap.keys()).map(async (groupId) => {
        try {
          const response = await textMessageSendApi.queryUserInfo({
            id: groupId,
          });
          const totalCount = response.data ? response.data.length : 0;
          groupMap.get(groupId).totalCount = totalCount;
        } catch (error) {
          console.error(`获取分组${groupId}人员总数失败:`, error);
          groupMap.get(groupId).totalCount = 0;
        }
      });

      await Promise.all(promises);
    },

    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$message({
            message: "短信发送成功",
            type: "success",
          });
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      detailList: [],
      title: "",
      content: "",
    };
  },
};
</script>

<style scoped lang="scss">
.matter-cascader {
  width: 100%;
  height: 100px;
  ::v-deep {
    .el-input {
      height: 100%;
      input {
        height: 100% !important;
      }
    }

    .el-cascader__tags {
      top: 5px;
      transform: none;
    }
  }
}

.add-form {
  padding: 20px;
}

.el-form-item {
  flex: 1;
}
</style>
