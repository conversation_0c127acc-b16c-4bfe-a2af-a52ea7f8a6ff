<template>
  <div class="search-form">
    <!-- 搜索表单组件 - Component -->
    <div class="search-header" v-if="showTips">
      <div class="search-tips">
        <span v-for="tip in tips" :key="tip" class="search-tip">
          <i class="el-icon-info"></i>
          {{ tip }}
        </span>
      </div>
    </div>
    <div class="search-content">
      <div class="search-row" v-for="(row, index) in formRows" :key="index">
        <div class="search-item" v-for="item in row" :key="item.prop">
          <label>{{ item.label }}：</label>
          <el-input
            v-if="item.type === 'input'"
            v-model="formData[item.prop]"
            :placeholder="item.placeholder"
            size="small"
            clearable
          ></el-input>
          <el-select
            v-else-if="item.type === 'select'"
            v-model="formData[item.prop]"
            :placeholder="item.placeholder"
            size="small"
            clearable
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'date'"
            v-model="formData[item.prop]"
            type="date"
            :placeholder="item.placeholder"
            size="small"
            :value-format="item.format || 'yyyy-MM-dd'"
            clearable
          ></el-date-picker>

          <el-date-picker
            v-else-if="item.type === 'daterange'"
            v-model="formData[item.prop]"
            type="daterange"
            size="small"
            range-separator="-"
            start-placeholder="起始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
          <el-date-picker
            v-else-if="item.type === 'dateYear'"
            v-model="formData[item.prop]"
            type="year"
            size="small"
            format="yyyy"
            value-format="yyyy"
            @change="item.change"
          />
          <el-date-picker
            v-else-if="item.type === 'dateMonth'"
            v-model="formData[item.prop]"
            type="month"
            size="small"
            format="yyyy-MM"
            value-format="yyyy-MM"
            @change="item.change"
          />
          <el-cascader
            v-else-if="item.type === 'cascader'"
            :style="{ width: item.width }"
            :options="item.options"
            :props="item.props"
            clearable
          >
          </el-cascader>
        </div>
      </div>
      <!-- 按钮单独成行 -->
      <div class="search-buttons-row">
        <div class="search-buttons">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-search"
            @click="handleSearch"
            >查询</el-button
          >
          <el-button size="small" icon="el-icon-refresh" @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SearchForm",
  props: {
    formConfig: {
      type: Array,
      default: () => [],
    },
    tips: {
      type: Array,
      default: () => [],
    },
    showTips: {
      type: Boolean,
      default: false,
    },
    itemsPerRow: {
      type: Number,
      default: 4,
    },
    value: {},
  },
  data() {
    return {
      formData: {},
    };
  },
  computed: {
    formRows() {
      const rows = [];
      for (let i = 0; i < this.formConfig.length; i += this.itemsPerRow) {
        rows.push(this.formConfig.slice(i, i + this.itemsPerRow));
      }
      return rows;
    },
  },
  watch: {
    formConfig: {
      handler() {
        this.initFormData();
      },
      immediate: true,
    },
    value: {
      handler() {
        this.formData = this.value ? this.value : {};
      },
      deep: true,
    },
  },
  methods: {
    initFormData() {
      const data = {};
      this.formConfig.forEach((item) => {
        data[item.prop] = item.defaultValue || "";
      });
      this.formData = data;
    },
    handleSearch() {
      this.$emit("search", { ...this.formData });
    },
    handleReset() {
      this.initFormData();
      this.$emit("reset");
    },
    getFormData() {
      return { ...this.formData };
    },
    setFormData(data) {
      this.formData = { ...this.formData, ...data };
    },
  },
};
</script>

<style scoped>
.search-form {
  background: transparent;
  padding: 8px 0;
  border: none;
  box-shadow: none;
  margin-bottom: 0;
}

.search-header {
  margin-bottom: 8px;
  padding-bottom: 0;
  border-bottom: none;
}

.search-tips {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.search-tip {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 5px;
  line-height: 1.4;
}

.search-tip i {
  color: var(--icon-primary);
}

.search-content {
  padding: 0;
}

.search-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 16px 24px;
}

.search-row:last-child {
  margin-bottom: 0;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 240px;
}

.search-item label {
  font-size: 14px;
  color: #666666;
  white-space: nowrap;
  width: 80px;
  min-width: 80px;
  text-align: left;
  flex-shrink: 0;
}

.search-item .el-input,
.search-item .el-select,
.search-item .el-date-picker,
.el-date-editor,
.el-cascader {
  flex: 1;
  min-width: 120px;
}

.search-buttons-row {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.search-buttons {
  display: flex;
  gap: 12px;
}

@media (max-width: 1200px) {
  .search-form {
    padding: 8px 0;
  }

  .search-row {
    gap: 12px 20px;
  }

  .search-item label {
    min-width: 50px;
  }
}

@media (max-width: 768px) {
  .search-buttons-row {
    justify-content: center;
  }

  .search-buttons {
    width: 100%;
    justify-content: center;
  }
}
</style>
