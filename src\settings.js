module.exports = {
  title: '应急指挥一张图管理系统',

  /**
   * @type {boolean} true | false
   * @description 是否在边栏中显示徽标
   */
  sidebarLogo: true,

  /**
   * @type {number} 0 | 1
   * @description 菜单模式 0：路由菜单 1：弹框路由混合菜单
   */
  menuMode: 0,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description 需要显示错误日志组件。
   * 默认仅在生产环境中使用
   * 如果您还想在开发人员中使用它，则可以通过['production'，'development']
   */
  errorLog: 'production',

  /**
   * @type {string} '
   * file-dev | xxx-prod'
   * @description 阿里云上传字段配置
   */
  fileNameDev: 'achat-prod', // 开发环境
  fileNameProd: 'achat-prod', // 生成环境

  /**
   * @type {string} 'https://xxx.xxx.xxxx'
   * @description 文件地址前缀
   */

  fileUrlDev: 'http://**************:9000/achat-prod/',
  fileUrlProd: 'http://**************:9000/achat-prod/'
}
