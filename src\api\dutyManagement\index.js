// 值班管理相关api
import request from "@/utils/request";
export default class dutyManagement {

  /**
   * --------------------------值班安排-------------------------------
   */

  // 值班岗位管理---新增值班岗位管理模板
  static createPositionTemplate(data) {
    return request({
        url: "/ds/duty/positionTemplate/createPositionTemplate",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---删除值班岗位管理模板
  static deletePositionTemplate(data) {
    return request({
        url: "/ds/duty/positionTemplate/deletePositionTemplate",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---修改值班岗位管理模板
  static updatePositionTemplate(data) {
    return request({
        url: "/ds/duty/positionTemplate/updatePositionTemplate",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---查询某个值班岗位管理模板
  static queryPositionTemplateById(data) {
    return request({
        url: "/ds/duty/positionTemplate/queryPositionTemplateById",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---查询值班岗位管理模板列表
  static queryPositionTemplateList(data) {
    return request({
        url: "/ds/duty/positionTemplate/queryPositionTemplateList",
        method: "post",
        data ,
    });
  }

  // 值班岗位管理---新增值班岗位管理信息
  static createPosition(data) {
    return request({
        url: "/ds/duty/position/createPosition",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---删除值班岗位管理信息
  static deletePosition(data) {
    return request({
        url: "/ds/duty/position/deletePosition",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---修改值班岗位管理信息
  static updatePosition(data) {
    return request({
        url: "/ds/duty/position/updatePosition",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---查询某个值班岗位管理信息
  static queryPositionById(data) {
    return request({
        url: "/ds/duty/position/queryPositionById",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---查询值班岗位管理信息列表
  static queryPositionList(data) {
    return request({
        url: "/ds/duty/position/queryPositionList",
        method: "post",
        data ,
    });
  }
  // 值班岗位管理---通过级别查询值班岗位
  static queryPositionByLevel(data) {
    return request({
        url: "/ds/duty/position/queryPositionByLevel",
        method: "post",
        data ,
    });
  }


  // 排班管理---查询排班表表头
  static queryPositionByMonth(data) {
    return request({
        url: "/ds/duty/arrangement/queryPosition",
        method: "post",
        data ,
    });
  }

  // 排班管理---查询排班表表格数据
  static queryArrangementByMonth(data) {
    return request({
        url: "/ds/duty/arrangement/queryArrangement",
        method: "post",
        data ,
    });
  }

  // 排班管理---查询其他处室排班表表格数据
  static queryDeptArrangement(data) {
    return request({
        url: "/ds/duty/deptArrangement/queryDeptArrangement",
        method: "post",
        data ,
    });
  }

  
  // 排班管理---查询值班安排统计不带职务排班表表格数据
  static queryDeptArrangementNoPosition(data) {
    return request({
        url: "/ds/duty/deptArrangement/queryDeptArrangementNoPosition",
        method: "post",
        data ,
    });
  }

  // 排班管理---单元格添加值班人员
  static createArrangement(data) {
    return request({
        url: "/ds/duty/arrangement/createArrangement",
        method: "post",
        data ,
    });
  }
  
  // 排班管理---单元格操作替换班
  static createSubstitute(data) {
    return request({
        url: "/ds/duty/arrangement/createSubstitute",
        method: "post",
        data ,
    });
  }

  // 排班管理---下载排班表模板
  static downloadTemplate = (year, month) => {
    return request({
      url: "/ds/duty/deptArrangement/downloadTemplate?year=" + year + "&month=" + month,
      method: "get",
      responseType: 'blob',
    });
  };

  // 排班管理---导入值班信息表格
  static importDutyInfo(data) {
    return request({
        url: "/ds/duty/deptArrangement/uploadDutyInfo",
        method: "post",
        data ,
    });
  }

  // 排班管理---导出不带职务的值班信息表格
  static exportDutyInfo(data) {
    return request({
        url: "/ds/duty/deptArrangement/exportDutyInfo",
        method: "post",
        data ,
        responseType: 'blob',
    });
  }



  // 换班替班---获取替班换班列表
  static querySubstitutePage(data) {
    return request({
        url: "/ds/duty/substitute/querySubstitutePage",
        method: "post",
        data ,
    });
  }
  
  // 换班替班---审批替班换班
  static duTyReplaceApproval(data) {
    return request({
        url: "/ds/duty/substitute/approval",
        method: "post",
        data ,
    });
  }



  /**
   * --------------------------值班检查-------------------------------
   */
  // 值班检查管理---新增值班检查
  static createInspection(data) {
    return request({
        url: "/ds/duty/inspect/createInspection",
        method: "post",
        data ,
    });
  }

  // 值班检查管理---删除值班检查
  static deleteInspection(data) {
    return request({
        url: "/ds/duty/inspect/deleteInspection",
        method: "post",
        data ,
    });
  }

  // 值班检查管理---修改值班检查
  static updateInspection(data) {
    return request({
        url: "/ds/duty/inspect/updateInspection",
        method: "post",
        data ,
    });
  }

  // 值班检查管理---查询值班检查列表
  static queryInspectionList(data) {
    return request({
        url: "/ds/duty/inspect/queryInspectionList",
        method: "post",
        data ,
    });
  }

  // 值班检查管理---查看值班检查详情
  static queryInspectionInfo(data) {
    return request({
        url: "/ds/duty/inspect/queryInspectionInfo",
        method: "post",
        data ,
    });
  }


  // 值班检查统计---查看值班检查统计列表
  static queryInspectionStatisticsList(data) {
    return request({
        url: "/ds/duty/inspect/queryInspectionStatisticsList",
        method: "post",
        data ,
    });
  }
  

   /**
   * --------------------------值班事项记录单-------------------------------
   */
  // 接报信息记录单---查询接报信息记录列表
  static queryReportInfoList(data) {
    return request({
        url: "/ds/infoAggregation/queryReportInfoList",
        method: "post",
        data ,
    });
  }

  // 接报信息记录单---查询接报信息记录详情
  static queryReportInfo(data) {
    return request({
        url: "/ds/infoAggregation/queryReportInfo",
        method: "post",
        data ,
    });
  }

  // 接报信息记录单---修改接报信息记录单
  static updateReportInfo(data) {
    return request({
        url: "/ds/infoAggregation/updateReportInfo",
        method: "post",
        data ,
    });
  }


  // 预警信息记录单---查询预警信息记录列表
  static queryWarningInfoList(data) {
    return request({
        url: "/ds/earlyWarning/access/queryWarningInfoList",
        method: "post",
        data ,
    });
  }

  // 预警信息记录单---查询预警信息记录详情
  static queryWarningInfoById(data) {
    return request({
        url: "/ds/earlyWarning/access/queryWarningInfoById",
        method: "post",
        data ,
    });
  }

  // 预警信息记录单---修改预警信息记录单
  static updateWarningInfo(data) {
    return request({
        url: "/ds/earlyWarning/access/updateWarningInfo",
        method: "post",
        data ,
    });
  }


  // 录入信息记录单---新增录入信息记录单
  static createInputInfo(data) {
    return request({
        url: "/ds/duty/positionTemplate/createInputInfo",
        method: "post",
        data ,
    });
  }

  // 录入信息记录单---删除录入信息记录单
  static deleteInputInfo(data) {
    return request({
        url: "/ds/duty/positionTemplate/deleteInputInfo",
        method: "post",
        data ,
    });
  }

  // 录入信息记录单---查询录入信息记录单列表
  static queryInputInfoPage(data) {
    return request({
        url: "/ds/duty/positionTemplate/queryInputInfoPage",
        method: "post",
        data ,
    });
  }

  // 录入信息记录单---查询录入信息记录单详情
  static queryInputInfoById(data) {
    return request({
        url: "/ds/duty/positionTemplate/queryInputInfoById",
        method: "post",
        data ,
    });
  }

  // 录入信息记录单---修改录入信息记录单
  static updateInputInfo(data) {
    return request({
        url: "/ds/duty/positionTemplate/updateInputInfo",
        method: "post",
        data ,
    });
  }


  /**
   * --------------------------值班详情统计-------------------------------
   */
  // 值班详情统计---查询值班详情统计列表
  static queryDutyDetailsStatistics(data) {
    return request({
        url: "/ds/duty/dutyDetailsStatistics/queryDutyDetailsStatistics",
        method: "post",
        data ,
    });
  }


  /**
   * --------------------------交接班管理-------------------------------
   */
  // 交接班管理---查询交接班值班人员信息
  static queryShiftingDuty(data) {
    return request({
        url: "/ds/duty/shiftingDuty/queryShiftingDuty",
        method: "get",
        data ,
    });
  }

  // 交接班管理---查询交接班数据
  static queryShiftData(shiftType) {
    return request({
        url: "/ds/duty/shiftingDuty/queryShiftData?shiftType=" + shiftType,
        method: "get",
    });
  }

  // 交接班管理---保存交接班相关数据
  static updateShiftData(data) {
    return request({
        url: "/ds/duty/shiftingDuty/updateShiftData",
        method: "post",
        data ,
    });
  }

  // 交接班管理---生成联合值守工作信息
  static shiftingDutyUrl(data) {
    return request({
        url: "/ds/duty/shiftingDuty/shiftingDutyUrl",
        method: "post",
        data ,
    });
  }

  // 刊物信息---获取普刊信息文件地址
  static paginatedUrl(data) {
    return request({
        url: "/ds/publication/paginatedUrl",
        method: "post",
        data ,
    });
  }



  /**
   * --------------------------工作通知-------------------------------
   */
  // 内容管理---添加工作通知内容
  static createContentManage(data) {
    return request({
        url: "/ds/contentManage/createContentManage",
        method: "post",
        data ,
    });
  }

  // 内容管理---删除工作通知内容
  static deleteContentManage(data) {
    return request({
        url: "/ds/contentManage/deleteContentManage",
        method: "post",
        data ,
    });
  }

  // 内容管理---修改工作通知内容
  static updateContentManage(data) {
    return request({
        url: "/ds/contentManage/updateContentManage",
        method: "post",
        data ,
    });
  }

  // 内容管理---查询工作通知内容详情
  static queryContentManageById(data) {
    return request({
        url: "/ds/contentManage/queryContentManageById",
        method: "post",
        data ,
    });
  }

  // 内容管理---查询工作通知内容列表
  static queryContentManagePage(data) {
    return request({
        url: "/ds/contentManage/queryContentManagePage",
        method: "post",
        data ,
    });
  }

  // 内容管理---发布工作通知内容
  static publishWorkNotice(data) {
    return request({
        url: "/ds/contentManage/publish",
        method: "post",
        data ,
    });
  }


  // 通知跟踪---查询通知跟踪详情
  static queryNotifyTrackInfo(data) {
    return request({
        url: "/ds/notifyTrack/queryNotifyTrackInfo",
        method: "post",
        data ,
    });
  }

  // 通知跟踪---查询通知跟踪列表
  static queryNotifyTrackPage(data) {
    return request({
        url: "/ds/notifyTrack/queryNotifyTrackPage",
        method: "post",
        data ,
    });
  }

  // 通知跟踪---通知跟踪重发
  static workNoticeSendAgain(data) {
    return request({
        url: "/ds/notifyTrack/sendAgain",
        method: "post",
        data ,
    });
  }


  
  

}