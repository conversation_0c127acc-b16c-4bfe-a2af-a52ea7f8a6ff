<!-- 会议通知---MeetingNotice -->
<template>
  <div class="user-index-container">
    <portal-table
      v-if="showMeetingType === 1"
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="joinForm"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="会议名称" prop="meetingName">
          <el-input :disabled="true" style="width: 281px;" v-model="form.meetingName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="会议日期" prop="meetingDate">
          <el-date-picker
            :disabled="true"
            v-model="form.meetingDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeMeetingDate">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="调试时间" prop="deTime">
          <el-time-picker
            :disabled="true"
            style="width: 281px;"
            v-model="form.deTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择调试时间">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="召开时间" prop="holdTime">
          <el-time-picker
            :disabled="true"
            style="width: 281px;"
            v-model="form.holdTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择召开时间">
          </el-time-picker>
        </el-form-item>

        <el-form-item label="报备截止时间" prop="deadlineDate">
          <el-date-picker
            :disabled="true"
            v-model="form.deadlineDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeDeadline">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否使用会控" prop="meetingControls">
          <el-select :disabled="true" v-model="form.meetingControls" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议周期" prop="meetingCycle">
          <el-select :disabled="true" v-model="form.meetingCycle" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingCycleList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="会议终端类型" prop="meetingTerminalType">
          <el-select :disabled="true" v-model="form.meetingTerminalType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTerminalTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议类别" prop="meetingType">
          <el-select :disabled="true" v-model="form.meetingType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="主会场信息" prop="venueInfo">
          <el-select :disabled="true" v-model="form.venueInfo" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueInfoList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主会场需求" prop="venueRequirement">
          <el-select :disabled="true" v-model="form.venueRequirement" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueRequirementList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="本地使用终端" prop="locallyTerminal">
          <el-select :disabled="true" v-model="form.locallyTerminal" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in locallyTerminalList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议级别" prop="meetingLevel">
          <el-select :disabled="true" v-model="form.meetingLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="分会场发言" prop="subVenueSpeeches">
          <el-select :disabled="true" v-model="form.subVenueSpeeches" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in subVenueSpeechesList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会单位" prop="attendUnitList">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 600px;"
            v-model="form.attendUnitList"
            :options="orgTree"
            :props="props"
            :disabled="true"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleAttendUnitChange">
          </el-cascader>
        </el-form-item>


        <el-form-item label="领导级别" prop="leadershipLevel">
          <el-select :disabled="true" v-model="form.leadershipLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in leadershipLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会领导" prop="attendLeader">
          <el-input :disabled="true" style="width: 281px;" v-model="form.attendLeader" placeholder="请输入" />
        </el-form-item>


        <el-form-item label="承办单位" prop="organUnit">
          <el-cascader
            style="width: 281px;"
            v-model="form.organUnit"
            :options="orgTree"
            :props="organUnitProps"
            :disabled="true"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleArganUnitChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input :disabled="true" style="width: 281px;" v-model="form.phone" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="保障人员" prop="supportPersonnel">
          <el-select :disabled="true" v-model="form.supportPersonnel" placeholder="请选择" style="width: 600px;">
            <el-option
              v-for="item in supportPersonnelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通知附件" prop="fileList">
          <el-upload
            style="width: 600px;"
            :disabled="true"
            ref="uploadRef"
            class="upload-demo"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :http-request="uploadFile"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
          </el-upload>
        </el-form-item>

        <el-form-item label="会议说明" prop="meetingNotes">
          <el-input 
            :disabled="true" 
            style="width: 600px;" 
            v-model="form.meetingNotes" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>

        <el-divider v-if="form.meetingRegister === 1" content-position="left">
          <h3>参会信息报送</h3>
        </el-divider>

        <!-- 参会终端部分 -->
        <div v-if="form.meetingRegister === 1" v-for="(terminal, index) in joinForm.terminalList" :key="index">
          <el-form-item 
            label="参会终端" 
            :prop="`terminalList[${index}].meetingTerminal`"
            :rules="{required: true, message: '请输入参会终端', trigger: 'blur'}">
            <el-input 
              :disabled="styleType === 3" 
              style="width: 241px;" 
              v-model="terminal.meetingTerminal" 
              placeholder="请输入参会终端">
            </el-input>
          </el-form-item>
          
          <el-form-item 
            label="参会终端用途" 
            :prop="`terminalList[${index}].meetingTerminalPurpose`"
            :rules="{required: true, message: '请输入参会终端用途', trigger: 'blur'}">
            <el-input 
              :disabled="styleType === 3" 
              style="width: 241px;" 
              v-model="terminal.meetingTerminalPurpose" 
              placeholder="请输入参会终端用途">
            </el-input>
          </el-form-item>

          <el-button v-if="index > 0" @click="removeTerminal(index)" type="danger" size="small" style="top: 55px;">
              删除
          </el-button>
        </div>
        
        <el-form-item v-if="form.meetingRegister === 1">
          <el-button :disabled="styleType === 3" @click="addTerminal" type="primary" size="small">新增参会终端</el-button>
        </el-form-item>
        
        <!-- 参会领导部分 -->
        <div v-if="form.meetingRegister === 1" v-for="(leader, index) in joinForm.leaderList" :key="'leader'+index">
          <el-form-item 
            label="参会领导"  
            :prop="`leaderList[${index}].signAttendLeader`"
            :rules="{required: true, message: '请输入参会领导姓名', trigger: 'blur'}">
            <el-input 
              :disabled="styleType === 3" 
              style="width: 241px;" 
              v-model="leader.signAttendLeader" 
              placeholder="请输入参会领导姓名">
            </el-input>
          </el-form-item>
          
          <el-form-item 
            label="联系电话" 
            :prop="`leaderList[${index}].attendLeaderPhone`"
            :rules="[
              {required: true, message: '请输入参会领导联系电话', trigger: 'blur'},
              {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
            ]">
            <el-input 
              :disabled="styleType === 3" 
              style="width: 241px;" 
              v-model="leader.attendLeaderPhone" 
              placeholder="请输入参会领导联系电话">
            </el-input>
          </el-form-item>

          <el-button 
            v-if="index > 0" 
            @click="removeLeader(index)" 
            type="danger" 
            size="small" 
            style="top: 50px;">
              删除
          </el-button>
        </div>
        
        <el-form-item v-if="form.meetingRegister === 1">
          <el-button 
            :disabled="styleType === 3" 
            @click="addLeader" 
            type="primary" 
            size="small">
            新增参会领导
          </el-button>
        </el-form-item>

        <!-- 保障人员 -->
        <br v-if="form.meetingRegister === 1">
        <el-form-item
          v-if="form.meetingRegister === 1" 
          label="保障人员" prop="signSupportPersonnel" 
          :rules="{required: true, message: '请输入保障人员姓名', trigger: 'blur'}">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 241px;" 
            v-model="joinForm.signSupportPersonnel" 
            placeholder="请输入保障人员姓名">
          </el-input>
        </el-form-item>
        <!-- 保障人员联系电话 -->
        <el-form-item 
          v-if="form.meetingRegister === 1"
          label="联系电话" prop="signPhone"
          :rules="[
            {required: true, message: '请输入保障人员联系电话', trigger: 'blur'},
            {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
           ]">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 241px;" 
            v-model="joinForm.signPhone" 
            placeholder="请输入保障人员联系电话">
          </el-input>
          <!-- <span style="color: #999; margin-left: 10px;">注：须与承办手机一致</span> -->
        </el-form-item>
        
        <!-- 参会说明 -->
        <el-form-item v-if="form.meetingRegister === 1" label="参会说明" prop="signMeetingNotes">
          <el-input
            :disabled="styleType === 3" 
            style="width: 600px;" 
            v-model="joinForm.signMeetingNotes" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>
        <!-- <el-form-item label="参会说明" prop="description">
          <el-input style="width: 600px;"  type="textarea" v-model="form.description" placeholder="请输入参会说明"></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="参会终端" prop="remark">
          <el-input :disabled="styleType === 3" style="width: 281px;" v-model="form.phone" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="参会终端" prop="remark">
          <el-input :disabled="true" style="width: 281px;" v-model="form.phone" placeholder="请输入" />
        </el-form-item> -->
        
      </el-form>
    </general-dialog>

    <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog>

    <reportMeeting 
      ref="reportMeetingRef"
      @buttonBackClick="handleReportBack()"> 
    </reportMeeting>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, orgApi, meetingManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond, getCurrentDate, getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils";
import reportMeeting from "../MeetingNotice/components/reportMeeting.vue";

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
    reportMeeting,
  },
  data() {
    return {
      showMeetingType: 1,
      searchItems: [
        {
          prop: "attendanceType",
          label: "参与类型",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [
            { label: "我组织的", value: 0 },
            { label: "我参加的", value: 1 }
          ],
        },
        {
          prop: "meetingType",
          label: "会议类型",
          type: "select",
          placeholder: "请选择",
          width: "180",
          options: [],
        },
        {
          prop: "meetingDate",
          label: "会议日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "holdTime",
          label: "召开时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
      ],
      columns: [
        { prop: "meetingName", label: "会议名称", text: true },
        { prop: "meetingDate", label: "会议日期", text: true },
        { prop: "holdTime", label: "召开时间", text: true },
        { prop: "deadlineDate", label: "截止时间", text: true },
        { prop: "meetingNotes", label: "会议说明", text: true },
        { prop: "venueInfo", label: "主会场信息", text: true },
        { prop: "meetingTerminalType", label: "会议终端类型", text: true },
        { prop: "supportPersonnel", label: "保障人员", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '260px',
          operationList: [
            {
              label: '报名',
              permission: 'recentMeeting:sign',
              buttonClick: this.handleSign,
              isShow: (row, $index) => {
                if(this.tableData[$index].signFlag === 1 || this.tableData[$index].meetingRegister === 0){
                  return false
                }else{
                  return true
                }
              }
            },
            {
              label: '详情',
              permission: 'recentMeeting:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '报送信息',
              permission: 'recentMeeting:report',
              buttonClick: this.handleApproval,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      tableData: [],
      
      styleType: 1, //1：报名，2：编辑，3：查看
      trueOrFalseTypeList:[
        {itemName: "是", id: 1 },
        {itemName: "否", id: 0 }
      ],
      meetingTerminalTypeList: [
        {itemName: "测试应急终端", id: "测试应急终端"},
        {itemName: "测试指挥终端", id: "测试指挥终端"},
      ],
      venueInfoList: [
        {itemName: "测试主会场1", id: "测试主会场1"},
        {itemName: "测试主会场2", id: "测试主会场2"},
      ],
      venueRequirementList: [
        {itemName: "测试主会场需求1", id: "测试主会场需求1"},
        {itemName: "测试主会场需求2", id: "测试主会场需求2"},
      ],
      locallyTerminalList: [
        {itemName: "172.2.1.10", id: "172.2.1.10"},
        {itemName: "172.2.1.11", id: "172.2.1.11"},
      ],
      meetingLevelList: [
        {itemName: "一类会议", id: "1"},
        {itemName: "二类会议", id: "2"},
        {itemName: "三类会议", id: "3"},
        {itemName: "四类会议", id: "4"},
      ],
      subVenueSpeechesList: [
        {itemName: "市应急指挥中心", id: "市应急指挥中心"},
        {itemName: "丰台区应急指挥中心", id: "丰台区应急指挥中心"},
        {itemName: "西城区应急管理局", id: "西城区应急管理局"},
        {itemName: "东城区应急管理局", id: "东城区应急管理局"},
      ],
      leadershipLevelList: [
        {itemName: "局级", id: "局级"},
        {itemName: "副局级", id: "副局级"},
        {itemName: "处级", id: "处级"},
        {itemName: "副处级", id: "副处级"},
        {itemName: "正科级", id: "正科级"},
        {itemName: "副科级", id: "副科级"},
      ],
      supportPersonnelList: [
        {itemName: "张三（18090900909）", id: "张三（18090900909）"},
        {itemName: "李四（18090900909）", id: "李四（18090900909）"},
        {itemName: "王五（18090900909）", id: "王五（18090900909）"},
        {itemName: "赵六（18090900909）", id: "赵六（18090900909）"},
      ],
      setSendingTimeList: [
        {itemName: "审批后立即发送", id: "0"},
        {itemName: "审批后选择时间发送", id: "1"},
      ],

      meetingCycleList:[
        {itemName: "1天", id: "1"},
        {itemName: "7天", id: "7"},
        {itemName: "15天", id: "15"},
        {itemName: "一个月", id: "30"},
      ],
      noticeMethodTypeList: [
        {itemName: "系统消息", id: "1"},
        {itemName: "短信", id: "2"},
        {itemName: "语音广播", id: "3"},
        {itemName: "京办", id: "4"},
      ],
      meetingReminderList: [
        {itemName: "提前5分钟", id: "5"},
        {itemName: "提前10分钟", id: "10"},
        {itemName: "提前15分钟", id: "15"},
        {itemName: "提前30分钟", id: "30"},
        {itemName: "自定义时间", id: "0"},
      ],
      orgTree: [],
      organUnitProps: {
        multiple: false,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      meetingTypeList: [],


      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "760px",
      generalDialogTitle: "报名会议",
      // buttonList: [
      //   { id: 1, text: "不使用会议流程", type: "primary", width: "150px" },
      // ],

      form: {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        sendingTime: "",
        setSendingTime: "",
        fileList:[],
      },
      joinForm: {
        terminalList: [
          { meetingTerminal: '', meetingTerminalPurpose: '' } // 默认一条参会终端
        ],
        signSupportPersonnel: '',
        signPhone: '',
        signMeetingNotes: '',
        leaderList: [
          { signAttendLeader: '', attendLeaderPhone: '' } // 默认一条参会领导
        ],
      },
      rules: {
        // meetingName: [
        //   {required: true, message: '会议名称不能为空', trigger: 'blur'}
        // ],
        // meetingType: [
        //   {required: true, message: '会议类别不能为空', trigger: 'blur'}
        // ],
        // meetingControls: [
        //   {required: true, message: '是否使用会控不能为空', trigger: 'blur'}
        // ],
        // meetingCycle: [
        //   {required: true, message: '会议周期不能为空', trigger: 'blur'}
        // ],
        // organUnit: [
        //   {required: true, message: '承办单位不能为空', trigger: 'blur'}
        // ],
        // attendUnitList: [
        //   {required: true, message: '参会单位不能为空', trigger: 'blur'}
        // ],
        // noticeMethodList: [
        //   {required: true, message: '通知方式不能为空', trigger: 'blur'}
        // ],
        // meetingRecorded: [
        //   {required: true, message: '是否会议录播不能为空', trigger: 'blur'}
        // ],
        // meetingRegister: [
        //   {required: true, message: '是否会议报名不能为空', trigger: 'blur'}
        // ],
        // meetingAlert: [
        //   {required: true, message: '是否会议提醒不能为空', trigger: 'blur'}
        // ],
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    // this.registerHandlers();
  },
  methods: {
    // registerHandlers() {
    //   this.$store.commit("generalEvent/registerEventHandler", {
    //     type: "add_top",
    //     handler: this.handleAdd,
    //   });
    // },

    // 新增参会终端
    addTerminal() {
      this.joinForm.terminalList.push({ meetingTerminal: '', meetingTerminalPurpose: '' });
    },
    // 删除参会终端
    removeTerminal(index) {
      this.joinForm.terminalList.splice(index, 1);
    },
    // 新增参会领导
    addLeader() {
      this.joinForm.leaderList.push({ signAttendLeader: '', attendLeaderPhone: '' });
    },
    // 删除参会领导
    removeLeader(index) {
      this.joinForm.leaderList.splice(index, 1);
    },

    handleChangeMeetingDate(value) {
      this.form.meetingDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleChangeDeadline(value) {
      this.form.deadlineDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleArganUnitChange(value) {
      // 选择的承办单位
    },
    handleAttendUnitChange(value) {
      // 选择的参会单位集合
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          this.searchItems[1].options = this.meetingTypeList.map((item) => ({
            label: item.itemName,
            value: item.id
          }))
        }
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionResultList = await getItemList(inspectionResultType);
      //   this.searchItems[2].options = this.inspectionResultList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      // 查询会议详情
      meetingManagementApi.queryMeetingNoticeDetail({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        this.form = {
          ...data,
        };
      });
      // 查询报名详情
      meetingManagementApi.queryMeetingSignDetail({ meetingId: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.joinForm = data;
        if (!this.joinForm.terminalList || this.joinForm.terminalList.length === 0) {
          this.joinForm.terminalList = [ { meetingTerminal: '', meetingTerminalPurpose: '' } ];
        }
        if (!this.joinForm.leaderList || this.joinForm.leaderList.length === 0) {
          this.joinForm.leaderList = [ { signAttendLeader: '', attendLeaderPhone: '' } ];
        }
      });
    },

    //新增
    // async handleAdd() {
    //   this.styleType = 0;
    //   this.dialogVisible = true;
    //   // this.form = {};
    //   // this.fileList = [];
    //   // this.form.fileList = [];
    //   this.generalDialogTitle = "选择会议流程";
    //   this.buttonList = [{ id: 1, text: "不使用会议流程", type: "primary", width: "150px" }];
    //   const res = await meetingManagementApi.queryMeetingFlowList();
    //   const { code, data, message, error } = res;
    //   if (code !== 0) return this.$message.error(message || error)
    //   this.flowTableData = data || [];
    // },

    //编辑
    handleEdit(row) {
      this.queryOrgTreeDataList();
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑会议通知";
    },

    //查看
    handleReview(row) {
      this.queryOrgTreeDataList();
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看会议通知";
    },

    //删除
    // handleDelete(row) {
    //   this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     meetingManagementApi.deleteMeetingNotice({ id: row.id }).then((res) => {
    //       const { code, message, error } = res;
    //       if (code !== 0) return this.$message.error(message || error);
    //       this.$message.success('删除成功');
    //       this.handSubmitSuccess();
    //     })
    //   })
    // },

    // 报名
    handleSign(row) {
      this.queryOrgTreeDataList();
      this.styleType = 1;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "报名会议";
      // this.$confirm('确定审核通过此会议通知, 是否继续?', '审核', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   meetingManagementApi.auditMeeting({ id: row.id, signFlag: 1 }).then((res) => {
      //     const { code, message, error } = res;
      //     if (code !== 0) return this.$message.error(message || error);
      //     this.$message.success('审核通过');
      //     this.handSubmitSuccess();
      //   })
      // })
    },

    // 报送
    handleApproval(row) {
      this.showMeetingType = 2;
      this.$refs.reportMeetingRef.showReportInfoList(row.id, row.meetingName);
    },

    handleReportBack() {
      this.showMeetingType = 1;
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await meetingManagementApi.queryMeetingSignPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.meetingDate && row.meetingDate.length > 0) {
        row.meetingStartDate = conversionDateNotSecond(row.meetingDate[0])
        row.meetingEndDate = conversionDateNotSecond(row.meetingDate[1])
        delete row.meetingDate
      }
      if (row.holdTime && row.holdTime.length > 0) {
        row.holdStartTime = conversionDateNotSecond(row.holdTime[0])
        row.holdEndTime = conversionDateNotSecond(row.holdTime[1])
        delete row.holdTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.resetFormData();
      // this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      if (this.styleType === 3) {
        this.handleCancel();
        return;
      }
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await meetingManagementApi.signMeeting(this.joinForm);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('报名成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } 
          // else {
          //   const res = await meetingManagementApi.updateMeetingNotice(this.form);
          //   const {code, error} = res;
          //   if (code === 0) {
          //     this.$message.success('修改成功')
          //     this.handSubmitSuccess();
          //   } else {
          //     this.$message.error(error)
          //   }
          // }
        } else {
          this.$alert('请填写所有必填字段！', '提示', {
            confirmButtonText: '确定',
          });
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.resetFormData();
      // this.$refs.addForm.resetFields();
    },

    resetFormData() {
      this.form = {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        setSendingTime: "",
        sendingTime: "",
        fileList:[],
      };
      this.joinForm = {
        terminalList: [
          { meetingTerminal: '', meetingTerminalPurpose: '' } // 默认一条参会终端
        ],
        signSupportPersonnel: '',
        signPhone: '',
        signMeetingNotes: '',
        leaderList: [
          { signAttendLeader: '', attendLeaderPhone: '' } // 默认一条参会领导
        ],
      }
    },

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(file.url);
      } 
      // window.open(fileUrl, '_blank');
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
  .el-upload__tip {
    color: red;
    line-height: normal !important;
  }
}
</style>
