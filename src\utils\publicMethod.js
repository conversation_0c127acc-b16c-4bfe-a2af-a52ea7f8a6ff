import moment from "moment";
import { auth } from "@/utils"; 

/**
 * 时间格式化
 * 带时间
 * @param {*} value
 * @returns
 */
export default function conversionDate(value) {
  return moment(value).format("YYYY-MM-DD HH:mm:ss");
}

/**
 * 时间格式化
 * 带时间不带秒
 * @param {*} value
 * @returns
 */
export function conversionDateNotSecond(value) {
  return moment(value).format("YYYY-MM-DD HH:mm");
}

/**
 * 时间格式化
 * 不带时间
 * @param {*} value
 * @returns
 */
export function conversionDateNotTime(value) {
  return moment(value).format("YYYY-MM-DD");
}

/**
 * 时间格式化
 * 不带时间且不带 -
 * @param {*} value
 * @returns
 */
export function ConvertDateToWithoutHorizontalBars(value) {
  return moment(value).format("YYYYMMDD");
}

/**
 * 获取指定格式的时间
 * targetDate如果不传则获取当前时间
 * @param {*} formatStr
 * @returns
 */
export function getCurrentDate(formatStr, targetDate) {
  const date = targetDate ? moment(targetDate) : moment();
  return date.format(formatStr);
}


/**
 * 获取当前环境的IP地址
 * @returns
 */
export function getIPAddress() {
  const url = new URL(process.env.VUE_APP_API_BASE_URL);
    let hostNameWithPort = url.hostname;
  if (hostNameWithPort.includes(":")) {
    hostNameWithPort = hostNameWithPort.split(":")[0];
  }
  return hostNameWithPort;
}


/**
 * 获取KKfile文件预览地址
 * @returns
 */
export function getKKFilePreviewUrl(fileUrl) {
  const kkfileUrl = fileUrl ='http://' + getIPAddress() + ':9806/onlinePreview?url=' + encodeURIComponent(btoa(fileUrl));
  return kkfileUrl;
}
