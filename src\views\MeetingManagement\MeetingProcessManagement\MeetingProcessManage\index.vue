<!-- 会议流程管理---MeetingNotice -->
<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="会议流程名称" prop="flowName">
          <el-input :disabled="styleType === 3" style="width: 231px;" v-model="form.flowName" placeholder="请输入" />
        </el-form-item>
        
        <el-form-item label="会议类别" prop="meetingType">
          <el-select :disabled="styleType === 3" v-model="form.meetingType" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in meetingTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否使用会控" prop="meetingControls">
          <el-select :disabled="styleType === 3" v-model="form.meetingControls" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议周期" prop="meetingCycle">
          <el-select :disabled="styleType === 3" v-model="form.meetingCycle" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in meetingCycleList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="承办单位" prop="organUnit">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 490px;"
            v-model="form.organUnit"
            :options="orgTree"
            :props="organUnitProps"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleArganUnitChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="参会单位" prop="attendUnitList">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 490px;"
            v-model="form.attendUnitList"
            :options="orgTree"
            :props="props"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleAttendUnitChange">
          </el-cascader>
        </el-form-item>

        <el-form-item label="通知方式" prop="noticeMethodList">
          <el-select :disabled="styleType === 3" v-model="form.noticeMethodList" multiple placeholder="请选择" style="width: 495px;">
            <el-option
              v-for="item in noticeMethodTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否会议录播" prop="meetingRecorded">
          <el-select :disabled="styleType === 3" v-model="form.meetingRecorded" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否会议报名" prop="meetingRegister">
          <el-select :disabled="styleType === 3" v-model="form.meetingRegister" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否会议提醒" prop="meetingReminder">
          <el-select :disabled="styleType === 3" v-model="form.meetingReminder" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.meetingReminder === 1" label="会议提醒" prop="meetingReminderTime">
          <el-input :disabled="styleType === 3" style="width: 231px;" v-model="form.meetingReminderTime" placeholder="请输入提前多少分钟提醒" /> 分钟
          <!-- <el-select :disabled="this.styleType === 3" v-model="form.meetingReminderTime" placeholder="请选择" style="width: 231px;">
            <el-option
              v-for="item in meetingReminderList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input 
            v-if="form.meetingReminder != '5' && form.meetingReminder != '10' && form.meetingReminder != '15' && form.meetingReminder != '30'" 
            :disabled="this.styleType === 3" 
            style="width: 231px;" 
            v-model="form.meetingReminderTime" 
            placeholder="请输入" />分钟 -->
        </el-form-item>

      </el-form>
    </general-dialog>

    <!-- <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { orgApi , meetingManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond, getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "meetingType",
          label: "类别",
          type: "select",
          placeholder: "请选择",
          width: "180",
          options: [],
        },
        {
          prop: "createTime",
          label: "创建时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
      ],
      columns: [
        { prop: "flowName", label: "会议流程名称", text: true },
        { prop: "meetingTypeName", label: "类型名称", text: true },
        { prop: "createName", label: "创建人", text: true },
        { prop: "createTime", label: "创建日期", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '260px',
          operationList: [
            {
              label: '发布',
              permission: 'meetingFlow:publish',
              buttonClick: this.handlePublish,
              isShow: (row, $index) => {
                if(this.tableData[$index].status === 1){
                    return false
                }else{
                    return true
                }
              }
            },
            {
              label: '编辑',
              permission: 'meetingFlow:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'meetingFlow:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      trueOrFalseTypeList:[
        {itemName: "是", id: 1},
        {itemName: "否", id: 0}
      ],
      meetingCycleList:[
        {itemName: "1天", id: "1"},
        {itemName: "7天", id: "7"},
        {itemName: "15天", id: "15"},
        {itemName: "一个月", id: "30"},
      ],
      noticeMethodTypeList: [
        {itemName: "系统消息", id: "1"},
        {itemName: "短信", id: "2"},
        {itemName: "语音广播", id: "3"},
        {itemName: "京办", id: "4"},
      ],
      meetingReminderList: [
        {itemName: "提前5分钟", id: "5"},
        {itemName: "提前10分钟", id: "10"},
        {itemName: "提前15分钟", id: "15"},
        {itemName: "提前30分钟", id: "30"},
        {itemName: "自定义时间", id: "0"},
      ],
      orgTree: [],
      organUnitProps: {
        multiple: false,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      meetingTypeList: [],
      // inspectionResultList: [],
      // fileBaseUrl: "",
      // fileList: [],
      // showIframe: false,
      // preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "660px",
      generalDialogTitle: "新增会议流程",

      form: {
        flowName: "",
        meetingType: "",
        meetingControls: "",
        meetingCycle: "",
        organUnit: "",
        attendUnitList: [],
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        // noticeMethod: "",
        // noticeMethod: "",
        // fileList:[]
      },
      rules: {
        flowName: [
          {required: true, message: '会议流程名称不能为空', trigger: 'blur'}
        ],
        meetingType: [
          {required: true, message: '会议类别不能为空', trigger: 'blur'}
        ],
        meetingControls: [
          {required: true, message: '是否使用会控不能为空', trigger: 'blur'}
        ],
        meetingCycle: [
          {required: true, message: '会议周期不能为空', trigger: 'blur'}
        ],
        organUnit: [
          {required: true, message: '承办单位不能为空', trigger: 'blur'}
        ],
        attendUnitList: [
          {required: true, message: '参会单位不能为空', trigger: 'blur'}
        ],
        noticeMethodList: [
          {required: true, message: '通知方式不能为空', trigger: 'blur'}
        ],
        meetingRecorded: [
          {required: true, message: '是否会议录播不能为空', trigger: 'blur'}
        ],
        meetingRegister: [
          {required: true, message: '是否会议报名不能为空', trigger: 'blur'}
        ],
        meetingReminder: [
          {required: true, message: '是否会议提醒不能为空', trigger: 'blur'}
        ],
        // meetingReminderTime: [
        //   {required: true, message: '会议提醒时间不能为空', trigger: 'blur'}
        // ],
      },
    };
  },
  mounted() {
    // this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleChangeMonth(value) {
      this.form.inspectionTime = conversionDateNotSecond(value);
    },
    handleArganUnitChange(value) {
      // 选择的反馈对象集合
      // this.form.feedbackList = value;
      // console.log("xc99999999", this.form.feedbackList);
    },
    handleAttendUnitChange(value) {
      // 选择的接收对象集合
      const checkedNodes = this.$refs.orgCascaderRef.getCheckedNodes(true);      
      const selectOrgData = checkedNodes.map((node) => node.data);
      this.form.attendUnitList = selectOrgData.map((node) => node.id);
      // console.log('xc12090129102',this.form.attendUnitList);
      // this.selectOrgData.forEach(nodes => {
      //     receiveList.push(nodes.id)
      // })
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          this.searchItems[0].options = this.meetingTypeList.map((item) => ({
            label: item.itemName,
            value: item.id
          }))
        }
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionResultList = await getItemList(inspectionResultType);
      //   this.searchItems[2].options = this.inspectionResultList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    //查看详情
    getRowDataInfo(row) {
      // this.fileList = [];
      // this.form.fileList = [];
      meetingManagementApi.queryMeetingFlowDetail({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        // if (data.fileList && data.fileList.length > 0) {
        //   data.fileList.forEach((row) => {
        //     this.fileList.push({
        //       name: row.fileName,
        //       url: this.fileBaseUrl + row.fileUrl,
        //       id: row.id,
        //     });
        //   });
        // } else {
        //   data.fileList = [];
        // }
        this.form = {
          ...data,
        };
      });
    },

    //新增
    handleAdd() {
      this.queryOrgTreeDataList();
      this.styleType = 1;
      this.dialogVisible = true;
      this.resetFormData();
      // this.form = {};
      // this.fileList = [];
      // this.form.fileList = [];
      this.generalDialogTitle = "新增会议流程";
    },

    //编辑
    handleEdit(row) {
      this.queryOrgTreeDataList();
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑会议流程";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看会议流程";
    },

    //删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        meetingManagementApi.deleteMeetingFlow({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('删除成功');
          this.handSubmitSuccess();
        })
      })
    },

    // 通报
    handlePublish(row) {
      this.$confirm('确定发布此会议流程, 是否继续?', '发布', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        meetingManagementApi.updateMeetingFlowStatus({ id: row.id, status: 1 }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('发布成功');
          this.handSubmitSuccess();
        })
      })
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await meetingManagementApi.queryMeetingFlowPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await meetingManagementApi.createMeetingFlow(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await meetingManagementApi.updateMeetingFlow(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },

    resetFormData() {
      this.form = {
        flowName: "",
        meetingType: "",
        meetingControls: "",
        meetingCycle: "",
        organUnit: "",
        attendUnitList: [],
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        // noticeMethod: "",
        // noticeMethod: "",
        // fileList:[]
      };
    },

    // // 新增上传方法
    // uploadFile(file) {
    //   // 文件大小校验
    //   this.fileList = [];
    //   const MAX_SIZE = 100 * 1024 * 1024; // 100MB
    //   if (file.file.size > MAX_SIZE) {
    //     this.$message.error("文件大小超过100MB限制");
    //     this.$refs.uploadRef.clearFiles();
    //     return;
    //   }
    //   const formData = new FormData();
    //   formData.append("file", file.file);

    //   systemManagementApi.uploadFile(formData).then((res) => {
    //     this.$refs.uploadRef.clearFiles();
    //     const { code, data, message, error } = res;
    //     if (code !== 0) return this.$message.error(message || error);
    //     this.$message.success("上传成功");
    //     setTimeout(() => {
    //       const fileUrl = this.fileBaseUrl + data.fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileType = getFileExtension(data.url);

    //       this.form.fileList.push(data);
    //       this.fileList.push({
    //         name: data.fileName,
    //         url: fileUrl,
    //         id: data.id,
    //       });
    //     }, 500);
    //   });
    // },
    // handleRemove(file, fileList) {
    //   this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    // },
    // handlePreview(file) {
    //   let fileUrl = file.url;
    //   const fileExtension = fileUrl.split(".").pop().toLowerCase();
    //   const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
    //   if (!previewableExtensions.includes(fileExtension)) {
    //     // 如果文件类型不支持直接预览，则重新拼接URL
    //     fileUrl ='http://' + getKKFilePreviewUrl() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
    //   } 
    //   window.open(fileUrl, '_blank');
    // },
    // handleExceed(files, fileList) {
    //   this.$message.warning("只能上传一个文件");
    //   // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    // },
    // beforeRemove(file, fileList) {
    //   return this.$confirm(`确定移除 ${file.name}？`);
    // },

    // // 关闭预览框
    // handleCloseIframe() {
    //   this.showIframe = false;
    // },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
