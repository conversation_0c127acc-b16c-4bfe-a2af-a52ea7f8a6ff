<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { dutyManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType } from "@/utils/dictionary";

import { conversionDateNotTime } from "@/utils/publicMethod";


export default {
  name: "DutyDetailStatistics",
  components: {
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "searchTime",
          label: "日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          label: "值班人员",
          prop: "userName",
          type: "input",
          placeholder: "请输入",
          width: "120",
        },
        {
          label: "所属单位",
          prop: "deptName",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        // {
        //   label: "值班岗位",
        //   prop: "inspectionType",
        //   type: "select",
        //   placeholder: "请选择",
        //   width: "150",
        //   options: [],
        // }
      ],
      columns: [
        { prop: "userName", label: "姓名", text: true },
        { prop: "deptName", label: "所属单位", text: true },
        { prop: "positionName", label: "值班岗位", text: true },
        { prop: "workNum", label: "工作日值班天数", text: true },
        { prop: "sleepNum", label: "休息日值班天数", text: true },
        { prop: "holidayNum", label: "法定节假日值班天数", text: true },
        { prop: "sumNum", label: "值班合计天数", text: true },
      ],
      tableData: [],

      inspectionTypeList: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  mounted() {
    this.registerHandlers();
    this.getTableDataList();
    // this.queryDictionaryType();
  },
  methods: {
    // 注册事件处理器
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.handleExportFile,
      });
    },

    // 导出表格
    async handleExportFile() {
      // 导出表格
      const param = {year:this.dutyYear, month:this.dutyMonth, dept:this.deptData.orgId};
      const res = await dutyManagementApi.exportDutyInfo(param);
      var blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
      var downloadElement = document.createElement('a');
      var href = window.URL.createObjectURL(blob); // 创建下载的链接
      downloadElement.href = href;
      downloadElement.download = this.tableTitle + '.xlsx'; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href); // 释放掉blob对象
    },

    //查询字典类型
    async queryDictionaryType() {
      try {
        this.inspectionTypeList = await getItemList(inspectionDictionaryType);
        this.searchItems[1].options = this.inspectionTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryDutyDetailsStatistics(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.searchTime && row.searchTime.length > 0) {
        row.startTime = conversionDateNotTime(row.searchTime[0])
        row.endTime = conversionDateNotTime(row.searchTime[1])
        delete row.searchTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
