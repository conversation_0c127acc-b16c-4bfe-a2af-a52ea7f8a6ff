<template>
  <div class="dispatcher-log-container">
    <portal-table
      ref="portalTableRef"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
export default {
  name: "DispatcherLog",
  components: {
    PortalTable,
  },
  data() {
    return {
      columns: [
        {
          prop: "content",
          label: "来电内容",
          text: true,
        },
        {
          prop: "time",
          label: "来电时间",
          text: true,
        },
        {
          prop: "unit",
          label: "来电单位",
          text: true,
        },
        {
          action: true,
          label: "操作",
          width: "350px",
          operationList: [
            {
              label: "播放录音",
              permission: "dispatcher:playAudio",
              buttonClick: this.handlePlayAudio,
              isShow: () => true,
            },
            {
              label: "导出录音",
              permission: "dispatcher:exportAudio",
              buttonClick: this.handleExportAudio,
              isShow: () => true,
            },
            {
              label: "生成接报",
              permission: "dispatcher:generateReport",
              buttonClick: this.handleGenerateReport,
              isShow: () => true,
            },
          ],
        },
      ],
      tableData: [
        {
          id: 1,
          content: "“7·9”京承高速天然气罐车侧翻",
          time: "07-09 06:55",
          unit: "市公安局交通管理局",
        },
        {
          id: 2,
          content: "北师大附中高一学生在家中坠楼身亡",
          time: "07-09 09:35",
          unit: "市公安局",
        },
        {
          id: 3,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 4,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 5,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 6,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 7,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 8,
          content: "西城区大栅栏街道XXXXXX",
          time: "06-22 11:52",
          unit: "西城区应急局",
        },
        {
          id: 9,
          content: "西城区大栅栏街道炸酱面馆起火",
          time: "06-22 12:52",
          unit: "西城区应急局",
        },
        {
          id: 10,
          content: "“12·21”京承高速天然气罐车侧翻",
          time: "12-21 06:55",
          unit: "119接警",
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 10,
      },
      searchItems: [
        {
          type: "input",
          prop: "call",
          label: "来电内容",
          placeholder: "请输入来电内容",
        },
        {
          type: "datetimerange",
          prop: "callTime",
          label: "来电时间",
          placeholder: "请输入来电时间",
        },
        {
          type: "input",
          prop: "callUnit",
          label: "来电单位",
          placeholder: "请输入来电单位",
        },
      ],
      loading: false,
    };
  },
  methods: {
    handleSearch() {},
    handleSizeChange() {},
    handleCurrentChange() {},
    handleSelectionChange() {},
  },
};
</script>

<style lang="scss" scoped>
.dispatcher-log-container {
  padding: 20px;
}
</style>
