/**
 * 权限检查混入
 * 为组件提供权限检查方法
 */
import { hasPermission, hasPagePermission } from "@/utils/permissionManager";
import { hasRole } from "@/utils/portalUtils";

export default {
  methods: {
    /**
     * 检查是否有指定权限（全局权限检查）
     * @param {string} permission - 权限标识
     * @param {string} parentPath - 父级路径（可选）
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, parentPath = "") {
      return hasPermission(permission, parentPath || this.$route?.path || "");
    },

    /**
     * 检查是否有页面级权限\支持页面隔离
     * @param {string} permission - 权限标识
     * @param {string} currentPagePath - 当前页面路径（可选，默认使用当前路由）
     * @returns {boolean} 是否有权限
     */
    hasPagePermission(permission, currentPagePath = "") {
      const pagePath = currentPagePath || this.$route?.path || "";
      return hasPagePermission(permission, pagePath);
    },

    /**
     * 检查是否有指定角色
     * @param {string} role - 角色名称
     * @returns {boolean} 是否有角色
     */
    hasRole(role) {
      return hasRole(role);
    },

    /**
     * 检查是否有管理员权限
     * @returns {boolean} 是否是管理员
     */
    isAdmin() {
      return this.hasRole("admin") || this.hasRole("超级管理员");
    },
  },
};
