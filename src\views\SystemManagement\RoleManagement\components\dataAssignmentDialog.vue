<template>
  <div
    class="data-Assignment-dialog-container"
    v-if="dataAssignmentDialogVisible"
  >
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入角色名称"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="角色分级" prop="userClass">
          <el-input
            v-model="form.userClass"
            placeholder="请输入角色名称"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="分配组织机构" prop="resourceMenu">
          <div class="allocated-menu">
            <el-tree
              show-checkbox
              node-key="id"
              :data="resourceMenu"
              :props="defaultProps"
              :default-checked-keys="defaultCheckedKeys"
              @check="handleCheckChange"
              ref="menuTree"
            />
          </div>
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi } from "@/api";
export default {
  name: "DataAssignmentDialog",
  data() {
    return {
      dataAssignmentDialogVisible: false,
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "分配数据",
      form: {
        id: "",
        roleName: "",
        userClass: "",
      },
      resourceMenu: [],
      rules: {},
      defaultProps: {
        children: "children",
        label: "orgName",
      },
      checkedKeys: [],
      defaultCheckedKeys: [],
    };
  },
  components: {
    GeneralDialog,
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const params = {
            roleId: this.form.id,
            orgIdList: this.checkedKeys,
          };
          this.dialogVisible = false;
          systemManagementApi.createRoleOrg(params).then((res) => {
            this.$message.success("操作成功");
            this.$emit("update");
          });
        }
      });
    },
    handleCheckChange() {
      const checkedNodes = this.$refs.menuTree.getCheckedNodes();
      const leafNodes = checkedNodes.filter(
        (node) => !node.children || node.children.length === 0
      );
      this.checkedKeys = leafNodes.map((node) => node.id);
    },
    // 查询菜单
    async queryOrg() {
      const params = {};
      try {
        let res = await systemManagementApi.queryOrg(params);
        console.log(res, "查询组织机构");
        this.resourceMenu = res.data;
      } catch (error) {}
    },
    // 查询分配数据
    async queryRoleOrg() {
      const params = {
        roleId: this.form.id,
      };
      try {
        let res = await systemManagementApi.queryRoleOrg(params);
        console.log(res, "查询分配数据");
        this.setCheckedKeys(res.data);
      } catch (error) {}
    },
    // 设置默认选中
    setCheckedKeys(checkKeys) {
      this.defaultCheckedKeys = this.getLeafCheckedKeys(checkKeys);
      this.checkedKeys = this.defaultCheckedKeys;
      this.$nextTick(() => {
        if (this.$refs.menuTree) {
          this.$refs.menuTree.setCheckedKeys(this.defaultCheckedKeys);
        }
      });
    },
    // 定义递归函数，获取所有选中叶子节点的id
    getLeafCheckedKeys(treeData) {
      let result = [];
      treeData.forEach((node) => {
        if (!node.children || node.children.length === 0) {
          if (node.selected) {
            result.push(node.orgId);
          }
        }
        if (node.children && node.children.length > 0) {
          result = result.concat(this.getLeafCheckedKeys(node.children));
        }
      });
      return result;
    },
  },
  mounted() {
    this.queryOrg();
  },
};
</script>

<style scoped>
.data-Assignment-dialog-container {
  height: 100%;
}
.allocated-menu {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}
</style>