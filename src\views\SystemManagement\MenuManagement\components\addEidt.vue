<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="菜单类型" prop="menuTypeModel">
          <el-select
            v-model="formData.menuTypeModel"
            :disabled="styleType === 2"
            placeholder="请选择"
            value-key="menuType"
            @change="changeMenuType"
          >
            <el-option
              v-for="data in menuTypeList"
              :key="data.menuType"
              :label="data.name"
              :value="data"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            formData.menuTypeModel == null
              ? '模块名称'
              : formData.menuTypeModel.menuType === 0
              ? '模块名称'
              : formData.menuTypeModel.menuType === 1
              ? '一级菜单名称'
              : formData.menuTypeModel.menuType === 2
              ? '二级菜单名称'
              : '按钮名称'
          "
          prop="name"
        >
          <el-input
            v-model="formData.name"
            autocomplete="off"
            placeholder="请输入菜单名称"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="父级菜单" prop="parentMenuPath">
          <el-cascader
            v-model="formData.parentMenuPath"
            :options="parentMenuTree"
            :props="cascaderProps"
            :show-all-levels="false"
            clearable
            filterable
            placeholder="请输入/选择父级菜单"
            style="width: 100%"
            @change="handleParentMenuChange"
          />
        </el-form-item>

        <!-- 有些页面没有面包屑按钮，所有非必填 -->
        <el-form-item label="菜单访问路径/顶部面包屑按钮访问路径" prop="path">
          <el-input
            v-model="formData.path"
            autocomplete="off"
            placeholder="请输入访问路径"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="菜单权限标识/列表按钮标识" prop="permission">
          <el-input
            v-model="formData.permission"
            autocomplete="off"
            placeholder="请输入菜单权限标识"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="formData.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="tel"
            @input="handlePhoneInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { systemManagementApi } from "@/api";

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 1新增; 2修改
      menuTypeList: [],
      parentMenuList: [],
      parentMenuTree: [], // 级联选择器的树形数据
      formData: {
        menuTypeModel: null,
        parentMenuModel: null,
        parentMenuPath: [], // 级联选择器的值
        name: "",
        path: "",
        // systemAppId: 20250318 //系统ID
      },
      // 级联选择器配置
      cascaderProps: {
        value: "id",
        label: "name",
        children: "children",
        checkStrictly: true, // 允许选择任意级别
      },
      rules: {
        menuTypeModel: [
          { required: true, message: "菜单类型不能为空", trigger: "blur" },
        ],
        parentMenuPath: [
          { required: true, message: "父级菜单不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        // path: [
        //   { required: true, message: "访问路径不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  mounted() {},
  methods: {
    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const {
            menuTypeModel,
            parentMenuPath,
            name,
            path,
            sort,
            // icon,
            permission,
          } = this.formData;
          const params = {
            menuType: menuTypeModel.menuType,
            parentId:
              parentMenuPath && parentMenuPath.length > 0
                ? parentMenuPath[parentMenuPath.length - 1]
                : 0,
            name: name,
            path: path,
            sort: sort,
            // icon: icon,
            permission: permission,
            delFlag: 0, //是否删除：0=否，1=是
            // systemAppId: 20250318 //系统ID
          };
          if (this.styleType === 1) {
            const res = await systemManagementApi.createMenu(params);
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("新增成功");
              this.$emit("ok");
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          } else {
            params.id = this.model.id;
            const res = await systemManagementApi.updateMenu(params);
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("修改成功");
              this.$emit("ok");
              this.close();
            } else {
              this.$message.error(error);
            }
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.reset();
      this.$emit("ok", 1);
    },
    reset() {
      this.formData = {
        menuTypeModel: null,
        parentMenuModel: null,
        parentMenuPath: [],
        name: "",
        path: "",
        sort: "",
      };
      this.model = {};
      this.menuTypeList = [];
      this.parentMenuList = [];
      this.parentMenuTree = [];
    },
    addFormFn() {
      this.reset();
      this.styleType = 1;
      this.getMenuTypeList();
    },
    edit(data) {
      this.styleType = 2;
      this.getMenuTypeList();
      this.model = Object.assign({}, data);
      const model = this.model;
      this.formData = {
        menuTypeModel: { menuType: model.menuType, name: model.name },
        parentMenuModel: {
          id: model.parentId,
          name: model.menuType === 0 ? "无" : model.name,
        },
        parentMenuPath: model.parentId ? [model.parentId] : [],
        name: model.name,
        path: model.path,
        sort: model.sort,
        permission: model.permission,
      };
      this.changeMenuType({ menuType: model.menuType, name: model.name });
    },
    async getMenuTypeList() {
      this.menuTypeList = [
        { name: "模块", menuType: 0 },
        { name: "一级菜单", menuType: 1 },
        { name: "二级菜单", menuType: 2 },
        { name: "按钮", menuType: 3 },
      ];
    },
    handlePhoneInput(value) {
      // 只允许输入数字 使用正则表达式移除非数字字符
      this.formData.sort = value.replace(/\D/g, "");
    },
    changeMenuType(v) {
      if (v.menuType > 0) {
        // 如果选择的不是模块，则获取所有的父级菜单列表
        this.getParentMenuTree(v.menuType - 1);
      } else {
        // 如果选择的是模块，则没有父级菜单，不能选择
        this.parentMenuTree = [{ name: "无", id: 0, children: [] }];
        this.formData.parentMenuPath = [0];
      }
    },
    async getParentMenuTree(maxMenuType) {
      // 获取所有菜单数据，然后过滤出符合条件的菜单作为父级菜单选项
      const params = {};
      const res = await systemManagementApi.getMenu(params);
      const { code, data, error } = res;
      if (code === 0) {
        // 过滤出菜单类型小于等于maxMenuType的菜单作为可选父级菜单
        this.parentMenuTree = this.filterMenuByType(data, maxMenuType);
      } else {
        this.$message.error(error);
      }
    },
    // 过滤菜单，只保留菜单类型小于等于指定类型的菜单
    filterMenuByType(menuList, maxMenuType) {
      const result = [];
      menuList.forEach((menu) => {
        if (menu.menuType <= maxMenuType) {
          const filteredMenu = {
            id: menu.id,
            name: menu.name,
            menuType: menu.menuType,
            children: [],
          };
          if (menu.children && menu.children.length > 0) {
            filteredMenu.children = this.filterMenuByType(
              menu.children,
              maxMenuType
            );
          }
          result.push(filteredMenu);
        }
      });
      return result;
    },
    // 级联选择器值变化处理
    handleParentMenuChange(value) {
      // 更新parentMenuModel以保持兼容性
      if (value && value.length > 0) {
        const selectedId = value[value.length - 1];
        this.formData.parentMenuModel = { id: selectedId };
      } else {
        this.formData.parentMenuModel = null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
