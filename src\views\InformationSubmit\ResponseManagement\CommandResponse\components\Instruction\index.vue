<template>
  <!-- 指令页面 -->
  <div class="instruction-index">
    <template v-if="!isShowTask">
      <portal-table
        style="padding: 20px"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="name"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @handle-selection-change="handleSelectionChange"
      />
      <div class="add-back-button">
        <el-button
          type="primary"
          plain
          icon="el-icon-back"
          @click="handleBackClick"
        >
          返回
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-s-promotion"
          @click="handleIssueCommand"
        >
          任务下发
        </el-button>
      </div>

      <!-- 新增弹窗 -->
      <general-dialog
        :dialog-visible="dialogVisible"
        :dialog-width="dialogWidth"
        :general-dialog-title="generalDialogTitle"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      >
        <el-form
          ref="addForm"
          :model="form"
          :rules="rules"
          class="add-form"
          label-width="85px"
        >
          <el-form-item label="指令名称" prop="name">
            <el-input
              v-model="form.commandName"
              autocomplete="off"
              placeholder="请输入"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </general-dialog>

      <!-- 任务下发弹窗 -->
      <general-dialog
        :dialog-visible="issueDialogVisible"
        :dialog-width="issueDialogWidth"
        :general-dialog-title="generalIssueDialogTitle"
        @cancel="handleCancel"
        @confirm="handleIssueSubmit"
      >
        <portal-table
          style="padding: 20px"
          :table-height="565"
          :showAddButton="false"
          :columns="issueColumns"
          :pagination="issuePagination"
          :table-data="issueTableData"
          row-key="name"
          @handle-size-change="handleIssueSizeChange"
          @handle-current-change="handleIssueCurrentChange"
          @handle-selection-change="handleIssueSelectionChange"
        />
      </general-dialog>
    </template>

    <!-- 任务页面 -->
    <task-index
      v-if="isShowTask"
      :instruction-data="instructionData"
      @handleTaskCancel="handleCancel"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import TaskIndex from "@/views/InformationSubmit/ResponseManagement/CommandResponse/components/Task/index.vue";
import { commandConfigurationApi, emergencyEventInfoApi } from "@/api";

export default {
  name: "InstructionIndex",
  components: { TaskIndex, GeneralDialog, PortalTable },
  props: {
    commandResponseData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isShowTask: false,
      instructionData: {},
      selectedRows: [],
      searchItems: [
        {
          prop: "commandName",
          label: "指令名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
      ],
      searchParams: null,
      tableData: [],
      columns: [
        { prop: "commandName", label: "指令名称", text: true },
        { prop: "createTime", label: "创建时间", text: true },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "任务",
              permission: "instruction:config",
              buttonClick: this.handleTask,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "编辑",
              permission: "instruction:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "instruction:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      isAdd: true,
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "添加指令",
      form: {
        commandName: "",
      },
      rules: {
        commandName: [
          { required: true, message: "请输入指令名称", trigger: "blur" },
        ],
      },

      issueDialogVisible: false,
      issueDialogWidth: "1100px",
      generalIssueDialogTitle: "任务下发",
      issueColumns: [
        { prop: "infoTitle", label: "事件标题", text: true },
        { prop: "infoTime", label: "事发时间", text: true },
        { prop: "createTime", label: "接报时间", text: true },
        { prop: "infoType", label: "事件类型", text: true },
        { prop: "infoReportingUnit", label: "上报单位", text: true },
        { prop: "responseLevel", label: "响应级别", text: true },
      ],
      issuePagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      issueTableData: [],
      issueSelectedRows: [],
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData(searchParams = {}) {
      let params = {
        reportId: this.commandResponseData.id,
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
        ...searchParams,
      };
      if (searchParams.keyWord) {
        params.keyWord = searchParams.keyWord;
      }
      const { code, data, message, error } =
        await commandConfigurationApi.queryInstructionTwoPage(params);
      if (code !== 0) return this.$message.error(message || error);

      this.tableData = data.items;
      this.pagination.total = data.total;
    },
    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1; // 搜索时重置到第一页
      this.fetchData(row); // 重新加载数据
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleAdd() {
      this.isAdd = true;
      this.dialogVisible = true;
    },

    handleEdit(row) {
      this.isAdd = false;
      this.generalDialogTitle = "修改指令";
      this.dialogVisible = true;
      this.form = {
        ...row,
      };
    },

    //任务配置
    handleTask(row) {
      this.isShowTask = true;
      this.instructionData = row;
      this.instructionData.reportId = this.commandResponseData.id;
    },

    handleDelete(row) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const { code, message, error } =
            await commandConfigurationApi.deleteInstruction({
              id: row.id,
            });
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success("删除成功");
          await this.fetchData();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          let params = {
            reportId: this.commandResponseData.id,
            commandName: this.form.commandName,
          };
          if (this.isAdd) {
            const { code, message, error } =
              await commandConfigurationApi.createInstruction(params);
            if (code !== 0) return this.$message.error(message || error);
            this.$message.success("添加成功");
          } else {
            params.id = this.form.id;
            const { code, message, error } =
              await commandConfigurationApi.updateInstruction(params);
            if (code !== 0) return this.$message.error(message || error);
            this.$message.success("修改成功");
          }
          this.handleCancel();
        }
      });
    },

    //任务下发弹窗
    handleIssueCommand() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要下发的指令");
        return;
      }
      this.fetchEventData();
      this.issueDialogVisible = true;
    },

    //查询事件列表
    async fetchEventData() {
      let params = {
        page: this.issuePagination.currentPage,
        count: this.issuePagination.pageSize,
      };

      const { code, data, message, error } =
        await emergencyEventInfoApi.queryEmergencyInfoList(params);
      if (code !== 0) return this.$message.error(message || error);

      this.issueTableData = data.items;
      this.issuePagination.total = data.total;
    },

    handleIssueSizeChange(size) {
      this.issuePagination.pageSize = size;
      this.fetchEventData();
    },

    handleIssueCurrentChange(page) {
      this.issuePagination.currentPage = page;
      this.fetchEventData();
    },

    handleIssueSelectionChange(selection) {
      this.issueSelectedRows = selection;
    },

    //任务下发提交
    async handleIssueSubmit() {
      if (
        this.selectedRows.length === 0 ||
        this.issueSelectedRows.length === 0
      ) {
        this.$message.warning("请选择要下发到的事件");
        return;
      }

      let commandIds = this.selectedRows.map((row) => row.id);
      let infoIds = this.issueSelectedRows.map((row) => row.id);
      /*console.log(commandIds);
      console.log(infoIds);*/

      let params = {
        commandIds,
        infoIds,
      };
      const { code, message, error } =
        await commandConfigurationApi.assignTasksInstruction(params);
      if (code !== 0) return this.$message.error(message || error);
      this.$message.success("任务下发成功");
      this.issueDialogVisible = false;
    },

    handleCancel() {
      this.isShowTask = false;
      this.dialogVisible = false;
      this.issueDialogVisible = false;
      this.form = {
        commandName: "",
      };
      this.fetchData();
    },

    handleBackClick() {
      this.$emit("handleCancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.instruction-index {
  position: relative;
  .add-back-button {
    position: absolute;
    top: 20px;
    right: 20px;

    .el-button--primary.is-plain {
      color: var(--themeColor);
      background: var(--lightGray1);
      border-color: var(--themeColor) !important;
    }
  }
}
::v-deep {
  .add-form {
    padding: 30px 50px 5px 50px;
  }
}
</style>
