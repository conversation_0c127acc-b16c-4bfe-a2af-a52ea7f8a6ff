<template>
    <!-- 右侧内容区域 -->
    <div class="contact-book-container">
        <div class="contact-book-main">
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <SearchForm v-model="formData" :form-config="searchConfig" :tips="searchTips" :show-tips="false"
                    @search="handleSearch" @reset="handleReset" ref="searchForm" />
            </div>
            <!-- 表格区域 -->
            <div class="wrapper">
                <div class="customer-char">
                    <div class="chart-titile">编发短信条数</div>
                    <div class="chart-view">
                        <Echart :options="lineEditOptions" />
                    </div>
                </div>

                <div class="customer-char">
                    <div class="chart-titile">发送人次</div>
                    <div class="chart-view">
                        <Echart :options="barSendOptions" />
                    </div>
                </div>
                <div class="customer-char">
                    <div class="chart-titile">短信类型</div>
                    <div class="chart-view">
                        <Echart :options="barTypeOptions" />
                    </div>
                </div>
                <div class="customer-char">
                    <div class="chart-titile">发送总条数 </div>
                    <div class="chart-view">
                        <Echart :options="lineSendTototalOptions" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script name="TextMessageAnalysis">
import analysisApi from '@/api/textMessageAnalysis'
import Echart from "@/components/echarts.vue";
import SearchForm from "@/components/SearchForm.vue";
export default {
    components: {
        Echart,
        SearchForm,
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "type",
                    label: "类型",
                    type: "select",
                    placeholder: "输选择类型",
                    width: "200px",
                    options: [
                        { label: "日", value: "1" },
                        { label: "年", value: "2" },
                    ],
                },
                {
                    prop: "datelist",
                    label: "统计时间",
                    type: "daterange",
                    placeholder: "输选择统计时间",
                    width: "200px",
                    format: 'YYYY-MM-DD'
                },

            ],
            searchTips: [],
            lineEditOptions: {
                color: ['#FECE43'],
                grid: {
                    top: '5%',
                    left: '10%',
                    right: '5%',
                    bottom: '20%',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                    valueFormatter(value) {
                        return value + '条'
                    },
                },
                xAxis: {
                    type: 'category',
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.1)',
                        },
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0,0,0,0.01)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                yAxis: {
                    type: 'value',
                    // max: "100",
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.6)',
                        },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.05)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                dataset: {
                    source: [
                        // [
                        //     "06月18日",
                        //     "06月19日",
                        //     "06月20日",
                        //     "06月21日",
                        //     "06月22日",
                        //     "06月23日",
                        //     "06月24日"
                        // ],
                        // [160, 150, 140, 150, 170, 162, 184],
                    ],
                },
                series: [
                    {
                        name: '发送数量',
                        type: 'line',
                        smooth: true,
                        symbolSize: 0,
                        lineStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 1,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255,184,100,0.8)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255,58,76,1)',
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                        seriesLayoutBy: 'row',
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 1,
                                y: 0,
                                x2: 1,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255,184,100,0.3)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255,58,76,0)',
                                    },
                                ],
                                global: false, // 缺省为 false
                            }
                        }
                    }
                ],
            },
            barSendOptions: {
                color: ['#ffb767', '#f67c4d', '#ff3a4c'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                    valueFormatter(value) {
                        return value + '次'
                    },
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '10%',
                    top: '10%',
                    containLabel: true,
                },
                legend: {
                    right: 40,
                    textStyle: {
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                xAxis: {
                    type: 'category',
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.1)',
                        },
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0,0,0,0.01)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                yAxis: {
                    type: 'value',
                    // max: "100",
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.6)',
                        },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.05)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                dataset: {
                    source: [
                        // ['12:00', '13:00', '14:00', '15:00', '16:00', '17:00'],
                        // [22, 44, 55, 66, 45, 35],
                        // [22, 44, 55, 66, 45, 35],
                    ],
                },
                series: [
                    {
                        name: '发送成功数',
                        type: 'bar',
                        barWidth: '20%',
                        seriesLayoutBy: 'row',
                    },
                    {
                        name: '发送失败数',
                        type: 'bar',
                        barWidth: '20%',
                        seriesLayoutBy: 'row',
                    },
                ],
            },
            barTypeOptions: {
                color: ['#ffb767', '#f67c4d', '#ff3a4c'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '10%',
                    top: '10%',
                    containLabel: true
                },
                legend: {
                    data: ['高温', '空气', '大风', '暴雨', '雨量', '雷电', '大雾']
                },
                xAxis: {
                    type: 'category',
                    data: ['高温', '空气', '大风', '暴雨', '雨量', '雷电', '大雾'],
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0,0,0,0.01)'
                        }
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.6)'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)'
                    }
                },
                series: [
                    {
                        type: 'bar',
                        barWidth: '20%',
                        data: []
                    }
                ]
            },
            lineSendTototalOptions: {
                color: ['#FECE43'],
                grid: {
                    top: '5%',
                    left: '10%',
                    right: '5%',
                    bottom: '20%',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                    valueFormatter(value) {
                        return value + '条'
                    },
                },
                xAxis: {
                    type: 'category',
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.1)',
                        },
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0,0,0,0.01)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                yAxis: {
                    type: 'value',
                    // max: "100",
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.6)',
                        },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0,0,0,0.05)',
                        },
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: 'rgba(0,0,0,0.6)',
                    },
                },
                dataset: {
                    source: [
                        // [
                        //     "06月18日",
                        //     "06月19日",
                        //     "06月20日",
                        //     "06月21日",
                        //     "06月22日",
                        //     "06月23日",
                        //     "06月24日"
                        // ],
                        // [160, 150, 140, 150, 170, 162, 184],
                    ],
                },
                series: [
                    {
                        name: '发送数量',
                        type: 'line',
                        smooth: true,
                        symbolSize: 0,
                        lineStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 1,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255,184,100,0.8)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255,58,76,1)',
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                        seriesLayoutBy: 'row',
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 1,
                                y: 0,
                                x2: 1,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255,184,100,0.3)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255,58,76,0)',
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                    }
                ]
            },
            formData: {
                datelist: [],
                type: '1',
                year: ''
            },


        };
    },
    computed: {

    },
    watch: {
        'formData.type': {
            handler() {
                if (this.formData.type == '1') {
                    this.searchConfig = [
                        {
                            prop: "type",
                            label: "类型",
                            type: "select",
                            placeholder: "输选择类型",
                            width: "200px",
                            options: [
                                { label: "日", value: "1" },
                                { label: "年", value: "2" },
                            ],
                        },
                        {
                            prop: "datelist",
                            label: "统计时间",
                            type: "daterange",
                            placeholder: "输选择统计时间",
                            width: "200px",
                            format: 'YYYY-MM-DD'
                        },
                    ]
                    let startTime = this.getLastNDays()
                    let endTime = this.geCurrentDays()
                    this.formData.datelist = [startTime, endTime]
                } else if (this.formData.type == '2') {
                    this.searchConfig = [
                        {
                            prop: "type",
                            label: "类型",
                            type: "select",
                            placeholder: "输选择类型",
                            width: "200px",
                            options: [
                                { label: "日", value: "1" },
                                { label: "年", value: "2" },
                            ],
                        },
                        {
                            prop: "year",
                            label: "统计时间",
                            type: "dateYear",
                            placeholder: "输选择统计时间",
                            width: "200px",
                            format: 'YYYY-MM-DD'
                        },
                    ]
                    this.formData.year = this.geCurrentDaysYear()
                }
            },
            deep: true
        }
    },
    methods: {
        handleSearch() {
            this.lineEditOptions.dataset.source = []
            this.barSendOptions.dataset.source = []
            this.barTypeOptions.legend.data = []
            this.barTypeOptions.xAxis.data = []
            this.barTypeOptions.series[0].data = []
            this.lineSendTototalOptions.dataset.source = []
            let data = {
                type: this.formData.type,
            }
            if (data.type == 1) {
                data.startDate = this.formData.datelist[0]
                data.endDate = this.formData.datelist[1]
            }
            if (data.type == 2) {
                data.year = this.formData.year
            }
            this.sendSmsNumber(data)
            this.sendPeopleNumber(data)
            this.templateTypeNumber(data)
            this.sendItemsNumber(data)
        },
        handleReset() {
            this.formData.type = ''
            this.formData.type = '1'
            this.handleSearch()
        },
        //
        //编发短信
        sendSmsNumber(data) {
            analysisApi.querySendSmsNumber({
                ...data
            }).then((res) => {
                this.lineEditOptions.dataset.source.push(res.data.date)
                this.lineEditOptions.dataset.source.push(res.data.number)
            })
        },

        //发送人次
        sendPeopleNumber(data) {
            analysisApi.querySendPeopleNumber({
                ...data
            }).then((res) => {
                this.barSendOptions.dataset.source.push(res.data.date)
                this.barSendOptions.dataset.source.push(res.data.success)
                this.barSendOptions.dataset.source.push(res.data.error)

            })
        },
        //分类型统计
        templateTypeNumber(data) {
            analysisApi.queryTemplateTypeNumber({
                ...data
            }).then((res) => {
                res.data.type.map((val) => {
                    this.barTypeOptions.legend.data.push(val)
                    this.barTypeOptions.xAxis.data.push(val)
                })
                res.data.num.map((val) => {
                    this.barTypeOptions.series[0].data.push(val)
                })
            })
        },
        //发送总条数
        sendItemsNumber(data) {
            analysisApi.querySendItemsNumber({
                ...data
            }).then((res) => {
                this.lineSendTototalOptions.dataset.source.push(res.data.date)
                this.lineSendTototalOptions.dataset.source.push(res.data.num)
            })
        },
        // 当前日期前往7天
        getLastNDays() {
            const currentDate = new Date();
            const tempDate = new Date(currentDate);
            tempDate.setDate(tempDate.getDate() - 6);
            const year = tempDate.getFullYear();
            const month = String(tempDate.getMonth() + 1).padStart(2, '0');
            const day = String(tempDate.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`
        },
        // 当前日期
        geCurrentDays(n = 7) {
            const currentDate = new Date();
            const tempDate = new Date(currentDate);
            tempDate.setDate(tempDate.getDate());
            const year = tempDate.getFullYear();
            const month = String(tempDate.getMonth() + 1).padStart(2, '0');
            const day = String(tempDate.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`
        },
        // 获取当前年
        geCurrentDaysYear() {
            const currentDate = new Date();
            const tempDate = new Date(currentDate);
            tempDate.setDate(tempDate.getDate());
            const year = tempDate.getFullYear();
            return `${year}`
        },

    },
    mounted() {
        this.$nextTick(() => {
            let startTime = this.getLastNDays()
            let endTime = this.geCurrentDays()
            this.formData.datelist = [startTime, endTime]
            this.handleSearch()
        })

    }
}
</script>

<style scoped lang="scss">
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}

.search-wrapper {
    margin-bottom: 8px;
}

// .wrapper {
//     flex: 1;
//     overflow: hidden;
//     transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
// }

.wrapper {
    flex: 1;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-wrap: wrap;

    .customer-char {
        display: flex;
        flex-direction: column;
        width: calc((100% - 10px) / 2);
        height: 50%;

        // margin-bottom: 10px;
        &:nth-child(2n) {
            margin-left: 10px;
        }

        .chart-titile {
            font-size: 20px;
            font-weight: bold;
            padding: 15px 30px;
        }

        .chart-view {
            flex: 1;
        }
    }

}
</style>