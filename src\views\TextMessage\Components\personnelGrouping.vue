<!-- 选择人员分组 -->
<template>
  <general-dialog :dialog-visible="dialogVisible" :general-dialog-title="'选择人员分组'" dialog-width="800px"
    @cancel="handleClose" @confirm="handleSubmit">
    <div class="add-form">
      <el-tabs v-model="tabsVal" type="border-card" class="el-tabs-grouping" @tab-click="onPaneSwitch">
        <el-tab-pane v-for="item in paneList" :key="item.id" :label="item.label" :name="item.id">
          <div class="tab-pane">
            <ul class="grouping-list">
              <li class="grouping-list_item" v-for="item in groupingList" :key="item.id"
                :class="{ 'grouping-actived': item.id == currentGroup.id }" @click="handleGroup(item)">
                {{ item.groupName }}
              </li>
            </ul>
            <div class="grouping-box">
              <div class="candidate">
                <div class="candidate-title">
                  <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">
                  </el-checkbox>
                  候选分组人员
                </div>
                <div class="candidate-box">
                  <el-checkbox-group v-model="candidate" @change="handleCheckedCitiesChange">
                    <el-checkbox v-for="item in candidateList" :label="item.userId" :value="item.userId"
                      :key="item.userId">
                      {{ item.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div class="highlight">
                <div class="highlight-title">
                  选中分组人员
                </div>
                <div class="highlight-box">
                  <el-tag class="highlight-tag" v-for="(tag, index) in highlightList" :key="tag.id" closable
                    :type="tag.id" @close="handleDelete(tag, index)">
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
import { textMessageSendApi } from "@/api";
export default {
  name: '',
  components: {
    GeneralDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      tabsVal: '1',//切换分组状态
      paneList: [
        {
          label: '常用分组',
          id: '1'
        },
        // {
        //   label: '业务分组',
        //   id: '2'
        // }
      ],// 分组数据
      groupingList: [],// 单位
      currentGroup: {},//选中的单位
      candidate: [],// 候选人员
      candidateList: [

      ],
      candidateAllList: [],
      highlightList: [

      ], // 选中人员
      checkAll: false,
      isIndeterminate: true
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  methods: {
    // 切换分组
    onPaneSwitch() {
      this.getGroupList()
    },
    // 全选 
    handleCheckAllChange(val) {
      if (val) {
        if (this.candidate.length) {
          this.candidate = this.candidate.filter((v) => {
            return v !== this.currentGroup.id
          })
        }
        this.candidate = this.candidate.concat(this.candidateList.flatMap(({ userId }) => [userId]))
      } else {
        if (this.candidate.length) {
          const userIds = new Set(this.candidateList.map(user => user.userId));
          this.candidate = this.candidate.filter(char => !userIds.has(char));
        }
      }
    },
    // 选择候补人员
    handleCheckedCitiesChange(value) {

    },
    // 删除选中的人员
    handleDelete(item) {
      this.highlightList.forEach((person, index) => {
        if (item.userId == person.userId && item.name == person.name) {
          this.highlightList.splice(index, 1)
        }
      })
      this.candidate.forEach((userId, index) => {
        if (item.userId == userId) {
          this.candidate.splice(index, 1)
        }
      })
    },
    // 保存
    handleSubmit() {
      this.dialogVisible = false
      this.$emit('update:modelValue', this.highlightList)
    },
    // 关闭
    handleClose() {
      this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.dialogVisible = false;
          this.resetForm();
        })
        .catch(() => {
          // 取消关闭
        })
    },
    // 选择分组并获取人员列表
    handleGroup(item) {
      this.currentGroup = item
      this.getPeopleList()
    },
    //获取人员列表
    async getPeopleList() {
      let id = this.currentGroup.id
      let candidateList = []
      let requestParams = await textMessageSendApi.queryUserInfo({
        id: id
      });
      candidateList = requestParams.data
      this.candidateList = candidateList.map((item) => {
        return {
          name: item.userName,
          userId: item.id,
          mobile: item.phone,
          groupId: this.currentGroup.id,
          groupName: this.currentGroup.groupName,
          isChecked: false
        }
      })
      const mergedList = [...this.candidateAllList, ...this.candidateList].reduce((map, item) => {
        map.set(item.userId, item); // 使用userId作为唯一键
        return map;
      }, new Map()).values();
      this.candidateAllList = Array.from(mergedList); // 转换为数组
    },
    // 获取分组列表
    async getGroupList() {
      let type = String(this.tabsVal);
      switch (type) {
        case '1':
          let requestParams = await textMessageSendApi.queryGroupManageList();
          this.groupingList = requestParams.data
          this.handleGroup(this.groupingList[0])
          break;
        default:
          break;
      }
    },
    // 重置
    resetForm() {
      this.tabsVal = '1';
      this.checkAll = false;
      this.isIndeterminate = true;
      this.candidate = []
      this.highlightList = []

    },
    incoming(val) {
      this.candidate = val.map((row) => {
        return row.userId
      })
      this.candidateAllList = val
    }
  },
  watch: {
    candidate: {
      handler() {
        this.highlightList = []
        let current = []
        if (this.candidate.length) {
          this.candidate.map((value) => {
            this.candidateAllList.map((row) => {
              if (value == row.userId) {
                this.highlightList.push(row)
              }
            })
            this.candidateList.map((row) => {
              if (value == row.userId) {
                current.push(row)
              }
            })
          })
        }
        this.checkAll = current.length === this.candidateList.length
        this.isIndeterminate = current.length > 0 && current.length < this.candidateList.length
      },
      deep: true
    },
    currentGroup: {
      handler() {
        let current = []
        if (this.candidate.length) {
          this.candidate.map((value) => {
            this.candidateList.map((row) => {
              if (value == row.userId) {
                current.push(row)
              }
            })
          })
        }
        this.checkAll = current.length === this.candidateList.length
        this.isIndeterminate = current.length > 0 && current.length < this.candidateList.length
      },
      deep: true
    },
  },
  
  mounted() {
    this.tabsVal = '1'
   
    this.getGroupList()
  }
}
</script>

<style scoped lang="scss">
.tab-pane {
  display: flex;

  .grouping-list {
    min-height: 400px;
    overflow-y: auto;
    list-style-type: none;
    width: 200px;
    border-right: 1px solid #DCDFE6;
    margin: 0px;
    padding: 10px;

    .grouping-list_item {
      line-height: 40px;
    }

    .grouping-actived {
      color: #409eff;
    }
  }

  .grouping-box {
    padding: 10px;
    flex: 1;

    .candidate {
      .candidate-title {
        font-weight: 700;
        padding-bottom: 20px;
      }

      .candidate-box {
        height: 200px;
        padding-left: 10px;
      }
    }

    .highlight {
      .highlight-title {
        font-weight: 700;
        padding-bottom: 20px;
      }

      .highlight-box {
        height: 200px;

        .highlight-tag {
          margin: 3px 7px;
        }
      }
    }
  }
}


.el-tabs-grouping {
  box-shadow: none
}

::v-deep(.el-tabs__content) {
  padding: 0px;
}
</style>