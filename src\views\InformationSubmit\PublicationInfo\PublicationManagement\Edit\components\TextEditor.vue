<template>
  <div class="text-editor">
    <!-- 文本编辑组件 - Component -->
    <el-input
      v-if="isEditMode"
      v-model="localContent"
      type="textarea"
      :rows="rows"
      :placeholder="placeholder"
      @input="handleContentChange"
      class="content-textarea"
    />
    <div v-else class="content-text">
      {{ localContent }}
    </div>
  </div>
</template>

<script>
export default {
  name: "TextEditor",
  props: {
    // 标题
    title: {
      type: String,
      required: true,
    },
    // 内容
    content: {
      type: String,
      default: "",
    },
    // 占位符
    placeholder: {
      type: String,
      default: "请输入内容",
    },
    // 是否编辑模式
    isEditMode: {
      type: Boolean,
      default: true,
    },
    // 文本框行数
    rows: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      localContent: "",
    };
  },
  watch: {
    content: {
      handler(newContent) {
        this.localContent = newContent;
      },
      immediate: true,
    },
  },
  methods: {
    // 内容变更处理
    handleContentChange() {
      this.$emit("content-change", this.localContent);
    },
  },
};
</script>

<style lang="scss" scoped>
.text-editor {
  width: 100%;
}

.content-text {
  line-height: 1.6;
  color: #606266;
  font-size: 14px;
  white-space: pre-wrap;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
  min-height: 200px;
}

.content-textarea {
  ::v-deep .el-textarea__inner {
    border: 1px solid #e4e7ed;
    padding: 12px;
    background: #fff;
    resize: vertical;
    font-size: 14px;
    line-height: 1.6;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}
</style>
