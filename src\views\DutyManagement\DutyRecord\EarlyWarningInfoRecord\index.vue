<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @switch-change="handleSwitch"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="预警事项" prop="currentInfoTitle">
          <el-input 
            :disabled="true" 
            type="textarea" 
            style="width: 400px;" 
            v-model="currentInfoTitle" 
            :rows="2" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select :disabled="this.styleType === 2" v-model="form.status" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in recordStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="是否关注" prop="isFocus">
          <el-switch
            :disabled="this.styleType === 2"
            v-model="form.isFocus"
            :active-value= 1
            :inactive-value= 0
          ></el-switch>
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, dutyManagementApi } from "@/api";
import { getItemList, infoEventDictionaryType } from "@/utils/dictionary";

import { conversionDateNotSecond } from "@/utils/publicMethod";


export default { 
  name: "EarlyWarningInfoRecord",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          label: "预警事项",
          prop: "matter",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "pushTime",
          label: "发布日期",
          type: "startEndPicker",
          startProp: "pushStartTime", // 开始时间字段
          endProp: "pushEndTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "status",
          label: "信息状态",
          type: "select",
          placeholder: "请输入",
          width: "120",
          options: [
            { label: "待办", value: "0" },
            { label: "已办", value: "1" },
            { label: "归档", value: "2" },
          ],
        },
      ],
      columns: [
        { prop: "matter", label: "预警事项", text: true },
        { prop: "level", label: "预警等级", text: true },
        { prop: "startDate", label: "起始时间", text: true },
        { prop: "pushTime", label: "发布日期", text: true },
        { prop: "pushDeptName", label: "发布单位", text: true },
        { prop: "status", label: "状态", text: true },
        {
          prop: 'isFocus',
          label: '是否关注',
          switch: true,
          activeValue: 1,
          inactiveValue: 0
        },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '160px',
          operationList: [
            {
              label: '查看',
              permission: 'dutyRecord:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'dutyRecord:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            // {
            //   label: '删除',
            //   permission: 'dutyRecord:delete',
            //   buttonClick: this.handleApproval,
            //   isShow: (row, $index) => {
            //     if(this.tableData[$index].inspectionResult === 0){
            //         return false
            //     }else{
            //         return true
            //     }
            //   }
            // }
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：编辑，2：查看
      currentInfoTitle: "",
      recordStatusList: [
        { label: "待办", value: "0" },
        { label: "已办", value: "1" },
        { label: "归档", value: "2" },
      ],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "编辑预警信息记录信息",

      form: {
        id: "",
        status: "",
        isFocus: 0
      },
      rules: {
        // inspectionTime: [
        //   {required: true, message: '检查时间不能为空', trigger: 'blur'}
        // ],
        // inspectionType: [
        //   {required: true, message: '检查方式不能为空', trigger: 'blur'}
        // ],
        // inspectionUnit: [
        //   {required: true, message: '被检查单位不能为空', trigger: 'blur'}
        // ],
        // inspectionJob: [
        //   {required: true, message: '被检查岗位不能为空', trigger: 'blur'}
        // ],
        // inspectionUser: [
        //   {required: true, message: '被检查人员不能为空', trigger: 'blur'}
        // ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.getTableDataList();
    // this.queryDictionaryType();
    // this.registerHandlers();
  },
  methods: {
    // registerHandlers() {
    //   this.$store.commit("generalEvent/registerEventHandler", {
    //     type: "add_top",
    //     handler: this.handleAdd,
    //   });
    // },
    // handleChangeMonth(value) {
    //   this.form.pushTime = conversionDateNotSecond(value);
    // },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.inspectionTypeList = await getItemList(infoEventDictionaryType);
        this.searchItems[2].options = this.inspectionTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      // this.fileList = [];
      // this.form.fileList = [];
      dutyManagementApi.queryWarningInfoById({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.currentInfoTitle = data?.matter;
        this.form.id = data?.id;
        this.form.status = data?.status;
        this.form.isFocus = data?.isFocus;
      });
    },

    //新增
    // handleAdd() {
    //   this.styleType = 1;
    //   this.dialogVisible = true;
    //   this.form = {};
    //   this.fileList = [];
    //   this.form.fileList = [];
    //   this.generalDialogTitle = "新增值班检查";
    // },

    //编辑
    handleEdit(row) {
      this.styleType = 1;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑接报信息记录信息";
    },

    //查看
    handleReview(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看接报信息记录信息";
    },

    // 通报
    // handleApproval() {
    //   setTimeout(() => {
    //     this.$message.success("通报成功！");
    //   }, 500);
    // },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryWarningInfoList(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.pushTime && row.pushTime.length > 0) {
        row.pushStartTime = conversionDateNotSecond(row.pushTime[0])
        row.pushEndTime = conversionDateNotSecond(row.pushTime[1])
        delete row.pushTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await dutyManagementApi.updateWarningInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },

    //关注/取消关注
    handleSwitch(row) {
      let msg
      if (row.isFocus === 1) {
        msg = "您确定要将此事件标记为重点关注？";
      } else if (row.isFocus === 0) {
        msg = "您确定要将此事件取消重点关注？";
      }
      this.$confirm( msg , {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then( () => {
        dutyManagementApi.updateWarningInfo({ id: row.id, isFocus: row.isFocus }).then((res) => {
          const {code, error} = res;
          if (code === 0) {
            this.$message.success('操作成功');
            this.handSubmitSuccess();
          } else {
            row.isFocus = row.isFocus === 1 ? 0 : 1;
            this.$message.error(error);
          }
        })
      })
      .catch(() => {
        row.isFocus = row.isFocus === 1 ? 0 : 1;
      })
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
