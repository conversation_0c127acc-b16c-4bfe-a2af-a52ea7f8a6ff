<template>
  <div class="command-configuration-container">
    <!-- 指令配置管理 - Page -->
    <portal-table
      ref="portalTableRef"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
      @add="handleAdd"
    />

    <!-- 指令配置表单弹窗 -->
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="'800px'"
      :general-dialog-title="dialogTitle"
      :show-footer="false"
      @cancel="handleCancel"
    >
      <command-configuration-form
        :is-edit-mode="isEditMode"
        :editing-row="editingRow"
        :org-options="orgOptions"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      />
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import CommandConfigurationForm from "./components/CommandConfigurationForm.vue";
import { orgApi, commandConfigurationApi } from "@/api";

export default {
  name: "CommandConfiguration",
  components: {
    PortalTable,
    GeneralDialog,
    CommandConfigurationForm,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      orgOptions: [],
      searchParams: {},
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 弹框相关数据
      dialogVisible: false,
      dialogTitle: "新增指令配置",
      isEditMode: false,
      editingRow: null,

      searchItems: [
        {
          type: "input",
          prop: "eventType",
          label: "事故类型",
          placeholder: "请输入事故类型",
        },
        {
          type: "input",
          prop: "subcategoryAccident",
          label: "事故小类",
          placeholder: "请输入事故小类",
        },
        {
          type: "input",
          prop: "responseDirective",
          label: "响应指令",
          placeholder: "请输入响应指令",
        },
        {
          type: "input",
          prop: "commandRole",
          label: "角色",
          placeholder: "请输入角色",
        },
        {
          type: "input",
          prop: "responsibilities",
          label: "任务查询",
          placeholder: "请输入任务查询",
        },
        {
          type: "select",
          prop: "organUnit",
          label: "机构/单位/名称",
          placeholder: "请选择机构/单位/名称",
          options: [{ label: "全部", value: "" }],
        },
      ],
      columns: [
        {
          prop: "reportName",
          label: "事故类型",
          text: true,
        },
        {
          prop: "responseCommand",
          label: "响应指令",
          text: true,
        },
        {
          prop: "orgNameStr",
          label: "机构/单位/名称",
          text: true,
        },
        {
          prop: "commandCenter",
          label: "指挥部/现场指挥部",
          text: true,
        },
        {
          prop: "commandRole",
          label: "角色",
          text: true,
        },
        {
          prop: "completionTime",
          label: "完成时间",
          text: true,
        },
        {
          prop: "responsibilities",
          label: "职责/任务",
          text: true,
        },
        {
          action: true,
          label: "操作",
          width: "200px",
          operationList: [
            {
              label: "编辑",
              permission: "command:edit",
              buttonClick: this.handleEdit,
              isShow: () => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "command:dealete",
              buttonClick: this.handleDelete,
              isShow: () => {
                return true;
              },
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.fetchData();
    this.loadOrgData();

    // 注册全局按钮事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: this.handleAdd,
    });

    // 注册任务下发按钮事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "task_dispatch_top",
      handler: this.handleTaskDispatch,
    });

    // 注册任务同步按钮事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "task_sync_top",
      handler: this.handleTaskSync,
    });
  },
  beforeDestroy() {
    // 清理全局事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "add_top",
      handler: null,
    });

    // 清理任务下发按钮事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "task_dispatch_top",
      handler: null,
    });

    // 清理任务同步按钮事件处理器
    this.$store.commit("generalEvent/registerEventHandler", {
      type: "task_sync_top",
      handler: null,
    });
  },
  methods: {
    async loadOrgData() {
      const params = {
        id: "",
        orgName: "",
        orderRule: 0,
        parentId: "",
      };

      try {
        const response = await orgApi.queryOrgTree(params);
        if (response.code === 0) {
          this.orgOptions = this.transformOrgTreeToOptions(response.data || []);

          const searchOrgItem = this.searchItems.find(
            (item) => item.prop === "organUnit"
          );
          if (searchOrgItem) {
            searchOrgItem.options = [
              { label: "全部", value: "" },
              ...this.orgOptions,
            ];
          }
        }
      } catch (error) {
        console.error("获取组织机构数据失败:", error);
        this.$message.error("获取组织机构数据失败");
      }
    },

    transformOrgTreeToOptions(treeData) {
      const options = [];
      const traverse = (nodes) => {
        nodes.forEach((node) => {
          options.push({
            label: node.orgName,
            value: node.id,
          });
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        });
      };
      traverse(treeData);
      return options;
    },

    async fetchData() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          ...this.searchParams,
        };

        const response = await commandConfigurationApi.queryResponseCommandPage(
          params
        );

        if (response.code === 0) {
          // 字段映射：将 API 返回的字段名映射到表格需要的字段名
          response.data.items.forEach((item) => {
            item.responseCommand =
              item.responseDirectiveName || item.responseDirectiveCustom;
          });
          this.tableData = response.data.items;
          this.pagination.total = response.data.total || 0;
        } else {
          this.$message.error(response.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取指令配置数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    handleSearch(searchParams) {
      this.pagination.currentPage = 1;
      this.searchParams = searchParams;
      this.fetchData();
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData();
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleAdd() {
      this.isEditMode = false;
      this.editingRow = null;
      this.dialogTitle = "新增指令配置";
      this.dialogVisible = true;
    },

    handleEdit(row) {
      this.isEditMode = true;
      this.editingRow = row;
      this.dialogTitle = "编辑指令配置";
      this.dialogVisible = true;
    },

    handleDelete(row) {
      this.$confirm("确定要删除这条指令配置吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response =
              await commandConfigurationApi.deleteResponseCommand({
                id: row.id,
              });

            if (response.code === 0) {
              this.$message.success("删除成功！");
              this.fetchData(); // 重新获取数据
            } else {
              this.$message.error(response.message || "删除失败");
            }
          } catch (error) {
            console.error("删除失败:", error);
            this.$message.error("删除失败，请重试");
          }
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    handleCancel() {
      this.dialogVisible = false;
      this.isEditMode = false;
      this.editingRow = null;
    },

    async handleSubmit(submitData) {
      try {
        let response;

        if (this.isEditMode) {
          // 编辑模式
          response = await commandConfigurationApi.updateResponseCommand({
            ...submitData,
            id: this.editingRow.id,
          });

          if (response.code === 0) {
            this.$message.success("编辑指令配置成功！");
            this.fetchData(); // 重新获取数据
          } else {
            this.$message.error(response.message || "编辑失败");
            return;
          }
        } else {
          // 新增模式
          response = await commandConfigurationApi.createResponseCommand(
            submitData
          );

          if (response.code === 0) {
            this.$message.success("新增指令配置成功！");
            this.fetchData(); // 重新获取数据
          } else {
            this.$message.error(response.message || "新增失败");
            return;
          }
        }

        this.dialogVisible = false;
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error("提交失败，请重试");
      }
    },

    // 任务下发处理方法
    async handleTaskDispatch() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$message.warning("请先选择要下发的指令配置");
        return;
      }

      this.$confirm("确定要下发选中的指令配置吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const infoIds = this.selectedRows.map((row) => row.id);

            const response = await commandConfigurationApi.assignTasks({
              infoIds,
            });

            if (response.code === 0) {
              this.$message.success("任务下发成功！");
              this.fetchData(); // 重新获取数据
            }
          } catch (error) {
            console.error("任务下发失败:", error);
          }
        })
        .catch(() => {
          this.$message.info("已取消下发");
        });
    },

    // 任务同步处理方法
    async handleTaskSync() {
      this.$confirm("确定要同步指令配置数据吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(async () => {
          try {
            const response = await commandConfigurationApi.syncTasks();

            if (response.code === 0) {
              this.$message.success("任务同步成功！");
              this.fetchData(); // 重新获取数据
            } else {
              this.$message.error(response.message || "任务同步失败");
            }
          } catch (error) {
            console.error("任务同步失败:", error);
            this.$message.error("任务同步失败，请重试");
          }
        })
        .catch(() => {
          this.$message.info("已取消同步");
        });
    },
  },
};
</script>

<style scoped>
.command-configuration-container {
  padding: 20px;
}
</style>
