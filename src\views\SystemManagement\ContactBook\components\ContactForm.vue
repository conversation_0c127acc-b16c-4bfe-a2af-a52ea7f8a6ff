<template>
  <div>
    <!-- 联系人表单组件 - Component -->
    <el-dialog
      :title="isEdit ? '编辑联系人' : '新增联系人'"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select
                v-model="form.gender"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option label="男" value="male"></el-option>
                <el-option label="女" value="female"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                v-model="form.mobile"
                placeholder="请输入手机号码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="department">
              <el-select
                v-model="form.department"
                placeholder="请选择部门"
                style="width: 100%"
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input
                v-model="form.position"
                placeholder="请输入职位"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="邮箱地址" prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱地址"
          ></el-input>
        </el-form-item>

        <el-form-item label="办公地址" prop="office">
          <el-input
            v-model="form.office"
            placeholder="请输入办公地址"
          ></el-input>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? "更新" : "添加" }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { formRules } from "@/utils/validate";

export default {
  name: "ContactForm",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        name: "",
        gender: "",
        phone: "",
        mobile: "",
        department: "",
        position: "",
        email: "",
        office: "",
        remark: "",
      },
      rules: formRules.contact,
      submitting: false,
      departments: [
        { label: "办公室", value: "办公室" },
        { label: "应急指挥中心", value: "应急指挥中心" },
        { label: "应急办", value: "应急办" },
        { label: "综合科", value: "综合科" },
        { label: "预警科", value: "预警科" },
        { label: "值班室", value: "值班室" },
        { label: "调度室", value: "调度室" },
      ],
    };
  },
  mounted() {
    this.registerHandlers();
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    addHandler() {
      this.dialogVisible = true;
    },
    registerHandlers() {
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'add_top',
        handler: this.addHandler
      });
    },
    initForm() {
      if (this.isEdit && this.formData) {
        this.form = { ...this.form, ...this.formData };
      } else {
        this.resetForm();
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    resetForm() {
      this.form = {
        name: "",
        gender: "",
        phone: "",
        mobile: "",
        department: "",
        position: "",
        email: "",
        office: "",
        remark: "",
      };
    },

    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const formData = { ...this.form };
            if (this.isEdit) {
              formData.id = this.formData.id;
            }
            this.$emit("submit", formData);
          } finally {
            this.submitting = false;
          }
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },

    handleClose() {
      this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.dialogVisible = false;
          this.resetForm();
        })
        .catch(() => {
          // 取消关闭
        });
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
}
</style>
