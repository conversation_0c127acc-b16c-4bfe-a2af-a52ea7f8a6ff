// 表单验证规则
export const formRules = {
  // 联系人表单验证规则
  contact: {
    name: [
      { required: true, message: "请输入姓名", trigger: "blur" },
      {
        min: 2,
        max: 20,
        message: "姓名长度在 2 到 20 个字符",
        trigger: "blur",
      },
    ],
    gender: [{ required: true, message: "请选择性别", trigger: "change" }],
    phone: [
      {
        pattern: /^(\d{3,4}-?)?\d{7,8}$/,
        message: "请输入正确的联系电话",
        trigger: "blur",
      },
    ],
    mobile: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    department: [
      { required: true, message: "请选择所属部门", trigger: "change" },
    ],
    position: [{ required: true, message: "请输入职位", trigger: "blur" }],
    email: [
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
    office: [
      { max: 100, message: "办公地址不能超过100个字符", trigger: "blur" },
    ],
    remark: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
  },
};
