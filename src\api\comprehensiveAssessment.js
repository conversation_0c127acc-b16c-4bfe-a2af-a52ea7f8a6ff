import request from "@/utils/request";
export default {
    // 细则统计
    criteriaStatistics() {
        return request({
            url: '/ds/evaluate/criteria/criteriaStatistics',
            method: 'get',
        })
    },
    // 查询评价细则列表分页
    queryCriteriaList(params) {
        return request({
            url: '/ds/evaluate/criteria/queryCriteriaList',
            method: 'post',
            data: params
        })
    },
    // 添加评价细则
    createCriteria(params) {
        return request({
            url: '/ds/evaluate/criteria/createCriteria',
            method: 'post',
            data: params
        })
    },
    // 编辑评价细则
    updateCriteria(params) {
        return request({
            url: '/ds/evaluate/criteria/updateCriteria',
            method: 'post',
            data: params
        })
    },
    // 删除评价细则
    deleteCriteria(params) {
        return request({
            url: '/ds/evaluate/criteria/deleteById',
            method: 'post',
            data: params
        })
    },
    // 查询评价方案列表分页
    querySchemeList(params) {
        return request({
            url: '/ds/evaluate/scheme/querySchemeList',
            method: 'post',
            data: params
        })
    },
    // 添加评价方案
    createScheme(params) {
        return request({
            url: '/ds/evaluate/scheme/createScheme',
            method: 'post',
            data: params
        })
    },
    // 编辑评价方案
    updateScheme(params) {
        return request({
            url: '/ds/evaluate/scheme/updateScheme',
            method: 'post',
            data: params
        })
    },

    // 查询评价方案详情
    querySchemeById(params) {
        return request({
            url: '/ds/evaluate/scheme/querySchemeById',
            method: 'post',
            data: params
        })
    },
    // 查询评价通知列表分页
    queryNoticePage(params) {
        return request({
            url: '/ds/appraisalNotice/queryNoticePage',
            method: 'post',
            data: params
        })
    },
    // 添加评价通知
    createNotice(params) {
        return request({
            url: '/ds/appraisalNotice/createNotice',
            method: 'post',
            data: params
        })
    },
    // 查询评价通知详情
    queryNoticeInfo(params) {
        return request({
            url: '/ds/appraisalNotice/queryNoticeInfo',
            method: 'post',
            data: params
        })
    },
    // 查询用户评价上报分页列表
    queryComprehensiveList(params) {
        return request({
            url: '/ds/evaluate/comprehensive/queryComprehensiveList',
            method: 'post',
            data: params
        })
    },
    // 提交用户评价上报
    submitComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/submitComprehensive',
            method: 'post',
            data: params
        })
    },
    // 保存用户评价上报草稿
    saveComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/saveComprehensive',
            method: 'post',
            data: params
        })
    },

    // 查看用户评价上报详情
    queryComprehensiveById(params) {
        return request({
            url: '/ds/evaluate/comprehensive/queryComprehensiveById',
            method: 'post',
            data: params
        })
    },

    // 删除用户评价上报
    deleteComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/deleteComprehensive',
            method: 'post',
            data: params
        })
    },
    // 用户评价上报审核通过
    auditPassComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/auditPassComprehensive',
            method: 'post',
            data: params
        })
    },
    // 用户评价上报审核驳回
    auditRejectComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/auditRejectComprehensive',
            method: 'post',
            data: params
        })
    },
    // 审核保存草稿
    auditSaveComprehensive(params) {
        return request({
            url: '/ds/evaluate/comprehensive/auditSaveComprehensive',
            method: 'post',
            data: params
        })
    },
    // 导出评价通知
    exportList(params) {
        return request({
            url: '/ds/appraisalNotice/exportList',
            method: 'post',
            data: params,
            responseType: "blob",
        })
    },
    // 下载模板 1 组织机构表 2 用户信息表 3 评价通知信息
    downloadTemplate(params) {
        return request({
            url: '/ds/download/template',
            method: 'get',
            params,
            responseType: "blob",
        })
    },
    // 导入评价通知
    importNotice(params) {
        return request({
            url: '/ds/appraisalNotice/upload',
            method: 'post',
            data: params,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },

    // 最新评价，不含草稿
    queryComprehensiveStatusList(params) {
        return request({
            url: '/ds/evaluate/comprehensive/queryComprehensiveStatusList',
            method: 'post',
            data: params,
        })
    },
    // 综合评价管理评价通知列表
    queryNoticeList(params) {
        return request({
            url: '/ds/appraisalNotice/queryNoticeList',
            method: 'post',
            data: params,
        })
    },

    // 查询分页列表(评价详情列表,已完成)
    queryComprehensiveDetailList(params) {
        return request({
            url: '/ds/evaluate/comprehensive/queryComprehensiveDetailList',
            method: 'post',
            data: params,
        })
    },

    // 导出细则
    criteriaExportList(params) {
        return request({
            url: '/ds/evaluate/criteria/exportList',
            method: 'post',
            data: params,
            responseType: "blob",
        })
    },
    // 导入细则
    criteriaUpload(params) {
        return request({
            url: '/ds/evaluate/criteria/upload',
            method: 'post',
            data: params,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
    // 导出评价方案
    schemeExportList(params) {
        return request({
            url: '/ds/evaluate/scheme/exportList',
            method: 'post',
            data: params,
            responseType: "blob",
        })
    },
    // 导入评价方案
    schemeUpload(params) {
        return request({
            url: '/ds/evaluate/scheme/upload',
            method: 'post',
            data: params,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },

    // 查询组织机构树形结构
    queryOrg(params) {
        return request({
            url: '/ds/org/queryOrg',
            method: 'post',
            data: params,
        })
    },
    // 最新评价分页列表
    queryLatestEvaluationPage(params) {
        return request({
            url: '/ds/evaluate/manage/queryLatestEvaluationPage',
            method: 'post',
            data: params,
        })
    },
    // 评价详情分页列表
    queryEvaluationDetailsPage(params) {
        return request({
            url: '/ds/evaluate/report/queryEvaluationDetailsPage',
            method: 'post',
            data: params,
        })
    },
    // 机构排名情况列表
    queryOrgRanStatusList(params) {
        return request({
            url: '/ds/evaluate/circular/queryOrgRanStatusList',
            method: 'post',
            data: params,
        })
    },
    // 值守统计评价，记录分页列表
    queryEvaluationRecordPage(params) {
        return request({
            url: '/ds/evaluate/statistics/queryEvaluationRecordPage',
            method: 'post',
            data: params,
        })
    },
    // 评价阅读
    queryComprehensiveRead(params) {
        return request({
            url: '/ds/evaluate/comprehensive/queryComprehensiveRead',
            method: 'post',
            data: params,
        })
    },
    // 优秀单位名单
    queryExcellentUnitList(params) {
        return request({
            url: '/ds/evaluate/circular/queryExcellentUnitList',
            method: 'post',
            data: params,
        })
    },
    // 存在问题单位名单
    queryProblemsUnitList(params) {
        return request({
            url: '/ds/evaluate/circular/queryProblemsUnitList',
            method: 'post',
            data: params,
        })
    },
    // 评价完成情况,图表
    queryCompletionStatus(params) {
        return request({
            url: '/ds/evaluate/manage/queryCompletionStatus',
            method: 'post',
            data: params,
        })
    },
    // 评价分数分布,图表
    queryDistributionScores(params) {
        return request({
            url: '/ds/evaluate/manage/queryDistributionScores',
            method: 'post',
            data: params,
        })
    },
    // 评价统计总数
    queryEvaluationManage(params) {
        return request({
            url: '/ds/evaluate/manage/queryEvaluationManage',
            method: 'post',
            data: params,
        })
    },

}