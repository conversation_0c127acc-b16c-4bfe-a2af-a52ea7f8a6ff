<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :dialog-width="'700px'"
    :general-dialog-title="dialogTitle"
    :show-footer="dialogType !== 'detail'"
    :set-component-name="$store.getters.componentName"
    @cancel="handleCancel"
    @confirm="handleSubmit"
  >
    <el-form
      ref="form"
      :model="form"
      class="add-form"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-form-item label="方案名称" prop="schemeName">
        <el-input v-model="form.schemeName" autocomplete="off" />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方案类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option
                v-for="item in planTypeList"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="评价周期" prop="cycle">
            <el-input v-model="form.cycle" autocomplete="off" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="选择日期" required>
        <el-col :span="11">
          <el-form-item prop="startTime">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="form.startTime"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col class="line" style="text-align: center" :span="2">-</el-col>
        <el-col :span="11">
          <el-form-item prop="endTime">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="form.endTime"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="方案描述" prop="describe">
        <el-input type="textarea" v-model="form.describe"></el-input>
      </el-form-item>
      <el-form-item label="方案细则" prop="criteriaList">
        <div class="criteria-list">
          <el-checkbox-group
            v-model="checkboxCriteriaList"
            @change="handleCheckedChange"
          >
            <el-checkbox
              v-for="(item, index) in criteriaListData"
              :key="index"
              :label="item.criteriaId"
            >
              <div class="criteria-item">
                <div>{{ item.name }}</div>
                <!-- <div class="score">{{ item.score }}分</div> -->
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="附件" prop="fileList">
        <el-upload
          class="upload-demo"
          action="#"
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :file-list="form.fileList"
        >
          <el-button
            size="small"
            :disabled="dialogType == 'detail'"
            type="primary"
            >点击上传</el-button
          >
          <div slot="tip" class="el-upload__tip">
            支持扩展名：.rar .zip .doc .docx .pdf .jpg...
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import emergencyKnowledgeBaseApi from "@/api/emergencyKnowledgeBase";
import conversionDate from "@/utils/publicMethod";
export default {
  components: {
    GeneralDialog,
  },
  data() {
    return {
      dialogType: "",
      dialogVisible: false,
      dialogTitle: "新建方案",
      criteriaListData: [],
      form: {
        schemeName: "",
        type: "",
        cycle: "",
        startTime: "",
        endTime: "",
        describe: "",
        criteriaList: [],
        fileList: [],
      },
      planTypeList: [
        {
          label: "年度评价",
          value: "1",
        },
        {
          label: "季度评价",
          value: "2",
        },
        {
          label: "月度评价",
          value: "3",
        },
        {
          label: "周度评价",
          value: "4",
        },
      ],
      rules: {
        schemeName: [
          { required: true, message: "请输入方案名称", trigger: "blur" },
        ],
        type: [{ required: true, message: "请输入方案类型", trigger: "blur" }],
        cycle: [{ required: true, message: "请输入评价周期", trigger: "blur" }],
        startTime: [
          { required: true, message: "请输入开始时间", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "请输入结束时间", trigger: "blur" },
        ],
        describe: [
          { required: true, message: "请输入方案描述", trigger: "blur" },
        ],
        criteriaList: [
          { required: true, message: "请选择评价细则", trigger: "blur" },
        ],
        fileList: [{ required: true, message: "请上传文件", trigger: "blur" }],
      },
      checkboxCriteriaList: [],
    };
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      this.form.startTime = this.form.startTime
        ? conversionDate(this.form.startTime)
        : "";
      this.form.endTime = this.form.endTime
        ? conversionDate(this.form.endTime)
        : "";
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType === "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          comprehensiveAssessmentApi.createScheme(this.form).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.$emit("refreshTableData");
            }
          });
        }
      });
    },
    editSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          comprehensiveAssessmentApi.updateScheme(this.form).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "编辑成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.$emit("refreshTableData");
            }
          });
        }
      });
    },
    // 查询评价细则列表
    queryCriteriaList() {
      comprehensiveAssessmentApi
        .queryCriteriaList({
          page: 1,
          count: 50,
        })
        .then((res) => {
          this.criteriaListData = res.data.items;
        });
    },
    // 查询评价方案详情
    querySchemeById(row) {
      comprehensiveAssessmentApi
        .querySchemeById({
          schemeId: row.schemeId,
        })
        .then((res) => {
          if (res.code === 0) {
            console.log(res, "res");
            res.data.fileList.forEach((element) => {
              element.name = element.fileName;
            });
            this.form = res.data;
            this.checkboxCriteriaList = res.data.criteriaList.map(
              (item) => item.criteriaId
            );
          }
        });
    },
    async customUpload(options) {
      const formData = new FormData();
      formData.append("file", options.file);
      try {
        const res = await emergencyKnowledgeBaseApi.uploadFile(formData);
        if (res.code === 0) {
          res.data.name = res.data.fileName;
          console.log(this.form, "uuuuuuuu");
          this.form.fileList.push(res.data);
        }
      } catch (error) {
        console.error("上传失败", error);
      }
    },
    beforeUpload(file) {
      const ext = file.name.split(".").pop().toLowerCase();
      const targetExtensions = ["rar", "zip", "doc", "docx", "pdf", "jpg"];
      const includes = targetExtensions.includes(ext);
      if (!includes) {
        this.$message.warning(
          "上传文件只能是 rar、zip、doc、docx、pdf、jpg 格式!"
        );
      }
      return includes;
    },
    handleRemove(file, fileList) {
      this.form.fileList = fileList;
    },
    handleCheckedChange(checkedList) {
      console.log(checkedList, "checkedList");

      this.form.criteriaList = checkedList.map((id) => ({ criteriaId: id }));
    },
    resetForm() {
      this.form = {
        schemeName: "",
        type: "",
        cycle: "",
        startTime: "",
        endTime: "",
        describe: "",
        criteriaList: [],
        fileList: [],
      };
      this.checkboxCriteriaList = [];
    },
  },
  mounted() {
    this.queryCriteriaList();
  },
};
</script>

<style  lang="scss" scoped>
.criteria-list {
  max-height: 250px;
  overflow-y: auto;
  background-color: #f9fafb;
  padding: 10px;
  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
    // flex-wrap: wrap;
    // gap: 5px;
  }
  .criteria-item {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    align-items: center;
    .score {
      // font-size: 15px;
    }
  }
  .add-bottom {
    color: #409eff;
    text-align: right;
    cursor: pointer;
  }
}
.upload-demo {
  .el-upload__tip {
    line-height: 20px !important;
  }
}
::v-deep .el-upload-list__item {
  transition: none;
}
</style>