<template>
  <div class="task-feedback-container">
    <!-- 任务反馈 - Page -->
    <portal-table
      ref="portalTableRef"
      :showSelection="false"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />
    <feedback-form
      ref="feedbackFormRef"
      :dialogVisible="feedbackDialogVisible"
      @close="closeDialog"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { receiveInformationApi } from "@/api";
import FeedbackForm from "../TaskInformation/components/FeedbackForm.vue";

export default {
  name: "TaskFeedback",
  components: {
    PortalTable,
    FeedbackForm
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchParams: {},
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 10,
      },

      searchItems: [
        {
          type: "input",
          prop: "infoTitle",
          label: "事件名称",
          width: 300,
          placeholder: "请输入事件名称",
        },
        {
          type: "input",
          prop: "responseDirective",
          width: 300,
          label: "响应指令",
          placeholder: "请输入响应指令",
        },

        {
          type: "select",
          prop: "status",
          width: 300,
          label: "任务状态",
          placeholder: "请选择任务状态",
          options: [
            { label: "全部", value: "" },
            { label: "已下达", value: 1 },
            { label: "已签收", value: 2 },
            { label: "已反馈", value: 3 },
            { label: "已完成", value: 4 },
          ],
        },
        {
          type: "input",
          width: 300,
          prop: "organUnit",
          label: "机构/单位/名称",
          placeholder: "请输入机构/单位/名称",
        },
      ],
      columns: [
        {
          prop: "infoTitle",
          label: "事件名称",
          text: true,
        },
        {
          prop: "eventType",
          label: "事发类型",
          text: true,
        },
        {
          prop: "responseDirective",
          label: "响应指令",
          text: true,
        },
        {
          prop: "organUnit",
          label: "机构/单位/名称",
          text: true,
        },
        {
          prop: "commandCenter",
          label: "指挥部现场指挥部",
          text: true,
        },
        {
          prop: "commandRole",
          label: "角色",
          text: true,
        },
        {
          prop: "responsibilities",
          label: "职责/任务",
          text: true,
        },
        {
          prop: "completionTime",
          label: "要求完成时间",
          text: true,
        },
        {
          prop: "taskFeedback",
          label: "任务反馈",
          text: true,
        },
        {
          prop: "status",
          label: "任务状态",
          text: true,
          formatter: (row) => {
            const statusMap = {
              1: "已下达",
              2: "已签收",
              3: "已反馈",
              4: "已完成",
            };
            return statusMap[row.status] || "";
          },
        },

        {
          action: true,
          label: "操作",
          width: "100px",
          operationList: [
            {
              label: "查看",
              permission: "taskFeedback:view",
              buttonClick: this.handleView,
              isShow: () => true,
            },
          ],
        },
      ],
      feedbackDialogVisible: false,
    };
  },
  mounted() {
    this.queryResponseTaskBackPage();
  },
  methods: {
    queryResponseTaskBackPage() {
      const parmas = {
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
        ...this.searchParams,
      };
      try {
        receiveInformationApi.queryResponseTaskBackPage(parmas).then((res) => {
          if (res.code === 0) {
            this.tableData = res.data.items;
            this.pagination.total = res.data.total;
          }
        });
      } catch (error) {}
    },
    processTableData(rawData) {
      const statusMap = {
        pending: "待执行",
        executing: "执行中",
        completed: "已完成",
        overdue: "已逾期",
      };

      const statusTypeMap = {
        pending: "info",
        executing: "warning",
        completed: "success",
        overdue: "danger",
      };

      return rawData.map((item) => ({
        ...item,
        taskStatusText: statusMap[item.taskStatus] || item.taskStatus || "",
        tagType: statusTypeMap[item.taskStatus] || "info",
      }));
    },

    handleSearch(searchParams) {
      this.pagination.currentPage = 1;
      this.searchParams = searchParams;
      this.queryResponseTaskBackPage();
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.queryResponseTaskBackPage();
    },

    handleCurrentChange(page) {
      console.log(page);

      this.pagination.currentPage = page;
      this.queryResponseTaskBackPage();
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleView(row) {
      this.feedbackDialogVisible = true;
      this.$refs.feedbackFormRef.queryResponseTaskInfoDetail(row.id, 2);
    },

    closeDialog() {
      this.feedbackDialogVisible = false;
    },
  },
};
</script>

<style scoped>
.task-feedback-container {
  padding: 20px;
}
</style>
