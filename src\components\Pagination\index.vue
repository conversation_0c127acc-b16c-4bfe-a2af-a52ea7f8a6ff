<template>
  <div class="pagination-container">
    <el-pagination
      popper-class="pagination-popper"
      class="pagination-wrapper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="mergedPageSizes"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "Pagination",
  props: {
    total: {
      type: Number,
      required: true,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSizes: {
      type: Array,
      default: () => [5, 10, 20, 50, 100],
    },
  },
  computed: {
    mergedPageSizes() {
      // 合并默认值和传入值，去重后排序
      return [
        ...new Set([
          ...this.$options.props.pageSizes.default(),
          ...this.pageSizes,
        ]),
      ].sort((a, b) => a - b);
    },
  },
  methods: {
    handleSizeChange(size) {
      this.$emit("update:pagination", {
        total: this.total,
        currentPage: this.currentPage,
        pageSize: size,
      });
      this.$emit("handle-size-change", size);
    },

    handleCurrentChange(page) {
      this.$emit("update:pagination", {
        total: this.total,
        currentPage: page,
        pageSize: this.pageSize,
      });
      this.$emit("handle-current-change", page);
    },
  },
};
</script>

<style scoped lang="scss">
.pagination-container {
  ::v-deep .el-pagination {
    .el-pagination__sizes{
      margin-right: 25px;
    }
  }
}
</style>
