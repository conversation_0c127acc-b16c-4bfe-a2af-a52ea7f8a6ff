/**
 * 动态路由管理器 - 核心模块
 * 负责将后端菜单数据转换为Vue Router路由配置
 */

// 路由配置
const ROUTE_CONFIG = {
  // 是否启用约定式路由（新增路由推荐使用）
  enableConventionRouting: true,
  // 是否启用组件映射表兜底（兼容历史路由）
  enableComponentMapFallback: true,
};

// 组件映射表 - 只保留无法使用约定式路由的特殊路径
const COMPONENT_MAP = {
  // 系统管理模块 - 特殊路径（路径与目录结构不匹配）
  "/system/OrganizationManagement": () =>
    import("@/views/SystemManagement/OrganizationManagement/index.vue"),
  "/system/userManagement": () =>
    import("@/views/SystemManagement/UserManagement/index.vue"),
  "/system/menuManage": () =>
    import("@/views/SystemManagement/MenuManagement/index.vue"),
  "/system/roleManagement": () =>
    import("@/views/SystemManagement/RoleManagement/index.vue"),
  "/system/contact": () =>
    import("@/views/SystemManagement/ContactBook/index.vue"),
  "/system/positionManagement": () =>
    import("@/views/SystemManagement/PositionManagement/index.vue"),
  "/system/logManagement": () =>
    import("@/views/SystemManagement/LogManagement/index.vue"),
  "/system/ipLoginManagement": () =>
    import("@/views/SystemManagement/IPLoginManagement/index.vue"),
  "/system/groupingManagement": () =>
    import("@/views/SystemManagement/GroupingManagement/index.vue"),
};

// 默认图标映射
const DEFAULT_ICONS = {
  0: "el-icon-s-home", // 一级菜单
  1: "el-icon-folder", // 二级菜单
  2: "el-icon-document", // 三级菜单
  3: "el-icon-setting", // 按钮权限
};

class RouteManager {
  constructor(router) {
    this.router = router;
    this.dynamicRoutes = new Set(); // 记录动态添加的路由
    this.Layout = () => import("@/layout/index.vue");
  }

  /**
   * 根据菜单数据生成路由配置
   * @param {Array} menuData - 后端返回的菜单数据
   * @returns {Array} 路由配置数组
   */
  generateRoutes(menuData) {
    if (!Array.isArray(menuData)) {
      return [];
    }

    const routes = [];

    menuData.forEach((menu) => {
      if (menu.menuType === 0) {
        // 一级菜单
        const route = this.createRouteConfig(menu);
        if (route) {
          routes.push(route);
        }
      }
    });

    return routes;
  }

  /**
   * 创建单个路由配置
   * @param {Object} menu - 菜单项数据
   * @returns {Object|null} 路由配置对象
   */
  createRouteConfig(menu) {
    try {
      const route = {
        path: menu.path,
        name: this.generateRouteName(menu.path),
        component: this.Layout,
        meta: {
          title: menu.name,
          icon: menu.icon || DEFAULT_ICONS[menu.menuType],
          menuId: menu.id,
          menuType: menu.menuType,
          requiresAuth: true,
        },
      };

      // 处理子路由
      if (menu.children && menu.children.length > 0) {
        route.children = this.generateChildRoutes(menu.children);
      }

      return route;
    } catch (error) {
      console.error("创建路由配置失败:", error, menu);
      return null;
    }
  }

  /**
   * 生成子路由配置
   * @param {Array} children - 子菜单数组
   * @returns {Array} 子路由配置数组
   */
  generateChildRoutes(children) {
    const childRoutes = [];

    children.forEach((child) => {
      // 跳过按钮权限（menuType: 3）
      if (child.menuType === 3) return;

      const component = this.getComponent(child.path);
      if (!component) {
        return;
      }

      const childRoute = {
        path: child.path,
        name: this.generateRouteName(child.path),
        component: component,
        meta: {
          title: child.name,
          icon: child.icon || DEFAULT_ICONS[child.menuType],
          menuId: child.id,
          menuType: child.menuType,
          requiresAuth: true,
        },
      };

      // 递归处理嵌套子路由
      if (child.children && child.children.length > 0) {
        childRoute.children = this.generateChildRoutes(child.children);
      }

      childRoutes.push(childRoute);
    });

    return childRoutes;
  }

  /**
   * 根据路径获取对应组件
   * @param {string} path - 路由路径
   * @returns {Function|null} 组件加载函数
   */
  getComponent(path) {
    // 1. 优先使用映射表（精确匹配，避免路径错误）
    if (ROUTE_CONFIG.enableComponentMapFallback) {
      const component = COMPONENT_MAP[path];
      if (component) {
        return component;
      }
    }

    // 2. 兜底使用约定式路由（新路由推荐）
    if (ROUTE_CONFIG.enableConventionRouting) {
      const conventionComponent = this.generateConventionComponent(path);
      if (conventionComponent) {
        return conventionComponent;
      }
    }

    console.warn(`路由组件未找到: ${path}`);
    return null;
  }

  /**
   * 约定式路由组件生成
   *
   * 路径映射规则：
   * /system/userManagement -> @/views/System/UserManagement/index.vue
   * /textMessage/template -> @/views/TextMessage/Template/index.vue
   * /dutyManagement/dutyArrange -> @/views/DutyManagement/DutyArrange/index.vue
   * @param {string} path - 路由路径
   * @returns {Function|null} 组件加载函数
   */
  generateConventionComponent(path) {
    try {
      // 将路径转换为组件路径
      const componentPath = path
        .split("/")
        .filter(Boolean)
        .map((segment) => {
          // 处理驼峰命名：userManagement -> UserManagement
          return segment.charAt(0).toUpperCase() + segment.slice(1);
        })
        .join("/");

      // 约定式路由映射完成

      return () => import(`@/views/${componentPath}/index.vue`);
    } catch (error) {
      console.warn(`约定式路由组件加载失败: ${path}`, error);
      return null;
    }
  }

  /**
   * 生成路由名称
   * @param {string} path - 路由路径
   * @returns {string} 路由名称
   */
  generateRouteName(path) {
    return path
      .split("/")
      .filter(Boolean)
      .map((segment, index) =>
        index === 0
          ? segment
          : segment.charAt(0).toUpperCase() + segment.slice(1)
      )
      .join("");
  }

  /**
   * 动态添加路由到路由器
   * @param {Array} routes - 路由配置数组
   */
  addDynamicRoutes(routes) {
    // 先移除现有的404路由（如果存在）
    this.remove404Route();

    routes.forEach((route) => {
      try {
        // 使用 addRoute 方法动态添加路由
        const removeRoute = this.router.addRoute(route);
        this.dynamicRoutes.add({
          name: route.name,
          remove: removeRoute,
        });
      } catch (error) {
        console.error(`添加动态路由失败: ${route.path}`, error);
      }
    });

    // 动态路由添加完成后，重新添加404路由
    this.add404Route();
  }

  /**
   * 添加404路由
   */
  add404Route() {
    try {
      const NotFound = () => import("@/views/404.vue");
      const remove404 = this.router.addRoute({
        path: "*",
        name: "NotFound",
        component: NotFound,
        meta: {
          title: "页面未找到",
          requiresAuth: false,
        },
      });

      this.dynamicRoutes.add({
        name: "NotFound",
        remove: remove404,
      });
    } catch (error) {
      console.error("添加404路由失败:", error);
    }
  }

  /**
   * 移除404路由
   */
  remove404Route() {
    try {
      // 查找并移除现有的404路由
      const notFoundRoute = Array.from(this.dynamicRoutes).find(
        (route) => route.name === "NotFound"
      );
      if (notFoundRoute && typeof notFoundRoute.remove === "function") {
        notFoundRoute.remove();
        this.dynamicRoutes.delete(notFoundRoute);
      }
    } catch (error) {
      console.error("移除404路由失败:", error);
    }
  }

  /**
   * 清除所有动态路由
   */
  clearDynamicRoutes() {
    this.dynamicRoutes.forEach(({ remove }) => {
      if (typeof remove === "function") {
        remove();
      }
    });
    this.dynamicRoutes.clear();
  }

  /**
   * 重新加载路由
   * @param {Array} menuData - 新的菜单数据
   */
  reloadRoutes(menuData) {
    // 清除现有动态路由
    this.clearDynamicRoutes();

    // 生成新路由
    const routes = this.generateRoutes(menuData);

    // 添加新路由
    this.addDynamicRoutes(routes);

    return routes;
  }

  /**
   * 获取当前所有动态路由信息
   * @returns {Array} 动态路由信息数组
   */
  getDynamicRoutesInfo() {
    return Array.from(this.dynamicRoutes).map(({ name }) => name);
  }
}

export default RouteManager;
