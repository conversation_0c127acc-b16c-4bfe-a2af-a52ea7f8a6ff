<template>
  <div class="tree-panel">
    <!-- 树形面板组件 - Component -->

    <!-- 树形面板标题 -->
    <div class="tree-header" v-if="title">
      <span class="tree-title">{{ title }}</span>
    </div>

    <!-- 搜索框 -->
    <div class="tree-search" v-if="showSearch">
      <el-input
        v-model="filterText"
        placeholder="请输入关键字搜索"
        size="small"
        prefix-icon="el-icon-search"
        clearable
        @input="handleFilter"
      />
    </div>

    <!-- 树形内容区域 -->
    <div class="tree-content">
      <el-tree
        ref="tree"
        :data="filteredTreeData"
        :props="treeProps"
        :default-expand-all="expandAll"
        :highlight-current="true"
        :filter-node-method="filterNode"
        node-key="id"
        @node-click="handleNodeClick"
        class="custom-tree"
      >
        <span class="tree-node" slot-scope="{ node, data }">
          <el-tooltip
            :content="node.label"
            placement="top"
            :disabled="!isTextOverflow(node.label)"
          >
            <span class="node-label">{{ node.label }}</span>
          </el-tooltip>
          <span class="node-actions" v-if="showActions">
            <el-button
              type="text"
              size="mini"
              icon="el-icon-edit"
              @click.stop="handleEdit(data)"
            ></el-button>
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              @click.stop="handleDelete(data)"
            ></el-button>
          </span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: "TreePanel",
  props: {
    title: {
      type: String,
      default: "",
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    treeProps: {
      type: Object,
      default: () => ({
        children: "children",
        label: "label",
      }),
    },
    expandAll: {
      type: Boolean,
      default: true,
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
    showActions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filterText: "",
      filteredTreeData: [],
    };
  },
  watch: {
    treeData: {
      handler(newVal) {
        this.filteredTreeData = [...newVal];
        // 数据更新后重新处理叶子节点图标
        this.$nextTick(() => {
          setTimeout(() => {
            this.hideLeafExpandIcons();
          }, 100);
        });
      },
      immediate: true,
      deep: true,
    },
    filteredTreeData: {
      handler() {
        // 过滤数据更新后重新处理叶子节点图标
        this.$nextTick(() => {
          setTimeout(() => {
            this.hideLeafExpandIcons();
          }, 100);
        });
      },
      deep: true,
    },
  },
  mounted() {
    // 延迟执行确保DOM渲染完成
    this.$nextTick(() => {
      setTimeout(() => {
        this.hideLeafExpandIcons();
      }, 100);
    });
  },
  methods: {
    handleNodeClick(data) {
      this.$emit("node-click", data);
    },
    handleEdit(data) {
      this.$emit("edit", data);
    },
    handleDelete(data) {
      this.$emit("delete", data);
    },
    handleFilter(val) {
      if (val) {
        this.filteredTreeData = this.filterTreeData(this.treeData, val);
      } else {
        this.filteredTreeData = [...this.treeData];
      }
    },
    filterTreeData(data, keyword) {
      const result = [];
      const labelField = this.treeProps.label || "label";
      data.forEach((item) => {
        const itemLabel = item[labelField] || "";
        if (itemLabel.toLowerCase().includes(keyword.toLowerCase())) {
          result.push({ ...item });
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.filterTreeData(item.children, keyword);
          if (filteredChildren.length > 0) {
            result.push({
              ...item,
              children: filteredChildren,
            });
          }
        }
      });
      return result;
    },
    filterNode(value, data) {
      if (!value) return true;
      const labelField = this.treeProps.label || "label";
      const itemLabel = data[labelField] || "";
      return itemLabel.toLowerCase().includes(value.toLowerCase());
    },
    // 文字溢出检测 - 基于实际DOM宽度
    isTextOverflow(text) {
      if (!text) return false;

      // 创建临时元素测量文本宽度
      const tempElement = document.createElement("span");
      tempElement.style.visibility = "hidden";
      tempElement.style.position = "absolute";
      tempElement.style.fontSize = "14px";
      tempElement.style.fontFamily = "inherit";
      tempElement.textContent = text;
      document.body.appendChild(tempElement);

      const textWidth = tempElement.offsetWidth;
      document.body.removeChild(tempElement);

      const treeWidth = this.$el ? this.$el.offsetWidth : 200;
      const availableWidth = treeWidth - 60; // 预留60px给图标、缩进、内边距

      return textWidth > availableWidth;
    },
    // 隐藏叶子节点展开图标 - 精确处理
    hideLeafExpandIcons() {
      this.$nextTick(() => {
        const treeEl = this.$refs.tree;
        if (treeEl && treeEl.$el) {
          // 延迟执行确保DOM完全渲染
          setTimeout(() => {
            this.hideIconsByDOM();
          }, 50);
        }
      });
    },
    // 通过DOM结构精确隐藏叶子节点图标
    hideIconsByDOM() {
      const treeEl = this.$refs.tree;
      if (treeEl && treeEl.$el) {
        // 查找所有节点
        const allNodes = treeEl.$el.querySelectorAll(".el-tree-node");
        allNodes.forEach((node) => {
          // 检查是否有子节点容器
          const childrenContainer = node.querySelector(
            ".el-tree-node__children"
          );

          // 判断是否为叶子节点：没有子节点容器或子节点容器为空
          const isLeafNode =
            !childrenContainer || childrenContainer.children.length === 0;

          if (isLeafNode) {
            // 为叶子节点添加is-leaf类（如果Element UI没有自动添加）
            node.classList.add("is-leaf");

            // 隐藏展开图标
            const expandIcon = node.querySelector(
              ".el-tree-node__content > .el-tree-node__expand-icon"
            );
            if (expandIcon) {
              expandIcon.style.display = "none";
              expandIcon.style.visibility = "hidden";
            }
          } else {
            // 确保非叶子节点显示展开图标
            node.classList.remove("is-leaf");
            const expandIcon = node.querySelector(
              ".el-tree-node__content > .el-tree-node__expand-icon"
            );
            if (expandIcon) {
              expandIcon.style.display = "";
              expandIcon.style.visibility = "";
            }
          }
        });
      }
    },
  },
};
</script>

<style scoped>
.tree-panel {
  height: 100%;
  background: var(--tree-panel-bg);
  border: var(--tree-panel-border);
  border-radius: var(--tree-panel-radius);
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 12px 16px;
  background: var(--nav-header-bg);
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 15px;
}

.tree-search {
  padding: 10px 12px;
  background: var(--search-bg);
  border-bottom: none;
}

.tree-content {
  padding: 6px;
  flex: 1;
  overflow: auto;
  background: var(--tree-panel-bg);
  width: 100%;
  max-width: 100%;
}

.custom-tree {
  background: transparent;
  overflow-x: hidden;
  max-width: 100%;
}

.custom-tree >>> .el-tree-node__content {
  height: 32px;
  overflow: hidden;
  max-width: 100%;
}

.custom-tree >>> .el-tree-node__expand-icon {
  color: var(--icon-primary);
}

/* 只隐藏真正的叶子节点展开图标 */
.custom-tree
  >>> .el-tree-node.is-leaf
  > .el-tree-node__content
  > .el-tree-node__expand-icon {
  display: none !important;
}

.custom-tree >>> .el-tree-node__label {
  font-size: 14px;
  color: var(--text-secondary);
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.node-label {
  flex: 1;
  padding: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-tree >>> .el-tree > .el-tree-node > .el-tree-node__content {
  padding-left: 8px !important;
}

.custom-tree
  >>> .el-tree
  > .el-tree-node
  .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__content {
  padding-left: 24px !important;
}

.custom-tree
  >>> .el-tree
  > .el-tree-node
  .el-tree-node__children
  .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__content {
  padding-left: 40px !important;
}

.node-actions {
  display: none;
}

.tree-node:hover .node-actions {
  display: flex;
}

.tree-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tree-content::-webkit-scrollbar-track {
  background: rgba(245, 247, 250, 0.5);
  border-radius: 5px;
}

.tree-content::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.6);
  border-radius: 5px;
  transition: background 0.3s ease;
  border: 1px solid transparent;
  background-clip: content-box;
}

.tree-content::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.8);
}
</style>
