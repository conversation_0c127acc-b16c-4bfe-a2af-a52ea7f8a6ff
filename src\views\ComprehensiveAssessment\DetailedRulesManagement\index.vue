<template>
  <div class="evaluation-criteria">
    <el-row style="margin: 0">
      <el-col :span="24">
        <cardComponent :pieceList="pieceList"></cardComponent>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover">
          <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-form-item label="细则名称">
              <el-input
                v-model="form.name"
                placeholder="请输入细则名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="评分方式">
              <el-select
                v-model="form.scoreManner"
                placeholder="请选择评分方式"
              >
                <el-option
                  v-for="item in scoreMannerList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所有类别">
              <el-input v-model="form.type" placeholder="请输入类别"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="onSubmit"
                >高级筛选</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-refresh"
                @click="resetForm"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <PortalTable
            :tableHeight="450"
            :columns="columns"
            :table-data="tableData"
            :showSelection="false"
            row-key="id"
            :pagination="pagination"
            :showAddButton="false"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-card>
      </el-col>
    </el-row>
    <dialogComponent
      ref="dialogComponent"
      @refreshTableData="refreshTableData"
    ></dialogComponent>
  </div>
</template>

<script>
import cardComponent from "./components/cardComponent.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import dialogComponent from "@/views/ComprehensiveAssessment/DetailedRulesManagement/components/dialogComponent.vue";

export default {
  name: "EvaluationCriteria",
  components: {
    cardComponent,
    PortalTable,
    dialogComponent,
  },
  data() {
    return {
      pieceList: [
        {
          title: "总评价细则",
          text: "24",
          status: "12",
        },
        {
          title: "自动评分细则",
          text: "18",
          status: "8",
        },
        {
          title: "人工评分细则",
          text: "6",
          status: "-5",
        },
        {
          title: "待审核细则",
          text: "2",
          status: "0",
        },
      ],
      form: {
        name: "",
        scoreManner: "",
        type: "",
      },
      columns: [
        { text: true, prop: "name", label: "细则名称", minWidth: 280 },
        { text: true, prop: "criteria", label: "评分标准", minWidth: 380 },
        { text: true, prop: "type", label: "分类", width: 120 },
        // { text: true, prop: "object", label: "对象", width: 180 },
        { text: true, prop: "score", label: "分值", width: 100 },
        { text: true, prop: "scoreManner", label: "评分方式", width: 120 },
        { text: true, prop: "config", label: "数据源/配置", width: 200 },
        {
          action: true,
          label: "操作",
          width: 300,
          operationList: [
            {
              label: "编辑",
              permission: "evaluationCriteria:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "evaluationCriteria:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "详情",
              permission: "evaluationCriteria:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          id: "001",
          name: "贯彻落实《北京市应急值守工作管理规范》，履行本部门应急值守工作的主体责任",
          criteria:
            "未制定或修订本部门应急值守相关制度规范的，扣2分；未明确专门处室承担应急值守工作职责的，扣1分（2分）；未完善本部门应急值守相关内部工作流程的，扣1分（2分）",
          category: "管理体制",
          object: "市委办局",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
        {
          id: "002",
          name: "建立健全本部门值班值守交接班工作",
          criteria:
            "健全本地区应急管理组织体系，加强对应急值守工作的组织领导和安排部署",
          category: "管理体制",
          object: "市委办局",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
        {
          id: "003",
          name: "认真制定本部门月度应急值守计划安排并组织落实",
          criteria:
            "未制定本地区法定节假日和重大活动期间值班值守工作方案的，扣2分；未按照上级通知要求，严格执行三级24小时在岗带班值班等制度的，每发生一次扣1分；未建立相关工作复盘总结机制的，扣1分。因值班值守工作，被国务院总值班室或应急管理部通报批评的，此项不得分",
          category: "值班值守",
          object: "市委办局",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
        {
          id: "006",
          name: "加强安全形势风险分析研判",
          category: "风险监测",
          object: "市委办局",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
          criteria: "",
        },
        {
          id: "010",
          name: "是否首报",
          criteria: "首报为1分，非首报为0分",
          category: "系统评分",
          object: "市委办局、区应急局",
          score: "1分",
          method: "系统评分",
          source: "API接口(应急信息报送系统)",
        },
        {
          id: "011",
          name: "报送时长",
          criteria:
            "30分钟内报送得3分，超过30分钟且在1小时内报送得2分，超过1小时且在2小时内报送得1分，超过2小时完成得0分",
          category: "系统评分",
          object: "市委办局、区应急局",
          score: "3分",
          method: "系统评分",
          source: "API接口(应急信息报送系统)",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
      scoreMannerList: [
        {
          label: "自动评分",
          value: "自评",
        },
        {
          label: "人工评分",
          value: "人工",
        },
        {
          label: "混合评分",
          value: "混合",
        },
      ],
    };
  },
  methods: {
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryCriteriaList();
    },
    resetForm() {
      this.form = {
        name: "",
        scoreManner: "",
        criteria: "",
      };
      this.queryCriteriaList();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.queryCriteriaList();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.queryCriteriaList();
    },
    handleEdit(row) {
      console.log(row);
      this.$refs.dialogComponent.dialogVisible = true;
      this.$refs.dialogComponent.dialogType = "edit";
      this.$refs.dialogComponent.dialogTitle = "编辑评价细则";
      this.$refs.dialogComponent.form = {
        name: row.name,
        criteria: row.criteria,
        deptId: row.deptId,
        score: row.score,
        scoreManner: row.scoreManner,
        type: row.type,
        criteriaId: row.criteriaId,
      };
    },
    handleDelete(row) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          comprehensiveAssessmentApi
            .deleteCriteria({ criteriaId: row.criteriaId })
            .then(() => {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.refreshTableData();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleDetail(row) {
      this.$refs.dialogComponent.dialogVisible = true;
      this.$refs.dialogComponent.dialogType = "detail";
      this.$refs.dialogComponent.dialogTitle = "评价细则详情";
      this.$refs.dialogComponent.form = {
        name: row.name,
        criteria: row.criteria,
        deptId: row.deptId,
        score: row.score,
        scoreManner: row.scoreManner,
        type: row.type,
        criteriaId: row.criteriaId,
      };
    },

    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "import_top",
        handler: this.importTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.exportTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.downloadTopHandler,
      });
    },
    addTopHandler() {
      console.log("添加");
      this.$refs.dialogComponent.resetForm();
      this.$refs.dialogComponent.dialogVisible = true;
      this.$refs.dialogComponent.dialogTitle = "添加评价细则";
      this.$refs.dialogComponent.dialogType = "add";
    },
    async importTopHandler() {
      try {
        // 创建文件输入元素
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".xlsx,.xls";

        input.onchange = async (e) => {
          const file = e.target.files[0];
          if (!file) return;

          // 创建FormData对象
          const formData = new FormData();
          formData.append("file", file);

          // 添加其他参数（如果需要）
          // formData.append("noticeTitle", this.searchData.noticeTitle);
          // formData.append("sendStatus", this.searchData.sendStatus);

          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: "文件上传中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          try {
            // 调用API
            const res = await comprehensiveAssessmentApi.criteriaUpload(
              formData
            );

            if (res.code === 0) {
              this.$message.success("导入成功");
              this.refreshTableData(); // 刷新表格数据
            } else {
              this.$message.error(res.message || "导入失败");
            }
          } catch (error) {
            console.error("导入错误:", error);
            this.$message.error("导入失败: " + (error.message || "未知错误"));
          } finally {
            loading.close();
          }
        };

        // 触发文件选择
        input.click();
      } catch (error) {
        console.error("导入异常:", error);
        this.$message.error("导入异常: " + error.message);
      }
    },
    async exportTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.criteriaExportList({
          ...this.form,
        });

        if (!res.data) {
          throw new Error("API返回数据为空");
        }

        // 添加响应类型检查;
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价细则.xlsx";

        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败: " + error.message);
      }
    },
    async downloadTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.downloadTemplate({
          type: 4,
        });
        if (!res.data) {
          throw new Error("API返回数据为空");
        }
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价细则模板.xlsx";
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败: " + error.message);
      }
    },
    // 细则统计
    async criteriaStatistics() {
      const res = await comprehensiveAssessmentApi.criteriaStatistics();
      console.log(res, "细则统计");
    },
    // 查询评价细则列表分页
    async queryCriteriaList() {
      const params = {
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
        ...this.form,
      };
      const res = await comprehensiveAssessmentApi.queryCriteriaList(params);
      if (res.code === 0) {
        this.tableData = res.data.items;
        this.pagination.total = res.data.total;
      }
      console.log(res, "查询评价细则列表分页");
    },
    // 刷新表格数据
    refreshTableData() {
      this.pagination.currentPage = 1;
      this.form = {
        name: "",
        scoreManner: "",
        criteria: "",
      };
      this.queryCriteriaList();
    },
  },
  mounted() {
    this.registerHandlers();
    this.criteriaStatistics();
    this.queryCriteriaList();
  },
};
</script>

<style lang="scss" scoped>
.evaluation-criteria {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>