<template>
  <div class="user-index-container">
    <!-- 用户职务管理 - Page -->

    <!-- 表格区域 -->
    <portal-table
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <!-- 编辑弹框 -->
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="'800px'"
      :general-dialog-title="dialogTitle"
      :show-footer="false"
      @cancel="handleDialogCancel"
    >
      <PositionForm
        :visible="dialogVisible"
        :edit-data="currentEditData"
        @success="handleFormSuccess"
        @cancel="handleDialogCancel"
      />
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import PositionForm from "./components/PositionForm.vue";
import { positionApi } from "@/api/index.js";

export default {
  name: "PositionManagement",
  components: { PortalTable, GeneralDialog, PositionForm },
  data() {
    return {
      columns: [
        { text: true, prop: "itemName", label: "职务名称" },
        { text: true, prop: "itemDesc", label: "职务描述" },
        { text: true, prop: "createTime", label: "更新日期" },
        { text: true, prop: "itemSort", label: "位次" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "编辑",
              permission: "user:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              type: "delete",
              permission: "user:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],

      tableData: [],
      searchKeyword: "",
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      searchItems: [
        {
          prop: "itemName",
          label: "用户职务名称",
          type: "input",
          placeholder: "请输入用户职务名称",
        },
      ],
      // 弹框相关
      dialogVisible: false,
      dialogTitle: "",
      currentEditData: {},
    };
  },

  mounted() {
    this.registerHandlers();
    this.loadPositionList();
  },
  methods: {
    // 注册事件处理器
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },

    // 新增职务
    handleAdd() {
      this.dialogTitle = "新增职务";
      this.currentEditData = null;
      this.dialogVisible = true;
    },

    // 加载职务列表
    async loadPositionList() {
      try {
        const params = {
          count: this.pagination.pageSize,
          itemName: this.searchKeyword || "",
          page: this.pagination.currentPage,
          systemDictionaryId: "202506231532",
        };

        const response = await positionApi.queryPositions(params);

        if (response && response.data) {
          this.tableData = response.data.items || [];
          this.pagination.total = response.data.total || 0;
        }
      } catch (error) {
        this.$message.error("加载职务列表失败: " + error.message);
      }
    },

    // 搜索
    handleSearch(searchParams) {
      this.searchKeyword = searchParams.itemName || "";
      this.pagination.currentPage = 1;
      this.loadPositionList();
    },

    // 重置搜索
    handleReset() {
      this.searchKeyword = "";
      this.pagination.currentPage = 1;
      this.loadPositionList();
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.loadPositionList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.loadPositionList();
    },

    // 编辑
    handleEdit(row) {
      this.currentEditData = { ...row };
      this.dialogTitle = "编辑职务";
      this.dialogVisible = true;
    },

    // 删除
    async handleDelete(row) {
      try {
        const confirmResult = await this.$confirm(
          `确定要删除职务"${row.itemName}"吗？此操作不可撤销。`,
          "删除确认",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        if (confirmResult) {
          const deleteData = {
            id: row.id,
          };

          const response = await positionApi.deletePositions(deleteData);

          if (response && response.code === 0) {
            this.$message.success("删除成功");
            this.loadPositionList();
          } else {
            this.$message.error(response?.message || "删除失败");
          }
        }
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("删除失败，请重试");
        }
      }
    },

    // 弹框取消
    handleDialogCancel() {
      this.dialogVisible = false;
      this.currentEditData = {};
    },

    // 表单提交成功
    handleFormSuccess() {
      this.dialogVisible = false;
      this.currentEditData = {};
      // 重新加载数据
      this.loadPositionList();
    },
  },
};
</script>

<style lang="scss" scoped>
.user-index-container {
  padding: 20px;
}
</style>
