import CryptoJS from "crypto-js";

let keyStr = "1qaz2wsx3edc5tgb";  // 密钥
let  ivStr = "2wsx3edc4rfv5tgb";   // iv偏移量
/**
 *  crypto-js 加密工具
 */
class CryptoUtils {
  constructor() {
    this.secretKey = process.env.VUE_APP_CRYPTO_SECRET;

    // 清除可能存在的旧加密数据
    this.clearOldEncryptedData();
  }

  /**
   * 清除旧的加密数据
   */
  clearOldEncryptedData() {
    const keys = ["token", "refreshToken", "userInfo", "permissions"];
    keys.forEach((key) => {
      const value = localStorage.getItem(key);
      if (value && value.startsWith("U2FsdGVkX1")) {
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * AES加密
   * @param {string} text - 要加密的文本
   * @returns {string} 加密后的文本
   */
  encrypt(text) {
    if (!text) return text;

    try {
      const str = typeof text === "string" ? text : JSON.stringify(text);
      return CryptoJS.AES.encrypt(str, this.secretKey).toString();
    } catch (error) {
      return text;
    }
  }

  /**
   * AES解密
   * @param {string} encryptedText - 加密的文本
   * @returns {string} 解密后的文本
   */
  decrypt(encryptedText) {
    if (!encryptedText) return encryptedText;

    try {
      const decryptedBytes = CryptoJS.AES.decrypt(
        encryptedText,
        this.secretKey
      );
      const decrypted = decryptedBytes.toString(CryptoJS.enc.Utf8);

      if (!decrypted) {
        return encryptedText;
      }

      return decrypted;
    } catch (error) {
      return encryptedText;
    }
  }

  /**
   * 加密对象
   * @param {object} obj - 要加密的对象
   * @returns {string} 加密后的字符串
   */
  encryptObject(obj) {
    if (!obj) return obj;
    return this.encrypt(JSON.stringify(obj));
  }

  /**
   * 解密对象
   * @param {string} encryptedStr - 加密的字符串
   * @returns {object} 解密后的对象
   */
  decryptObject(encryptedStr) {
    if (!encryptedStr) return null;

    try {
      const decrypted = this.decrypt(encryptedStr);
      return JSON.parse(decrypted);
    } catch (error) {
      return null;
    }
  }

  // AES加密
  encryptData = (word) => {
    let key = CryptoJS.enc.Utf8.parse(keyStr);
    let iv = CryptoJS.enc.Utf8.parse(ivStr);
    let srcs = CryptoJS.enc.Utf8.parse(word);

    let encrypted = CryptoJS.AES.encrypt(srcs, key, {
      iv:iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  }

  // AES解密
  decryptData = (word) => {
    var key = CryptoJS.enc.Utf8.parse(keyStr);
    let iv = CryptoJS.enc.Utf8.parse(ivStr);
    var decrypt = CryptoJS.AES.decrypt(word, key, {
      iv:iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
  }
}

export default new CryptoUtils();
