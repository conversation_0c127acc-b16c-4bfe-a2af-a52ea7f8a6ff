<template>
  <div class="dialog-container" v-if="dialogVisible">
    <div class="dialog-title">
      {{ title }}
    </div>
    <div class="divider-line"></div>
    <div class="dialog-content">
      <el-form
        :model="form"
        max-width="1000px"
        :rules="rules"
        ref="dialogForm"
        label-width="150px"
      >
        <el-row>
          <el-col :span="10">
            <el-form-item label="应急手册名称" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="应急手册分类" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择应急手册分类"
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="颁布单位" prop="issuingAuthority">
              <el-input v-model="form.issuingAuthority" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="颁布日期" prop="issuingDate">
              <el-date-picker
                v-model="form.issuingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择颁布日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="施行日期" prop="effectiveDate">
              <el-date-picker
                v-model="form.effectiveDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择施行日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="关联事故" prop="name">
              <el-input
                v-model="form.eventType"
                placeholder="请输入内容关联事故"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="是否失效" prop="isStatus">
              <el-select v-model="form.isStatus" placeholder="请选择是否失效">
                <el-option
                  v-for="item in isStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="摘要" prop="summary">
              <el-input type="textarea" v-model="form.summary" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 30px">
          <el-col :span="24">
            <el-form-item label="附件" prop="fileList">
              <div class="attachment">
                <div class="attachment-content" v-if="dialogType !== 'add'">
                  <div
                    class="attachment-item"
                    v-for="item in fileList"
                    :key="item.id"
                  >
                    <div class="attachment-item-img">
                      <img
                        class="image"
                        src="@/assets/images/attachment.png"
                        alt=""
                      />
                      <div class="attachment-item-img-text">
                        {{ item.fileName }}
                      </div>
                    </div>
                    <div class="attachment-item-text">
                      <div
                        class="click-title"
                        v-if="dialogType === 'edit'"
                        @click="attachmentClick(item, 'delete')"
                      >
                        删除
                      </div>
                      <div
                        class="click-title"
                        @click="attachmentClick(item, 'view')"
                      >
                        预览
                      </div>
                      <div
                        class="click-title"
                        @click="attachmentClick(item, 'download')"
                      >
                        下载
                      </div>
                    </div>
                  </div>
                </div>
                <el-upload
                  v-if="dialogType !== 'view'"
                  ref="uploadRef"
                  :auto-upload="false"
                  class="upload-demo"
                  drag
                  action="#"
                  :on-change="handleFileChange"
                  :before-upload="beforeUpload"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    单个附件大小不能超过20M，支持上传office文本
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 100px">
          <el-col :offset="9" :span="6">
            <el-button @click="returnClick">返回</el-button>
            <el-button
              type="primary"
              @click="submitForm"
              v-if="dialogType !== 'view'"
              >保存</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-dialog
      title="预览"
      :visible.sync="showIframe"
      v-if="showIframe"
      width="80%"
      height="600px"
      :before-close="handleClose"
    >
      <iframe
        :src="fileUrl"
        frameborder="0"
        width="100%"
        height="600px"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import { emergencyKnowledgeBaseApi } from "@/api";
import { auth } from "@/utils";
import conversionDate from "@/utils/publicMethod";
import { getKKFilePreviewUrl } from "@/utils/publicMethod";
export default {
  data() {
    return {
      dialogType: "",
      dialogVisible: false,
      form: {
        name: "",
        category: "",
        issuingAuthority: "",
        issuingDate: "",
        effectiveDate: "",
        eventType: "",
        isStatus: 0,
        summary: "",
        fileList: [],
        type: 3,
      },
      rules: {
        name: [
          { required: true, message: "请输入应急手册名称", trigger: "blur" },
        ],
        category: [
          { required: true, message: "请选择应急手册分类", trigger: "blur" },
        ],
        issuingAuthority: [
          { required: true, message: "请输入颁布单位", trigger: "blur" },
        ],
        issuingDate: [
          { required: true, message: "请选择颁布日期", trigger: "blur" },
        ],
        effectiveDate: [
          { required: true, message: "请选择施行日期", trigger: "blur" },
        ],
        eventType: [
          { required: true, message: "请输入关联事故", trigger: "blur" },
        ],
        isStatus: [
          { required: true, message: "请选择是否失效", trigger: "blur" },
        ],
        summary: [{ required: false, message: "请输入摘要", trigger: "blur" }],
        fileList: [{ required: true, message: "请上传附件" }],
      },
      title: "",
      fileList: [],
      uploadUrl: "",
      categoryOptions: [],
      isStatusOptions: [
        {
          label: "现在适用",
          value: 0,
        },
        {
          label: "失效",
          value: 1,
        },
      ],
      showIframe: false,
      fileUrl: "",
      uploadFileList: [],
    };
  },
  methods: {
    queryKnowledgeBaseInfo(params) {
      emergencyKnowledgeBaseApi
        .queryKnowledgeBaseInfo(params)
        .then((res) => {
          if (res.code === 0) {
            console.log(res);
            this.form = res.data;
            this.fileList = res.data.fileList;
            // this.form.isStatus = this.form.isStatus === 0 ? "现在适用" : "失效";
          }
        })
        .catch((err) => {
          // console.log(err);
        });
    },
    attachmentClick(item, type) {
      if (type === "delete") {
        this.attachmentClickDelete(item);
      }
      if (type === "view") {
        this.attachmentClickView(item);
      }
      if (type === "download") {
        this.attachmentClickDownload(item);
      }
    },
    attachmentClickView(item) {
      const file = {
        url: auth.getFileBaseUrl() + item.fileUrl,
      };
      this.handlePreview(file);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(file.url);
      }
      // window.open(fileUrl, "_blank");
      this.fileUrl = fileUrl;
      this.showIframe = true;
    },
    handleClose() {
      this.showIframe = false;
    },
    attachmentClickDownload(item) {
      this.toDownloadFile(item);
    },
    attachmentClickDelete(item) {
      console.log(item);

      try {
        emergencyKnowledgeBaseApi
          .deleteKnowledgeBaseFile({
            id: [item.id],
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.fileList = this.fileList.filter(
                (element) => element.id !== item.id
              );
            }
          });
      } catch (error) {}
    },
    submitForm() {
      if (this.dialogType === "add") {
        this.addKnowledgeBase();
      }
      if (this.dialogType === "edit") {
        this.updateKnowledgeBase();
      }
    },
    addKnowledgeBase() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          console.log(this.form);
          this.form.type = 3;
          this.form.fileList = [...this.uploadFileList];
          this.form.effectiveDate = this.form.effectiveDate
            ? conversionDate(this.form.effectiveDate)
            : "";
          this.form.issuingDate = this.form.issuingDate
            ? conversionDate(this.form.issuingDate)
            : "";
          emergencyKnowledgeBaseApi
            .createKnowledgeBase(this.form)
            .then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "添加成功",
                  type: "success",
                });
                this.uploadFileList = [];
                this.fileList = [];
                this.dialogVisible = false;
                this.$emit("getStandardSpecificationList");
                this.$emit("emergencyManualShow");
              }
            })
            .catch((err) => {
              // console.log(err);
            });
        }
      });
    },
    updateKnowledgeBase() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          console.log(this.form);
          this.form.fileList = [...this.uploadFileList];
          this.form.effectiveDate = this.form.effectiveDate
            ? conversionDate(this.form.effectiveDate)
            : "";
          this.form.issuingDate = this.form.issuingDate
            ? conversionDate(this.form.issuingDate)
            : "";
          emergencyKnowledgeBaseApi
            .updateKnowledgeBase(this.form)
            .then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "更新成功",
                  type: "success",
                });
                this.uploadFileList = [];
                this.fileList = [];
                this.dialogVisible = false;
                this.$emit("getStandardSpecificationList");
                this.$emit("emergencyManualShow");
              }
            })
            .catch((err) => {
              // console.log(err);
              this.$message.error("更新失败");
            });
        }
      });
    },
    // 下载文件
    toDownloadFile(row) {
      console.log(row);
      const baseUrl = getKKFilePreviewUrl();
      console.log(baseUrl, "reeeeeeeeeeee");
      console.log(row.fileUrl);
      const loading = this.$loading({
        lock: true,
        text: "正在下载...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        if (!row.fileUrl) {
          this.$message.warning("文件链接无效");
          return;
        }
        fetch(auth.getFileBaseUrl() + row.fileUrl)
          .then((response) => response.blob())
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = row.assessRulesName || "file";
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url); // 释放 URL 对象

            this.$message.success("文件下载已开始");
          })
          .catch((error) => {
            console.error("文件下载失败:", error);
            this.$message.error("文件下载失败，请稍后重试");
          });
      } catch (error) {
        console.error("文件下载失败:", error);
        this.$message.error("文件下载失败，请稍后重试");
      } finally {
        loading.close();
      }
    },
    async handleFileChange(file, fileList) {
      console.log(file, fileList);
      const formData = new FormData();
      formData.append("file", file.raw);
      try {
        const res = await emergencyKnowledgeBaseApi.uploadFile(formData);
        if (res.code === 0) {
          console.log(res);
          this.uploadFileList.push(res.data);
          this.form.fileList = [...this.uploadFileList];
          this.$refs.dialogForm.validateField("fileList");
        }
      } catch (error) {}
    },
    beforeUpload(file) {
      console.log(file, "before");
      return false;
      // const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      // const isLt2M = file.size / 1024 / 1024 < 20;
      // if (!isExcel) {
      //   this.$message.error("上传文件只能是Excel文件!");
      // }
      // if (!isLt2M) {
      //   this.$message.error("上传文件大小不能超过20MB!");
      // }
      // return isExcel && isLt2M;
    },
    resetForm() {
      Object.assign(this.$data, this.$options.data());
    },
    returnClick() {
      this.dialogVisible = false;
      this.$emit("emergencyManualShow");
    },
  },
};
</script>;
<style lang="scss" scoped>
.dialog-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #fff;
  padding: 30px;
  .dialog-title {
    font-size: 20px;
    font-weight: bold;
  }
  .divider-line {
    height: 1px;
    background-color: #e0e0e0;
    margin: 20px 0;
  }
  .dialog-content {
    margin: 20px 0 0 40px;
  }
  .attachment {
    display: flex;
    align-items: center;
    margin-top: 10px;
    height: 150px;

    .attachment-title {
      text-align: right;
      margin-right: 20px;
    }
    .attachment-content {
      display: flex;
      max-width: 500px;
      overflow-x: auto;
      white-space: nowrap;
      margin-right: 20px;
      .attachment-item {
        text-align: center;
        margin-right: 10px;
        .image {
          width: 100px;
          height: 100px;
        }
        .attachment-item-text {
          display: flex;
          justify-content: center;
          gap: 10px;
          font-size: 17px;
          color: #0f36e5;
          .click-title {
            cursor: pointer;
          }
        }
      }
    }
  }
  ::v-deep .el-input {
    width: 400px;
  }
  ::v-deep .el-textarea {
    width: 400px;
  }
}
</style>
