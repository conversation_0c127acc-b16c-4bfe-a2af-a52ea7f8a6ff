<template>
  <div class="emergency-manual-container">
    <div class="emergency-manual-main" v-show="emergencyManualVisible">
      <portal-table
        style="width: 100%"
        :tableHeight="600"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="id"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @handle-selection-change="handleSelectionChange"
      />
    </div>
    <public-dialog
      ref="dialogRef"
      @emergencyManualShow="emergencyManualVisible = true"
      @getStandardSpecificationList="getStandardSpecificationList"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import PublicDialog from "./components/publicDialog.vue";
import { emergencyKnowledgeBaseApi } from "@/api";
import conversionDate from "@/utils/publicMethod";
export default {
  name: "EmergencyManual",
  components: {
    PortalTable,
    PublicDialog,
  },
  data() {
    return {
      emergencyManualVisible: true,
      searchItems: [
        {
          prop: "category",
          label: "应急手册类别",
          type: "select",
          placeholder: "请输入应急手册类别",
          defaultValue: "全部",
          options: [],
        },
        {
          prop: "name",
          label: "智能检索",
          type: "input",
          placeholder: "请输入关键字搜索",
        },
        {
          prop: "implementationTime",
          label: "施行时间",
          type: "startEndPicker",
          placeholder: "输选择统计时间",
          //   width: "200px",
          //   format: "YYYY-MM-DD",
        },
        {
          prop: "eventType",
          label: "事件类型",
          type: "input",
          placeholder: "请输入关键字搜索",
        },
      ],
      tableData: [],
      columns: [
        {
          text: true,
          prop: "name",
          label: "应急手册名称",
        },
        {
          text: true,
          prop: "category",
          label: "类别",
        },
        {
          text: true,
          prop: "issuingAuthority",
          label: "颁布机构",
        },
        { text: true, prop: "issuingDate", label: "颁布日期" },
        { text: true, prop: "effectiveDate", label: "施行日期" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "查看",
              permission: "emergencyManual:view",
              buttonClick: this.handleView,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "编辑",
              permission: "emergencyManual:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "emergencyManual:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 100,
      },
      form: {
        category: "",
        name: "",
        effectiveEndDate: "",
        effectiveStartDate: "",
        eventType: "",
        type: 3,
      },
      categoryOptions: [],
      selectedRows: [],
    };
  },
  methods: {
    // 获取应急手册数据
    async getStandardSpecificationList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...this.form,
      };
      let res;
      try {
        res = await emergencyKnowledgeBaseApi.queryKnowledgeBasePage(params);
      } catch (error) {}
      this.tableData = res?.data.items || [];
      this.pagination.total = res?.data.total || 0;
    },
    // 搜索
    handleSearch(data) {
      console.log(data, "data", this.form);
      const {
        category = "",
        name = "",
        implementationTime = "",
        eventType = "",
      } = data;
      this.form = {
        ...this.form,
        category: category === "全部" ? "" : category,
        name,
        eventType: eventType === "全部" ? "" : eventType,
        effectiveEndDate: implementationTime[1]
          ? conversionDate(implementationTime[1])
          : "",
        effectiveStartDate: implementationTime[0]
          ? conversionDate(implementationTime[0])
          : "",
      };
      this.pagination.currentPage = 1;
      this.pagination.pageSize = 10;
      this.getStandardSpecificationList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.getStandardSpecificationList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getStandardSpecificationList();
    },

    addHandler() {
      this.emergencyManualVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "add";
      this.$refs.dialogRef.title = "新增应急手册";
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
    },
    handleSelectionChange(selection) {
      console.log(selection, "selection");
      this.selectedRows = [];
      selection.forEach((item) => {
        this.selectedRows.push(item.id);
      });
    },
    deleteHandler() {
      if (this.selectedRows.length === 0) {
        this.$message({
          message: "请选择要删除选项",
          type: "warning",
        });
        return;
      }
      this.deleteKnowledgeItems(this.selectedRows);
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "deleteBatch_top",
        handler: this.deleteHandler,
      });
    },
    //取消
    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        groupExplain: "",
        groupName: "",
      };
    },
    //提交
    handleSubmit() {
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType == "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .createGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.pagination.currentPage = 1;
              this.pagination.pageSize = 10;
              this.$refs.addForm.resetFields();
              this.getStandardSpecificationList();
            })
            .catch(() => {
              // this.$message.error("新增失败");
            });
        }
      });
    },
    // 列表编辑提交
    editSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .updateGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.$refs.addForm.resetFields();
              this.getStandardSpecificationList();
            })
            .catch(() => {
              // this.$message.error("编辑失败");
            });
        }
      });
    },
    // 列表删除按钮
    handleDelete(row) {
      this.deleteKnowledgeItems([row.id]);
    },
    deleteKnowledgeItems(ids) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          emergencyKnowledgeBaseApi
            .deleteKnowledgeBase({ id: ids })
            .then(() => {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getStandardSpecificationList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 列表编辑按钮
    handleEdit(row) {
      this.emergencyManualVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "edit";
      this.$refs.dialogRef.title = "编辑应急手册";
      this.$refs.dialogRef.queryKnowledgeBaseInfo({ id: row.id });
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
    },
    // 列表查看按钮
    handleView(row) {
      this.emergencyManualVisible = false;
      this.$refs.dialogRef.resetForm();
      this.$refs.dialogRef.dialogVisible = true;
      this.$refs.dialogRef.dialogType = "view";
      this.$refs.dialogRef.title = "查看应急手册";
      this.$refs.dialogRef.queryKnowledgeBaseInfo({ id: row.id });
      this.$refs.dialogRef.categoryOptions = this.categoryOptions;
      this.$refs.dialogRef.rules = {};
    },
    // 字典查询
    async queryItemListByDictionaryId(id) {
      let res;
      try {
        res = await emergencyKnowledgeBaseApi.queryItemListByDictionaryId({
          systemDictionaryId: id,
        });
      } catch (error) {}
      const arr = [];

      res?.data.items.forEach((item) => {
        arr.push({
          label: item.itemName,
          value: item.id,
        });
      });
      console.log(arr, "aaaaaaaaaaaaa");
      this.categoryOptions = arr;
      this.searchItems = this.searchItems.map((item) => {
        return {
          ...item,
          options: item.prop === "category" ? arr : [],
        };
      });
    },
  },
  mounted() {
    this.registerHandlers();
    this.getStandardSpecificationList();
  },
  created() {
    this.queryItemListByDictionaryId("202507011607");
  },
};
</script>

<style lang="scss" scoped>
.emergency-manual-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px;
  box-sizing: border-box;
  .emergency-manual-main {
    flex: 1;
    // display: flex;
    // gap: 8px;
    // padding: 8px;
    // overflow: hidden;
  }
}

.search-wrapper {
  margin-bottom: 8px;

  /* 搜索表单样式优化 */
  ::v-deep .search-form {
    .search-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-row,
      .search-buttons-row {
        margin: 0 !important;
      }
    }
  }
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

  /* 按钮样式优化 */
  ::v-deep .el-button--text {
    &:nth-child(2) {
      color: #b93742 !important;
      &:hover {
        color: #f70a0a !important;
      }
    }
    &:nth-child(1):hover,
    &:nth-child(3):hover {
      color: #0621f1 !important;
    }
  }
}
</style>