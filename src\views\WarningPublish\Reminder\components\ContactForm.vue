<template>
  <general-dialog :showFooter="isOperation != 3" :dialog-visible="dialogVisible" general-dialog-title="知识库"
    dialog-width="1100px" @cancel="handleClose">
    <div style="padding: 30px;">
      <el-tabs type="border-card" v-model="tableTabsValue">
        <el-tab-pane label="事故案例">
          <portal-table style="padding: 20px" :tableHeight="300" :showAddButton="false" :showSelection="false"
            :columns="columnsAccident" :pagination="pagination" :search-items="searchItemsAccident"
            :table-data="tableDataAccident" row-key="name" @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        </el-tab-pane>
        <el-tab-pane label="应急预案">
           <portal-table style="padding: 20px" :tableHeight="300" :showAddButton="false" :showSelection="false"
            :columns="columnsForecast" :pagination="pagination" :search-items="searchItemsForecast"
            :table-data="tableDataForecast" row-key="name" @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        </el-tab-pane>
        <el-tab-pane label="法律法规">
          <portal-table style="padding: 20px" :tableHeight="300" :showAddButton="false" :showSelection="false"
            :columns="columnsLaw" :pagination="pagination" :search-items="searchItemsLaw"
            :table-data="tableDataLaw" row-key="name" @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        </el-tab-pane>
        <el-tab-pane label="标准规范">
          <portal-table style="padding: 20px" :tableHeight="300" :showAddButton="false" :showSelection="false"
            :columns="columnsRules" :pagination="pagination" :search-items="searchItemsRules"
            :table-data="tableDataRules" row-key="name" @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        </el-tab-pane>
        <el-tab-pane label="专家">
          <portal-table style="padding: 20px" :tableHeight="300" :showAddButton="false" :showSelection="false"
            :columns="columnsExpert" :pagination="pagination" :search-items="searchItemsExpert"
            :table-data="tableDataExpert" row-key="name" @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        </el-tab-pane>
      </el-tabs>
    </div>

  </general-dialog>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
  name: "ContactForm",
  components: {
    PortalTable,
    GeneralDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isOperation: {
      type: Number,
      default: false,
    },
  },
  data() {
    return {
      tableTabsValue:'',
      searchParams:null,
      //事故案例
      searchItemsAccident: [
        {
          prop: "aa",
          label: "案例名称",
          type: "input",
          placeholder: "输入案例名称",
          width: "150",
        },
        {
          prop: "bb",
          label: "事件等级",
          type: "select",
          placeholder: "请选择事件等级",
          width: "150",
          options: [
            { label: "是", value: "1" },
            { label: "否", value: "0" },
          ],
        },

        {
          prop: "ff",
          label: "事件类型",
          type: "cascader",
          placeholder: "输选择事件类型",
          width: "150px",
          options: [
            {
              value: 'zhinan',
              label: '指南',
              children: [
                {
                  value: 'shejiyuanze',
                  label: '设计原则',
                }
              ]
            }
          ],
          props: {
            checkStrictly: true
          }
        },
      ],
      columnsAccident: [
        { prop: "aa", label: "案例名称", text: true },
        { prop: "bb", label: "事故类型", text: true },
        { prop: "cc", label: "事件等级", text: true },
        { prop: "dd", label: "事发日期", text: true, width: "160px" }
      ],
      tableDataAccident: [
        {
          aa: '123'
        }
      ],
      //应急预案
      searchItemsForecast: [
        {
          prop: "aa",
          label: "应急知识标题",
          type: "input",
          placeholder: "输入应急知识标题",
          width: "250",
        }
      ],
      columnsForecast: [
        { prop: "aa", label: "应急知识标题", text: true },
        { prop: "bb", label: "更新日期", text: true },
      ],
      tableDataForecast: [
        {
          aa: '123'
        }
      ],
      //法律法规
      searchItemsLaw: [
        {
          prop: "aa",
          label: "法律法规名称",
          type: "input",
          placeholder: "输入法律法规名称",
          width: "250",
        }
      ],
      columnsLaw: [
        { prop: "aa", label: "法律法规名称", text: true },
        { prop: "bb", label: "法律法规类别", text: true },
      ],
      tableDataLaw: [
        {
          aa: '123'
        }
      ],
      //标准规范
      searchItemsRules: [
        {
          prop: "aa",
          label: "名称",
          type: "input",
          placeholder: "输入名称",
          width: "250",
        }
      ],
      columnsRules: [
        { prop: "aa", label: "名称", text: true },
      ],
      tableDataRules: [
        {
          aa: '123'
        }
      ],
      //专家 
      searchItemsExpert: [
        {
          prop: "aa",
          label: "姓名",
          type: "input",
          placeholder: "输入姓名",
          width: "250",
        }
      ],
      columnsExpert: [
        { prop: "aa", label: "专家类型", text: true },
        { prop: "bb", label: "姓名", text: true },
        { prop: "cc", label: "电话", text: true },
      ],
      tableDataExpert: [
        {
          aa: '123'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 5,
        total: 0,
      },
    };
  },
  mounted() {
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.resetForm();
        })
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
    tableTabsValue: {
      handler(val) {
        this.pagination.currentPage = 1;
        this.pagination.pageSize = 5;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getTableDataList(){

    },
    // 搜索
    handleSearch(row) {
      this.pagination.pageSize = 1;
      this.searchParams = row;
      this.getTableDataList()
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },
    resetForm() {
      this.$nextTick(() => {

      })
    },
    handleClose() {
      this.dialogVisible = false;
    },


  },
};
</script>

<style scoped>

</style>
