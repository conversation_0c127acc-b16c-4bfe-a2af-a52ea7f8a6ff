<template>
  <!-- 信息退报 -->
  <div class="info-back">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="退报内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请输入退报内容"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "InfoBack",
  data() {
    return {
      form: {
        content: "",
      },
      rules: {
        content: [
          { required: true, message: "请输入退报内容", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$message({
            message: "信息退报成功",
            type: "success",
          });
          this.$refs.addForm.resetFields();
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      content: "",
    };
  },
};
</script>

<style scoped lang="scss"></style>
