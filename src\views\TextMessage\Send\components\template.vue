<!-- 选择模版 -->
<template>
    <general-dialog 
        :dialog-visible="dialogVisible" 
        :general-dialog-title="'选择人员分组'" 
        dialog-width="800px"
        @cancel="handleClose" 
        @confirm="handleSubmit">
        <div class="add-form">
            <SearchForm :form-config="searchConfig" :tips="searchTips" :show-tips="false" @search="handleSearch"
                @reset="handleReset" ref="searchForm" />
            <ul class="template-book">
                <li class="template-book_li" v-for="item in templateList" :key="item.id">
                    <div class="checkbox">
                        <el-checkbox v-model="item.selected" @change="onCheckboxChange(item)"></el-checkbox>
                    </div>
                    <div class="content">
                        <p class="title">{{ item.templateTheme }}</p>
                        <p class="mt-10">{{ item.templateContent }}</p>
                    </div>
                </li>
            </ul>
        </div>
    </general-dialog>
</template>

<script>
import { textMessageTemplateApi } from "@/api";
import GeneralDialog from '@/components/GeneralDialog.vue'
import SearchForm from "@/components/SearchForm.vue";
export default {
    components: {
        SearchForm,
        GeneralDialog
    },
    name: '',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        formData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "templateContent",
                    label: "模板内容",
                    type: "input",
                    placeholder: "输入模板内容",
                    width: "200px",
                },
                {
                    prop: "templateType",
                    label: "模板类型",
                    type: "select",
                    placeholder: "模板类型",
                    width: "200px",
                    options: [
                        { label: "中国劳务/应急部", value: "1" },
                    ],
                },

            ],

            searchTips: [

            ],
            form: {
                templateContent: '',
                templateType: ''
            },
            templateTypeOptions: [],
            templateList: [
            ],
            searchParams:{
                templateContent:'',
                templateType:''
            }
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
    },
    methods: {
        async initData(){
            let requestParams = await textMessageTemplateApi.getTextMessageTemplateListAll({...this.searchParams});
            this.templateList = requestParams.data
        },
        // 搜索相关方法
        async handleSearch(formData) {
            // 保持当前的组织过滤状态
            this.searchParams = { ...formData };
            this.initData()
            this.$message.success("搜索完成");
        },
        handleReset() {
            this.resetForm()
            this.initData()
        },


        resetForm() {
            this.searchParams = {
                templateContent: '',
                templateType: ''
            }
           
        },
        // 添加
        handleSubmit() {
            this.dialogVisible = false;
            let data = {}
            this.templateList.forEach((row) => {
                if (row.selected) {
                    data = row
                }

            })
            this.$emit('modelInput', data)
        },
        // 关闭
        handleClose() {
            this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.dialogVisible = false;
                    this.resetForm();
                })
                .catch(() => {
                    // 取消关闭
                });
        },
        // Checkbox
        onCheckboxChange(item) {
            this.templateList.forEach((row) => {
                row.selected = false
                if (row.templateId == item.templateId) {
                    item.selected = true
                }
            })
        }
    },
    mounted() {
        this.initData()
    }
}
</script>

<style scoped lang="scss">
.template-book {
    max-height: 400px;
    overflow-y: auto;
    padding: 0px;

    .template-book_li {
        display: flex;

        .checkbox {
            padding: 20px;
        }

        .content {
            box-sizing: content-box;
            padding-right: 20px;

            .title {
                font-weight: 700;
                font-size: 16px;
            }
        }
    }
}
</style>