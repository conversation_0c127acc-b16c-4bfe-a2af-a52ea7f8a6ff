<template>
  <div class="ip-login-management-container">
    <div class="ip-login-management-main">
      <portal-table
        style="width: 100%"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="name"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />
    </div>
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="用户编号" prop="userId">
          <el-autocomplete
            class="inline-input"
            v-model="form.userId"
            :fetch-suggestions="querySearch"
            placeholder="请输入用户名"
            :trigger-on-focus="false"
            @blur="handleBlur"
            @select="handleSelect"
            style="width: 440px"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input v-model="form.ipAddress" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="编号" prop="id">
          <el-input v-model="form.id" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi } from "@/api";
export default {
  name: "IPLoginManagement",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    const IP_REGEX =
      /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)$/;

    // 2. 使用完整逻辑分支（避免简写歧义）
    const validateIP = (rule, value, callback) => {
      if (value === "" || value === null || value === undefined) {
        callback(new Error("IP地址不能为空"));
      } else if (!IP_REGEX.test(value.trim())) {
        // 添加 trim() 防止空格干扰
        callback(new Error("请输入有效的IPv4地址（如：***********）"));
      } else {
        callback(); // 必须执行 callback 表示验证通过
      }
    };

    return {
      searchItems: [
        {
          prop: "name",
          label: "",
          type: "input",
          placeholder: "请输入",
          width: "200px",
        },
      ],
      tableData: [
        {
          affiliatedInstitutions: "机构1",
          accountName: "账号1",
          userName: "用户1",
          ipAddress: "***********",
          explain: "说明1",
          createTime: "2023-06-24 10:00:00",
          founder: "张三",
        },
      ],
      columns: [
        {
          prop: "orgName",
          label: "所属机构",
          text: true,
        },
        {
          prop: "loginName",
          label: "账号名称",
          text: true,
        },
        {
          prop: "userName",
          label: "用户姓名",
          text: true,
        },
        { prop: "ipAddress", label: "IP地址", text: true },
        { prop: "explain", label: "说明", text: true },
        {
          prop: "createTime",
          label: "创建时间",
          text: true,
        },
        { prop: "createId", label: "创建人", text: true },
      ],

      tableKey: 0,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "IP登录新增",
      form: {
        userId: "",
        id: "",
        ipAddress: "",
      },
      rules: {
        userId: [
          { required: true, message: "请输入用户编号", trigger: "change" },
        ],
        id: [{ required: false, message: "请输入编号", trigger: "blur" }],
        ipAddress: [
          { required: true, message: "请输入IP地址", trigger: "blur" },
          { validator: validateIP, trigger: ["blur", "change"] },
        ],
      },
      restaurants: [],
    };
  },
  methods: {
    // 查询IP登录管理数据
    async getGroupList() {
      const params = {
        count: this.pagination.pageSize,
        keyword: this.searchKeyword,
        page: this.pagination.current,
      };
      let res;
      try {
        res = await systemManagementApi.queryIpLoginPage(params);
      } catch (error) {}

      this.tableData = res?.data.items || [];
      this.pagination.total = res?.data.total;
    },

    // 搜索
    handleSearch(data) {
      if (data.name == "") {
        this.$message.warning("请输入IP地址");
        return;
      }
      this.pagination.pageSize = 10;
      this.pagination.current = 1;
      this.searchKeyword = data.name;
      this.getGroupList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.getGroupList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.current = page;
      this.getGroupList();
    },

    addHandler() {
      this.dialogVisible = true;
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
    },
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.addIpLoginHandle(this.form);
          this.pagination.current = 1;
          this.pagination.pageSize = 10;
          this.searchKeyword = "";
          this.getGroupList();
          this.dialogVisible = false;
          this.$refs.addForm.resetFields();
        } else {
          return false;
        }
      });
    },

    async addIpLoginHandle(params) {
      try {
        await systemManagementApi.addIpLogin(params);
      } catch (error) {}
    },
    async querySearch(queryString, cb) {
      this.restaurants = [];
      const res = await this.getUserListByNameHandle(queryString);
      res.forEach((item) => {
        this.restaurants.push({
          value: item.id,
        });
      });
      cb(this.restaurants);
    },
    handleSelect(item) {
      this.$set(this.form, "userId", item.value);
    },
    handleBlur() {
      const isValid = this.restaurants.some(
        (opt) => opt.value === this.form.userId
      );
      if (!isValid) {
        this.form.userId = "";
      }
    },
    // 用户名称模糊查询
    async getUserListByNameHandle(queryString) {
      const params = {
        userName: queryString,
      };
      try {
        let res = await systemManagementApi.getUserListByName(params);
        return res.data;
      } catch (error) {}
    },
  },
  mounted() {
    this.getGroupList();
    this.registerHandlers();
  },
};
</script>

<style lang="scss" scoped>
.ip-login-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px;
  box-sizing: border-box;
  .ip-login-management-main {
    flex: 1;
    display: flex;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
  }
}

.search-wrapper {
  margin-bottom: 8px;

  /* 搜索表单样式优化 */
  ::v-deep .search-form {
    .search-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-row,
      .search-buttons-row {
        margin: 0 !important;
      }
      .search-row {
        .search-item {
          label {
            display: none !important;
          }
        }
      }
    }
  }
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

  /* 按钮样式优化 */
  >>> .el-button--text {
    &:nth-child(2) {
      color: #b93742 !important;
      &:hover {
        color: #f70a0a !important;
      }
    }
    &:nth-child(1):hover,
    &:nth-child(3):hover {
      color: #0621f1 !important;
    }
  }
}
</style>
