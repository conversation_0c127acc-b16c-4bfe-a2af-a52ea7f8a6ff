<!-- 会议纪要---HistoryMeeting -->
<template>
  <div class="user-index-container" v-if="showReportInfo">
    <div class="meeting-report-top">
      <span class="meeting-title-name"> {{ meetingName }} </span>
      <el-button
        class="back-button"
        type="primary"
        size="small"
        plain
        @click="handleBackClick()"
      >
        返回 
      </el-button>
    </div>

    <div class="meeting-report-top2">
      <el-radio-group class="report-status-radio" v-model="reportStatus" @input="handleRadioClick">
        <el-radio-button :label="2">全部</el-radio-button>
        <el-radio-button :label="1">已报备</el-radio-button>
        <el-radio-button :label="0">未报备</el-radio-button>
      </el-radio-group>
      <el-button
        class="export-button"
        type="primary"
        size="small"
        @click="handleExportClick()"
      >
        导出 
      </el-button>
    </div>
    
    <portal-table
      style="padding: 20px"
      :tableHeight="tableHeight"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />
  </div>
</template>

<script>

import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { meetingManagementApi } from "@/api";

export default {
  name: "ReportMeeting",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      showReportInfo: false,
      meetingId: "",
      tableHeight: 600,
      meetingName: "森林防火调度会",
      reportStatus: 2,
      searchItems: [],
      columns: [
        { prop: "orgName", label: "单位名称", text: true },
        { prop: "reportStatusName", label: "报备状态", tag: true },
        { prop: "meetingTerminal", label: "参会终端", text: true },
        { prop: "supportPersonnel", label: "保障人员", text: true },
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  mounted() {
    const gaping = 50 + 40 + 12 + 20 + 45 + 140;
    this.tableHeight = window.innerHeight - gaping;
  },
  methods: {
    showReportInfoList(meetingId, meetingName) {
      this.meetingName = meetingName;
      this.showReportInfo = true;
      this.meetingId = meetingId;
      this.getTableDataList();
    },

    handleBackClick() {
      // 返回列表
      this.showReportInfo = false;
      this.$emit("buttonBackClick");
    },

    handleExportClick() {
      // 导出列表
    },

    handleRadioClick(value) {
      if (value === 1) {
        this.searchParams = {reportStatus : 1};
      } else if (value === 0) {
        this.searchParams = {reportStatus : 0};
      } else {
        this.searchParams = null;
      }
      this.getTableDataList();
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        type: 1, //类型 1:会议纪要 2:会议材料
        count: this.pagination.pageSize,
        page: this.pagination.current,
        meetingId: this.meetingId,
        ...this.searchParams,
      };
      const res = await meetingManagementApi.queryReportMeetingPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      console.log("查询列表", data);
      this.tableData = data.items || [];
      this.tableData.forEach((item) => {
        // item.tagType = item.reportStatus === "1" ? "success" : "danger";
        if (item.reportStatus == 1) {
            item.tagType = "success";
            item.reportStatusName = "已报备";
          } else {
            item.tagType = "danger";
            item.reportStatusName = "未报备";
          }
      });
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-report-top{
  top: 10px;
  position: relative;
  height: 40px;
  .meeting-title-name {
    display: inline-block;
    // width: 100%;
    margin-left: 20px;
    line-height: 40px;
    font-size: 20px;
    font-weight: 700;
    text-align: left;  
  }
  .back-button{
    position: absolute;
    right: 20px;
    top: 20px;
    transform: translateY(-50%);
  }
}

.meeting-report-top2 {
  position: relative;
  margin-left: 20px;
  margin-top: 20px;
  .export-button {
    position: absolute;
    right: 20px;
    top: 20px;
    transform: translateY(-50%);
  }
}

</style>
