// 会议管理相关api
import request from "@/utils/request";
export default class meetingManagement {

  /**
   * --------------------------会议准备管理-------------------------------
   */

  // 会议通知---新增会议通知
  static createMeetingNotice(data) {
    return request({
        url: "/ds/meeting/notice/createMeetingNotice",
        method: "post",
        data ,
    });
  }

  // 会议通知---删除会议通知
  static deleteMeetingNotice(data) {
    return request({
        url: "/ds/meeting/notice/deleteMeetingNotice",
        method: "post",
        data ,
    });
  }

  // 会议通知---修改会议通知
  static updateMeetingNotice(data) {
    return request({
        url: "/ds/meeting/notice/updateMeetingNotice",
        method: "post",
        data ,
    });
  }

  // 会议通知---查询会议通知详情
  static queryMeetingNoticeDetail(data) {
    return request({
        url: "/ds/meeting/notice/queryMeetingNoticeDetail",
        method: "post",
        data ,
    });
  }

  // 会议通知---查询会议通知分页列表
  static queryMeetingNoticePage(data) {
    return request({
        url: "/ds/meeting/notice/queryMeetingNoticePage",
        method: "post",
        data ,
    });
  }

  // 会议通知---会议通知审核
  static auditMeeting(data) {
    return request({
        url: "/ds/meeting/notice/auditMeeting",
        method: "post",
        data ,
    });
  }

  // 会议通知---查询报送信息列表
  static queryReportMeetingPage(data) {
    return request({
        url: "/ds/meeting/notice/queryReportMeetingPage",
        method: "post",
        data ,
    });
  }



  // 会议通知---查询近期会议详情
  static queryMeetingSignDetail(data) {
    return request({
        url: "/ds/meeting/sign/queryMeetingSignDetail",
        method: "post",
        data ,
    });
  }

  // 会议通知---查询近期会议分页列表
  static queryMeetingSignPage(data) {
    return request({
        url: "/ds/meeting/sign/queryMeetingSignPage",
        method: "post",
        data ,
    });
  }

  // 会议通知---会议报名
  static signMeeting(data) {
    return request({
        url: "/ds/meeting/sign/signMeeting",
        method: "post",
        data ,
    });
  }




  /**
   * --------------------------会议流程管理-------------------------------
   */

  // 会议流程管理---新增会议流程
  static createMeetingFlow(data) {
    return request({
        url: "/ds/meetingFlow/createMeetingFlow",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---删除会议流程
  static deleteMeetingFlow(data) {
    return request({
        url: "/ds/meetingFlow/deleteMeetingFlow",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---修改会议流程
  static updateMeetingFlow(data) {
    return request({
        url: "/ds/meetingFlow/updateMeetingFlow",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---查询会议流程详情
  static queryMeetingFlowDetail(data) {
    return request({
        url: "/ds/meetingFlow/queryMeetingFlowDetail",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---查询会议流程分页列表
  static queryMeetingFlowPage(data) {
    return request({
        url: "/ds/meetingFlow/queryMeetingFlowPage",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---发布会议流程
  static updateMeetingFlowStatus(data) {
    return request({
        url: "/ds/meetingFlow/updateMeetingFlowStatus",
        method: "post",
        data ,
    });
  }

  // 会议流程管理---查询已发布的流程
  static queryMeetingFlowList(data) {
    return request({
        url: "/ds/meetingFlow/queryMeetingFlowList",
        method: "get",
        data ,
    });
  }



  // 会议类型管理---新增会议类型
  static createMeetingType(data) {
    return request({
        url: "/ds/meetingType/createMeetingType",
        method: "post",
        data ,
    });
  }

  // 会议类型管理---删除会议类型
  static deleteMeetingType(data) {
    return request({
        url: "/ds/meetingType/deleteMeetingType",
        method: "post",
        data ,
    });
  }

  // 会议类型管理---修改会议类型
  static updateMeetingType(data) {
    return request({
        url: "/ds/meetingType/updateMeetingType",
        method: "post",
        data ,
    });
  }

  // 会议类型管理---查询会议类型列表
  static queryMeetingTypeList(data) {
    return request({
        url: "/ds/meetingType/queryMeetingTypeList",
        method: "post",
        data ,
    });
  }

  // 会议类型管理---查询会议类型分页列表
  static queryMeetingTypePage(data) {
    return request({
        url: "/ds/meetingType/queryMeetingTypePage",
        method: "post",
        data ,
    });
  }


    /**
     * --------------------------会议材料和纪要-------------------------------
     */

    // 会议材料和纪要---查询会议材料和纪要分页列表
    static queryMeetingFilePage(data) {
        return request({
            url: "/ds/meetingFile/queryMeetingFilePage",
            method: "post",
            data,
        });
    }

    // 会议材料和纪要---删除会议材料和纪要
    static deleteMeetingFile(data) {
        return request({
            url: "/ds/meetingFile/deleteMeetingFile",
            method: "post",
            data,
        });
    }


    // 历史会议---查询历史会议分页列表
    static queryMeetingHistoryPage(data) {
        return request({
            url: "/ds/meeting/history/queryMeetingHistoryPage",
            method: "post",
            data,
        });
    }
}
