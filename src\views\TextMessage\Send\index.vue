<template>
    <div class="contact-book-container">
        <div class="contact-book-main">
            <el-form ref="form" :model="form" :rules="rules" class="contact-book-main-form" label-width="100px" size="small">
                <el-row :gutter="20">
                    <el-col :span="16">
                        <div class="textMessage-template-container">
                            <el-form-item label="短信模版" prop="templateId">
                                <el-radio-group v-model="form.templateId" v-removeAriaHidden @change="onTemplate">
                                    <el-radio v-for="(item) in textMessageTemplate" :key="item.templateId"
                                        :label="item.templateId">
                                        {{ item.templateTheme }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <div class="action">
                                <el-button @click="handleAction('', 'template')">选择模板</el-button>
                                <el-button @click="handleAction('', 'audio')">通话语音</el-button>
                                <el-button @click="handleAction('', 'image')">图片识别</el-button>
                            </div>
                        </div>
                        <div class="textMessage-template-container">
                            <el-form-item label="收信人" prop="detailList">
                                <InputTag placeholder="请选择收信人"  v-model="form.detailList"></InputTag>
                            </el-form-item>
                            <div class="action">
                                <el-button @click="handleAction('', 'personnelGrouping')">选择分组</el-button>
                                <el-button @click="handleAction('', 'addUser')">添加人员</el-button>

                            </div>
                        </div>
                        <el-form-item label="短信主题" prop="theme">
                            <el-input v-model="form.theme" placeholder="请输入短信主题" />
                        </el-form-item>
                        <el-form-item label="短信内容" prop="content">
                            <el-input v-model="form.content" placeholder="请输入短信内容" type="textarea" class="content_textarea" resize="none" />
                            <div class="mission-box">
                                <div class="mission-total">{{ form.content ? form.content.length : 0 }} / 500 </div>
                                <div>
                                    <el-button @click="handleAction('', 'namesuffix')">署名后缀</el-button>
                                    <el-button @click="handleAction('', 'textsuffix')">文字后缀</el-button>
                                    <el-button @click="handleAction('', 'correct')">智能纠错</el-button>
                                    <el-button @click="handleAction('', 'clear')">清空</el-button>
                                </div>
                            </div>
                        </el-form-item>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="发送单位" prop="editor">
                                    <el-select v-model="form.editor" placeholder="请选择发送单位" style="width:300px">
                                        <el-option :label="item.name" :value="item.value"
                                            v-for="(item, index) in editorOptions" :key="index" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="发送通道" prop="channel" label-width="130">
                                    <el-radio-group v-model="form.channel" v-removeAriaHidden>
                                        <el-radio label="1"> 55573000 </el-radio>
                                        <el-radio label="2"> 局短信平台 </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>

                        </el-row>


                    </el-col>
                    <el-col :span="8">
                        <p class="aiTitle">AI助手</p>
                        <el-form-item label="" prop="grace" label-width="0">
                            <el-input v-model="form.grace" placeholder="" resize="none" type="textarea"  class="ai_textarea" />
                            <div class="mission-box">
                                <div class="mission-total">{{ form.content ? form.content.length : 0 }} / 500 </div>
                                <div>
                                    <el-button  @click="handleAction('', 'copy')">复制</el-button>
                                   
                                </div>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="footer">
                <el-button @click="handleSaveNote">保存</el-button>
                <el-button type="primary"  @click="handleSendNote" >发送</el-button>
            </div>
        </div>
        <Template :visible.sync="templateVisible" @modelInput="handleTemplateInput"></Template>
        <PersonnelGrouping ref="personnelGroupingRef" :visible.sync="personnelGroupingVisible" :modelValue="form.detailList" @update:modelValue="form.detailList = $event"></PersonnelGrouping>
        <AddUser :visible.sync="addUserVisible" :modelValue="form.detailList" @update:modelValue="form.detailList = $event"></AddUser>
    </div>
</template>

<script>
import { textMessageTemplateApi,textMessageSendApi  } from "@/api";
import InputTag from '@/components/InputTag.vue'
import Template from './components/template.vue'
import AddUser from '../Components/addUser.vue'
import PersonnelGrouping from '../Components/personnelGrouping.vue'
import { mapState, mapActions } from "vuex";
export default {
    name: '',
    components: {
        InputTag,
        Template,
        PersonnelGrouping,
        AddUser
    },
    props: {
    },
    data() {
        return {
            form: {
                templateId: '',
                detailList: [],
                theme: '',
                content: '',
                editor: '',
                channel: '1',
                reviewer: '',
                grace: '',
                requiredGroupCount: 0,
                sendType:'immediately'
            },
            textMessageTemplate: [
            ],// 短信模版
            editorOptions: [
                {
                    name: "市应急办",
                    value: "市应急办"
                }, 
                {
                    name: "市委",
                    value: "市委"
                }, 
                {
                    name: "市政府",
                    value: "市政府"
                }
            ],//发送单位
            rules: {
                templateId: [
                    {
                        required: true,
                        message: '请选择或添加收信人',
                        trigger: "blur"
                    },
                ],
                detailList: [
                    {
                        required: true,
                        message: '请选择收信人或输入收信人邮箱',
                        trigger: "blur",
                    },
                ],
                theme: [
                    {
                        required: true,
                        message: '请输入短信主题',
                        trigger: "blur"
                    },
                ],
                content: [
                    {
                        required: true,
                        message: '请输入短信内容',
                        trigger: "change"
                    },
                ],
                editor: [
                    {
                        required: true,
                        message: '请选择发送单位',
                        trigger: "change"
                    },
                ],
                channel: [
                    {
                        required: true,
                        message: '请输入选择发送通道',
                        trigger: "blur"
                    },
                ],
            },
            templateVisible:false,//选择模版状态
            personnelGroupingVisible:false,//选择分组状态
            addUserVisible:false,
        }
    },
    computed: {
        ...mapState("user", ["userInfo"]),
    },
    methods: {
        async initTemplateData(){
            let requestParams = await textMessageTemplateApi.getTextMessageTemplateListAll({isCommonlyUsed:1});
            this.textMessageTemplate = requestParams.data
        },
        onTemplate(value){
            let results = this.textMessageTemplate.find(item => item.templateId == value)
            this.form.templateId  = results.templateTheme
            this.form.content = results.templateContent
            this.form.templateId = results.templateId
        },
        handleTemplateInput(row){
            this.form.theme = row.templateTheme
            this.form.content = row.templateContent
            this.form.templateId = row.templateId
        },
        handleSaveNote(){
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.form.reviewer = this.userInfo.name
                    this.form.taskId = this.$route.query.id
                    textMessageSendApi.textMessageSave(this.form).then(()=>{
                        this.$message.success('保存成功')
                        this.$router.push({
                            path:'/textMessage/draft'
                        })
                    })
                } else {
                    return false;
                }
            });
        },
        handleSendNote(){
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.form.taskId = this.$route.query.id
                    this.form.reviewer = this.userInfo.name
                    textMessageSendApi.textMessageSend(this.form).then(()=>{
                        this.$router.push({
                            path:'/textMessage/management'
                        })
                        this.$message.success('发送成功') 
                    })
                } else {
                    return false;
                }
            });
        },
        handleAction(row, type) {
            switch (type) {
                case 'template':
                    this.templateVisible = true
                    break;
                case 'audio':
                    this.$message.info(`通话语音功能开发`);
                    break;
                case 'image':
                    this.$message.info(`图片识别功能开发`);
                    break;
                case 'personnelGrouping':
                    this.personnelGroupingVisible = true
                    this.$nextTick(()=>{
                        this.$refs.personnelGroupingRef.incoming(this.form.detailList)
                    })
                    break;
                case 'addUser':
                    this.addUserVisible = true
                   
                    break;   
                case 'namesuffix':
                   if (this.form.editor) {
                        this.form.content = this.form.content + "（" + this.form.editor + "）"
                    } else {
                        this.$message.warning('请先选择发送单位')
                    }
                    break;    
                case 'textsuffix':
                    textMessageSendApi.queryEnable().then(res => {
                        this.form.content = this.form.content + '后缀'
                    })
                    break;    
                case 'correct':
                    textMessageSendApi.textCorrection({content:this.form.content}).then(res => {
                        this.form.grace = res.data.content
                    })
                    break;  
                case 'clear':
                    this.form.grace = ""
                    this.form.content = ""
                    break; 
                case 'copy':
                    if (this.form.grace) {
                        navigator.clipboard.writeText(this.form.grace)
                        this.$message.success('复制成功')
                    } else {
                        this.$message.warning('请先配置复制内容')
                    }
                    break;     
                default:
                    break;
            }
            
        },
        //获取短信详情
        queryTextMessageDetails(){
            if(!this.$route.query.id) return
            textMessageSendApi.queryTextMessageDetails({taskId:this.$route.query.id}).then((res)=>{
                let {templateId,theme,content,editor,reviewer,grace,detailList} = res.data
                this.form =  {
                    templateId: templateId,
                    detailList:detailList,
                    theme: theme,
                    content: content,
                    editor: editor,
                    channel: '1',
                    reviewer: reviewer,
                    grace: grace,
                    requiredGroupCount: 0,
                    sendType:'immediately'
                }
            })

        }
    },
    mounted() {
        this.queryTextMessageDetails()
        this.initTemplateData()
    }
}
</script>

<style scoped lang="scss">
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}
.contact-book-main-form{
    flex: 1;
    margin-bottom: 20px;
    overflow-y: auto;
    overflow-x: hidden;
}
.textMessage-template-container {
    display: flex;

    .action {
        margin-left: 10px;
    }
}

.mission-box {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}

.el-form-item {
    flex: 1;
}
.content_textarea,.ai_textarea{
    &.ai_textarea{
        height: 570px;
    }
    &.content_textarea{
        height: 460px;
    }
    
    ::v-deep(.el-textarea__inner){
        height: 100%;
    }
}
.footer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}
::v-deep(.el-radio){
    margin-right: 13px;
}

</style>