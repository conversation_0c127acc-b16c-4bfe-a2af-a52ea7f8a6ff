<template>
  <div class="user-index-container">
    <!-- 日志管理 - Page -->

    <!-- 表格区域 -->
    <portal-table
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { logApi } from "@/api/index.js";

export default {
  name: "LogManagement",
  components: { PortalTable },
  data() {
    return {
      columns: [
        { text: true, prop: "sysName", label: "系统名称" },
        { text: true, prop: "pageName", label: "功能名称" },
        { text: true, prop: "itemName", label: "操作名称" },
        { text: true, prop: "username", label: "用户姓名" },
        { text: true, prop: "createTime", label: "日期时间" },
        { text: true, prop: "logType", label: "日志类型" },
        { text: true, prop: "sysMessage", label: "系统类型" },
      ],

      searchItems: [
        {
          prop: "systemName",
          label: "系统名称",
          type: "input",
          placeholder: "请输入系统名称",
        },
        {
          prop: "userName",
          label: "用户姓名",
          type: "input",
          placeholder: "请输入用户姓名",
        },
        {
          prop: "dateRange",
          label: "日期时间",
          type: "startEndPicker",
          placeholder: "请选择日期范围",
        },
        {
          prop: "operationType",
          label: "日志类型",
          type: "select",
          placeholder: "全部",
          options: [
            { label: "操作日志", value: "操作日志" },
            { label: "登录日志", value: "登录日志" },
            { label: "系统日志", value: "系统日志" },
          ],
        },
      ],

      tableData: [],
      searchParams: {},
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },

  mounted() {
    this.loadLogList();
  },

  methods: {
    // 加载日志列表
    async loadLogList() {
      try {
        const params = {
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          ...this.searchParams,
        };

        const response = await logApi.queryLogPage(params);

        if (response && response.data) {
          this.tableData = response.data.items || [];
          this.pagination.total = response.data.total || 0;
        }
      } catch (error) {
        this.$message.error("加载日志列表失败: " + error.message);
      }
    },

    // 搜索
    handleSearch(searchParams) {
      this.searchParams = { ...searchParams };
      this.pagination.currentPage = 1;
      this.loadLogList();
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.loadLogList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.loadLogList();
    },
  },
};
</script>

<style lang="scss" scoped>
.user-index-container {
  padding: 20px;
}
</style>
