<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :general-dialog-title="dialogTitle"
    dialog-width="800px"
    :show-footer="type != '审核详情'"
    @cancel="handleClose"
    @confirm="handleSubmit"
  >
    <div style="padding: 30px">
      <div
        style="padding: 0 40px"
        v-if="type == '审核详情' && processData.length == 0"
      >
        审核状态：未提交审核
      </div>
      <el-timeline class="el-review-timeline">
        <el-timeline-item
          v-for="(item, index) in processData"
          :key="index"
          :timestamp="item.date + '  ' + item.time"
          placement="top"
        >
          <div class="timeline-card">
            <div class="timeline-card-item">机构名称：{{ item.deptName }}</div>
            <div class="timeline-card-item">
              <p>操作人：{{ item.userName }}</p>
              <p class="timeline-card-item-status">
                操 作：{{ getStatusLabel(item.status) }}
              </p>
            </div>

            <div class="timeline-card-item">审核意见：{{ item.comment }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <el-form
        v-if="type != '审核详情'"
        ref="form"
        class="add-form"
        :rules="rules"
        :model="form"
        label-width="110px"
      >
        <el-form-item :label="type + '意见'" prop="result">
          <el-radio-group v-model="form.result">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" prop="comment">
          <el-input
            v-model="form.comment"
            :placeholder="'请输入' + type + '意见'"
            type="textarea"
            class="content_textarea"
            resize="none"
          />
        </el-form-item>
      </el-form>
    </div>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { warningPublishApi } from "@/api";
export default {
  components: {
    GeneralDialog,
  },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        result: 1,
        comment: "",
        warningId: "",
      },
      dialogVisible: false,
      processData: [],
      dialogTitle: "",
      type: "",
      rules: {
        result: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        comment: [
          {
            required: true,
            message: "请输入意见",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 拟办
    async queryProcess(row) {
      const res = await warningPublishApi.queryProcess({
        id: row.id,
      });
      if (res.code === 0) {
        this.processData = res.data;
      }
    },
    handleSubmit() {
      if (this.type === "拟办") {
        this.SubmitProposed();
      } else if (this.type === "审批") {
        this.SubmitApprove();
      }
    },
    // 拟办
    SubmitProposed() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            warningPublishApi
              .proposed({
                ...this.form,
              })
              .then((res) => {
                if (res.code === 0) {
                  this.dialogVisible = false;
                  this.$message.success("拟办成功");
                  this.$emit("initData");
                }
              });
          } catch {}
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },
    // 审批
    SubmitApprove() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            warningPublishApi
              .approve({
                ...this.form,
              })
              .then((res) => {
                if (res.code === 0) {
                  this.dialogVisible = false;
                  this.$message.success("审批成功");
                  this.$emit("initData");
                }
              });
          } catch {}
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    getStatusLabel(status) {
      console.log(this.options, status);

      return this.options.find((item) => item.value == status)?.label || status;
    },
  },
};
</script>

<style scoped lang='scss'>
::v-deep(.el-review-timeline) {
  overflow-y: auto;
  max-height: 450px;
  padding-top: 5px;
  .timeline-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background: #fff;
    padding: 10px;

    .timeline-card-item {
      display: flex;

      p {
        width: 50%;
      }
    }
  }

  .el-timeline-item__tail {
    background: #507ac2;
    border-left: 2px solid #507ac2;
  }

  .el-timeline-item__node {
    background-color: #507ac2;
  }

  .el-timeline-item__timestamp {
    margin-bottom: 7px;
    padding-top: 0px;
    font-size: 16px;
    color: #313131;
  }

  .timeline-card-item-status {
    color: #0057ec;
  }
}
.add-form {
  padding: 0px;
}
</style>