<template>
  <!-- 右侧内容区域 -->
  <div class="contact-book-container">
    <div class="contact-book-main">
      <!-- 搜索区域 -->
      <div class="search-wrapper">
        <SearchForm
          :form-config="searchConfig"
          :tips="searchTips"
          :show-tips="false"
          @search="handleSearch"
          @reset="handleReset"
          ref="searchFormRef"
        />
      </div>
      <!-- 表格区域 -->
      <div class="table-wrapper">
        <DataTable
          :table-data="contactList || []"
          :columns="responsiveTableColumns || []"
          :tableHeight="600"
          :total="pagination?.total || 0"
          :current-page="pagination?.current || 1"
          :page-size="pagination?.pageSize || 10"
          :row-actions="tableActions"
          :action-column-width="300"
          :loading="loading"
          :stripe="true"
          :border="true"
          :show-pagination="true"
          :show-index="true"
          :key="tableKey"
          @row-action="handleTableRowAction"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!-- 联系人表单对话框 -->
    <ContactForm
      ref="contactFormRef"
      :visible.sync="formVisible"
      :form-data="currentContact"
      :isEdit="isEdit"
      :eventTypeOptions="eventTypeOptions"
      @update:isEdit="isEdit = $event"
      @submit="handleFormSubmit"
    />
    <!-- 发布申请 -->
    <application-dialog ref="applicationDialogRef" @initData="initData" />
    <!-- 拟办 -->
    <proposed-form
      ref="proposedFormRef"
      @initData="initData"
      :options="approvalStatusOptions"
    />
    <!-- 提交报批 -->
    <report-form ref="reportFormRef" @initData="initData" />
    <!-- 智能辅助 -->
    <smart-reminder-dialog
      :eventTypeOptions="eventTypeOptions"
      ref="smartReminderRef"
      @initData="initData"
    />
  </div>
</template>

<script>
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import ContactForm from "./components/ContactForm.vue";
import ApplicationDialog from "./components/applicationDialog.vue";
import ProposedForm from "./components/ProposedForm.vue";
import ReportForm from "./components/ReportForm.vue";
import SmartReminderDialog from "./components/SmartReminderDialog.vue";
import { getItemList, eventType } from "@/utils/dictionary";
import mixinTable from "./mixinTable.js";
import { systemManagementApi, warningPublishApi } from "@/api";
const APPROVAL_STATUS_OPTIONS = [
  { label: "待发布申请", value: "1" },
  { label: "待拟办", value: "2" },
  { label: "拟办退回", value: "3" },
  { label: "拟办通过", value: "4" },
  { label: "报批审核", value: "5" },
  { label: "报批回退", value: "6" },
  { label: "报批通过", value: "7" },
];
export default {
  mixins: [mixinTable],
  components: {
    SearchForm,
    DataTable,
    ContactForm,
    ApplicationDialog,
    ProposedForm,
    ReportForm,
    SmartReminderDialog,
  },
  data() {
    return {
      // 搜索配置
      searchConfig: [
        {
          prop: "matter",
          label: "预警事项",
          type: "input",
          placeholder: "输入预警名称",
        },
        {
          prop: "deptId",
          label: "发布单位",
          type: "input",
          placeholder: "输入发布单位",
        },
        {
          prop: "approvalStatus",
          label: "审核状态",
          type: "select",
          placeholder: "请选择",
          options: APPROVAL_STATUS_OPTIONS,
        },
        {
          prop: "publishStatus",
          label: "发布状态",
          type: "select",
          placeholder: "请选择",
          options: [
            { label: "未发布", value: "0" },
            { label: "已发布", value: "1" },
          ],
        },
        {
          prop: "pushStartTime",
          label: "发布开始时间",
          type: "date",
          placeholder: "输选择发布开始时间",
          // format: "yyyy-MM-dd HH:mm:ss",
        },
        {
          prop: "pushEndTime",
          label: "发布结束时间",
          type: "date",
          placeholder: "输选择发布结束时间",
          // format: "yyyy-MM-dd HH:mm:ss",
        },
        {
          prop: "eventType",
          label: "事件类型",
          type: "select",
          placeholder: "输选择事件类型",
          options: [],
        },
      ],
      searchTips: [],
      // 表格列配置
      tableColumns: [
        { prop: "pushTime", label: "发布时间", width: 150, sortable: false },
        {
          prop: "pushDeptName",
          label: "发布单位",
          width: 150,
          sortable: false,
        },
        { prop: "matter", label: "预警事项", minWidth: 200, sortable: false },
        {
          prop: "level",
          label: "预警等级",
          width: 150,
          sortable: false,
          render: (row, index) => {
            return ["蓝色", "黄色", "橙色", "红色"][row.level - 1];
          },
        },
        { prop: "startDate", label: "起始时间", width: 150, sortable: false },
        {
          prop: "approvalStatus",
          label: "审核状态",
          width: 150,
          sortable: false,
          render: (row, index) => {
            return [
              "待发布申请",
              "待拟办",
              "拟办退回",
              "拟办通过",
              "报批审核",
              "报批回退",
              "报批通过",
            ][row.approvalStatus - 1];
          },
        },
        {
          prop: "publishStatus",
          label: "发布状态",
          width: 150,
          sortable: false,
          render: (row, index) => {
            return ["未发布", "已发布"][row.publishStatus];
          },
        },
      ],
      tableActions: [
        {
          key: "view",
          label: "查看",
          type: "text",
          size: "mini",
        },
        {
          key: "edit",
          label: "编辑",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["1", "3", "6"].includes(row.approvalStatus);
          },
        },
        {
          key: "remove",
          label: "删除",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["1", "3", "6"].includes(row.approvalStatus);
          },
        },
        {
          key: "application",
          label: "发布申请",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["1", "3", "6"].includes(row.approvalStatus);
          },
        },
        {
          key: "proposed",
          label: "拟办",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["2"].includes(row.approvalStatus);
          },
        },
        {
          key: "submitForApproval",
          label: "提交报批",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["4"].includes(row.approvalStatus);
          },
        },
        {
          key: "approve",
          label: "审批",
          type: "text",
          size: "mini",
          show: (row) => {
            return ["5"].includes(row.approvalStatus);
          },
        },
        {
          key: "publish",
          label: "发布",
          type: "text",
          size: "mini",
          show: (row) => {
            return (
              ["7"].includes(row.approvalStatus) && row.publishStatus === "0"
            );
          },
        },
        {
          key: "smartReminder",
          label: "智能辅助",
          type: "text",
          size: "mini",
        },
        {
          key: "auditDetails",
          label: "审核详情",
          type: "text",
          size: "mini",
        },
      ],
      // 表格刷新key
      tableKey: 0,
      // 表单相关状态
      formVisible: false,
      currentContact: {},
      isEdit: "add",
      eventTypeOptions: [],
    };
  },
  computed: {
    // 响应式表格列配置
    responsiveTableColumns() {
      return this.tableColumns;
    },
    approvalStatusOptions() {
      return APPROVAL_STATUS_OPTIONS;
    },
    eventTypeOptionsComputed() {
      return this.eventTypeOptions;
    },
  },
  methods: {
    // 搜索相关方法
    async handleSearch(formData) {
      // 保持当前的组织过滤状态
      const searchParams = { ...formData };
      await this.seachContactList(searchParams);
      await this.getContactList();
      this.$message.success("搜索完成");
    },
    async initData() {
      try {
        await this.getContactList();
      } catch (error) {
        console.error("短信模版页面初始化失败:", error);
        this.$message.error("页面初始化失败，请刷新重试");
      }
    },
    // 重置
    async handleReset() {
      await this.seachContactList({});
      await this.setCurrentChange({ current: 1 });
      await this.getContactList();
    },
    // 提交
    async handleFormSubmit(formData, isEdit) {
      if (isEdit == "edit") {
        await this.updateWarningPublish(formData);
      } else {
        await this.createWarningPublish(formData);
      }
      this.initData();
      this.formVisible = false;
    },
    // 点击列表操作
    async handleTableRowAction(action, row, index) {
      let type = action.key;
      let response = null;
      switch (type) {
        case "view":
          console.log(row);
          this.formVisible = true;
          this.isEdit = "view";
          response = await this.queryWarningPublish(row);
          this.currentContact = response;
          break;
        case "edit":
          this.formVisible = true;
          this.isEdit = "edit";
          response = await this.queryWarningPublish(row);
          this.currentContact = response;
          break;

        case "remove":
          this.$confirm("确定要删除吗", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              await this.removeWarningPublish(row);
              this.initData();
            })
            .catch(() => {});

          break;
        case "application":
          this.$refs.applicationDialogRef.dialogVisible = true;
          this.$refs.applicationDialogRef.warningId = row.id;
          this.$refs.applicationDialogRef.selectedNode = [];
          this.$refs.applicationDialogRef.formData = {
            selectedUserId: "",
          };
          break;
        case "submitForApproval":
          this.$refs.reportFormRef.dialogVisible = true;
          this.$refs.reportFormRef.resetForm();
          this.$refs.reportFormRef.form.warningId = row.id;
          break;
        case "proposed":
          this.$refs.proposedFormRef.dialogVisible = true;
          this.$refs.proposedFormRef.dialogTitle = "拟办";
          this.$refs.proposedFormRef.type = "拟办";
          this.$refs.proposedFormRef.queryProcess(row);
          this.$refs.proposedFormRef.form = {
            result: 1,
            comment: "",
            warningId: row.id,
          };
          break;
        case "approve":
          this.$refs.proposedFormRef.dialogVisible = true;
          this.$refs.proposedFormRef.dialogTitle = "审批";
          this.$refs.proposedFormRef.type = "审批";
          this.$refs.proposedFormRef.queryProcess(row);
          this.$refs.proposedFormRef.form = {
            result: 1,
            comment: "",
            warningId: row.id,
          };
          break;
        case "publish":
          this.publishHandle(row);
          break;
        case "auditDetails":
          this.$refs.proposedFormRef.dialogVisible = true;
          this.$refs.proposedFormRef.dialogTitle = "审核详情";
          this.$refs.proposedFormRef.type = "审核详情";
          this.$refs.proposedFormRef.queryProcess(row);

          break;
        case "smartReminder":
          this.$refs.smartReminderRef.dialogVisible = true;

          break;
        default:
          break;
      }
    },
    publishHandle(row) {
      this.$confirm("确认发布吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        warningPublishApi
          .publish({
            id: row.id,
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("发布成功");
              this.initData();
            }
          });
      });
    },
    // 分页Current
    async handleCurrentChange(page) {
      let formData = this.$refs.searchFormRef.formData;
      await this.seachContactList(formData);
      await this.setCurrentChange({ current: page });
      await this.getContactList();
    },
    // 分页Size
    async handleSizeChange(size) {
      this.pagination.current = 1;
      let formData = this.$refs.searchFormRef.formData;
      await this.seachContactList(formData);
      await this.setCurrentChange({ pageSize: size });
      await this.getContactList();
    },
    async getEventTypeOptions() {
      const res = await getItemList(eventType);
      this.eventTypeOptions = res.map((item) => ({
        label: item.itemName,
        value: item.itemValue,
      }));
      this.searchConfig.find((item) => item.prop === "eventType").options =
        this.eventTypeOptions;
    },
  },
  mounted() {
    this.getEventTypeOptions();
    this.initData();
  },
};
</script>

<style lang="scss" scoped>
.contact-book-container {
  display: flex;
  flex-direction: column;
}

.contact-book-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  overflow: hidden;
}

.search-wrapper {
  margin-bottom: 8px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-wrapper >>> .el-button--text:nth-child(1) {
  color: #409eff !important;
}

.table-wrapper >>> .el-button--text:nth-child(2) {
  color: #67c23a !important;
}

.table-wrapper >>> .el-button--text:nth-child(3) {
  color: #e6a23c !important;
}
</style>