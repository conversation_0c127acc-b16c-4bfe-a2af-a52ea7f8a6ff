<template>
  <div class="leader-selector">
    <!-- 领导批示用户选择器 - Component -->
    <el-input
      v-model="displayValue"
      :placeholder="placeholder"
      readonly
      clearable
      @click.native="showDialog = true"
      @clear="handleClear"
    >
      <template #suffix>
        <i class="el-icon-arrow-down" style="cursor: pointer"></i>
      </template>
    </el-input>

    <el-dialog
      title="选择批示领导"
      :visible.sync="showDialog"
      width="500px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :z-index="3000"
    >
      <div class="selector-content">
        <el-input
          v-model="searchText"
          placeholder="搜索领导姓名"
          clearable
          style="margin-bottom: 15px"
        >
          <template #prefix>
            <i class="el-icon-search search-icon"></i>
          </template>
        </el-input>

        <!-- 树组件 -->
        <div v-loading="loading" style="min-height: 200px">
          <el-tree
            ref="tree"
            :data="treeData"
            :props="treeProps"
            :filter-node-method="filterNode"
            node-key="id"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
            style="max-height: 400px; overflow-y: auto"
          >
            <template #default="{ node, data }">
              <span
                class="tree-node"
                :class="{
                  'is-user': data.type === 1,
                  'is-org': data.type === 0,
                  'is-selected': selectedUser && selectedUser.id === data.id,
                  'is-disabled': data.type === 0,
                }"
              >
                <i :class="getNodeIcon(data)" style="margin-right: 5px"></i>
                <span>{{ data.name }}</span>
                <span
                  v-if="data.type === 0"
                  style="color: #c0c4cc; font-size: 12px; margin-left: 5px"
                >
                  (组织)
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmSelection"
          :disabled="!selectedUser"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import leadershipDirectivesApi from "@/api/leadershipDirectives";

export default {
  name: "LeaderSelector",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择批示领导",
    },
  },
  data() {
    return {
      showDialog: false,
      searchText: "",
      treeData: [],
      selectedUser: null,
      displayValue: "",
      defaultExpandedKeys: [],
      loading: false, // 加载状态
      treeProps: {
        children: "children",
        label: "name",
        value: "id",
      },
    };
  },
  watch: {
    searchText(val) {
      this.$refs.tree && this.$refs.tree.filter(val);
    },
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.treeData.length > 0) {
          this.findAndSetUser(newVal);
        } else if (!newVal) {
          this.displayValue = "";
          this.selectedUser = null;
        }
      },
    },
    showDialog(val) {
      if (val) {
        this.loadOrgData();
      }
    },
  },
  methods: {
    async loadOrgData(forceRefresh = false) {
      // 如果已经有数据且不是强制刷新，直接返回
      if (this.treeData.length > 0 && !forceRefresh) {
        return;
      }

      this.loading = true;
      try {
        const response = await leadershipDirectivesApi.queryOrgAddressBook();

        if (response.code === 0 && response.data) {
          this.treeData = this.buildTreeData(response.data);
          this.setDefaultExpanded();

          // 如果有初始值，设置选中状态
          if (this.value) {
            this.$nextTick(() => {
              this.findAndSetUser(this.value);
            });
          }
        } else {
          this.$message.error("获取组织架构数据失败");
        }
      } catch (error) {
        this.$message.error("加载组织架构数据失败，请重试");
      } finally {
        this.loading = false;
      }
    },

    buildTreeData(data) {
      if (!Array.isArray(data)) return [];

      return data.map((item, index) => {
        const nodeData = {
          id: item.id || `node_${index}_${Date.now()}`, // 确保每个节点都有唯一ID
          name: item.orgName || item.userName || item.name, // 优先使用orgName或userName
          type: item.type, // 0: 组织, 1: 用户
          parentId: item.parentId,
          children: item.children ? this.buildTreeData(item.children) : [],
        };

        return nodeData;
      });
    },

    setDefaultExpanded() {
      // 默认展开第一层节点
      this.defaultExpandedKeys = this.treeData.map((item) => item.id);
    },

    getNodeIcon(data) {
      return data.type === 0 ? "el-icon-office-building" : "el-icon-user";
    },

    filterNode(value, data) {
      if (!value) {
        // 没有搜索条件时，显示所有节点（包括机构节点用于展示层级结构）
        return true;
      }
      // 有搜索条件时，只显示匹配的用户节点或包含匹配用户的机构节点
      if (data.type === 1) {
        // 用户节点：直接匹配名称
        return data.name.indexOf(value) !== -1;
      } else if (data.type === 0) {
        // 机构节点：检查是否包含匹配的用户子节点
        return this.hasMatchingUserChild(data, value);
      }
      return false;
    },

    // 检查机构节点是否包含匹配的用户子节点
    hasMatchingUserChild(orgNode, searchValue) {
      if (!orgNode.children || orgNode.children.length === 0) {
        return false;
      }

      for (const child of orgNode.children) {
        if (child.type === 1 && child.name.indexOf(searchValue) !== -1) {
          return true;
        }
        if (child.type === 0 && this.hasMatchingUserChild(child, searchValue)) {
          return true;
        }
      }
      return false;
    },

    handleNodeClick(data) {
      // Element UI tree 组件的 data 参数就是节点数据
      const nodeData = data;

      // 只允许选择用户节点（type=1），禁止选择机构节点
      if (nodeData && nodeData.type === 1) {
        this.selectedUser = nodeData;
      } else if (nodeData && nodeData.type === 0) {
        // 机构节点不可选择，给出提示
        this.$message.warning("请选择具体的用户，不能选择机构");
        this.selectedUser = null;
      }
    },

    confirmSelection() {
      if (this.selectedUser && this.selectedUser.type === 1) {
        this.displayValue = this.selectedUser.name;
        this.$emit("change", this.selectedUser.id);
        this.$emit("select", {
          userId: this.selectedUser.id,
          userName: this.selectedUser.name,
          parentId: this.selectedUser.parentId,
        });
        this.showDialog = false;
      } else {
        this.$message.warning("请选择一个用户");
      }
    },

    handleClear() {
      this.displayValue = "";
      this.selectedUser = null;
      this.$emit("change", "");
      this.$emit("select", null);
    },

    findAndSetUser(userId) {
      const user = this.findUserInTree(this.treeData, userId);
      if (user) {
        this.displayValue = user.name;
        this.selectedUser = user;
      }
    },

    findUserInTree(nodes, userId) {
      for (const node of nodes) {
        if (node.id == userId && node.type === 1) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findUserInTree(node.children, userId);
          if (found) return found;
        }
      }
      return null;
    },

    // 强制刷新数据
    async refreshData() {
      this.treeData = [];
      this.selectedUser = null;
      await this.loadOrgData(true);
    },
  },
};
</script>

<style lang="scss" scoped>
.leader-selector {
  width: 100%;

  .selector-content {
    .tree-node {
      display: flex;
      align-items: center;

      &.is-user {
        color: #303133;
        cursor: pointer;

        &:hover {
          color: #409eff;
        }
      }

      &.is-org {
        color: #909399;
        cursor: not-allowed;

        &:hover {
          color: #909399;
        }
      }

      &.is-disabled {
        color: #c0c4cc;
        cursor: not-allowed;

        &:hover {
          color: #c0c4cc;
        }
      }

      &.is-selected {
        background-color: #e6f7ff;
        color: #409eff;
        font-weight: bold;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
