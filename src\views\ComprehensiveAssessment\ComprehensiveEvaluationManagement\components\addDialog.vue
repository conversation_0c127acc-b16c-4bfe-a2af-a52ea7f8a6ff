<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :dialog-width="'700px'"
    :general-dialog-title="dialogTitle"
    :set-component-name="$store.getters.componentName"
    @cancel="handleCancel"
    :button-list="buttonList"
    :showConfirm="false"
    @buttonClick="handleButtonClick"
  >
    <el-form
      ref="form"
      :model="form"
      class="add-form"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-form-item label="通知标题" prop="noticeTitle">
        <el-input v-model="form.noticeTitle" />
      </el-form-item>
      <el-form-item label="通知类型" prop="noticeType">
        <el-select v-model="form.noticeType" placeholder="请选择">
          <el-option
            v-for="item in noticeTypeList"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="接收单位" prop="receiverUnit">
        <el-cascader
          v-model="form.receiverUnit"
          :options="orgOptions"
          :props="orgProps"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="通知内容" prop="noticeRemark">
        <el-input type="textarea" v-model="form.noticeRemark"></el-input>
      </el-form-item>
      <el-form-item label="附件" prop="fileInfoList">
        <el-upload
          class="upload-demo"
          action="#"
          drag
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :file-list="form.fileInfoList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            支持PDF、DOC、DOCX格式，最大10MB
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="发送时间" prop="sendTime">
        <el-date-picker
          type="datetime"
          placeholder="选择时间"
          v-model="form.sendTime"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import emergencyKnowledgeBaseApi from "@/api/emergencyKnowledgeBase";
import conversionDate from "@/utils/publicMethod";
export default {
  components: {
    GeneralDialog,
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: "新建方案",
      criteriaListData: [],
      form: {
        noticeTitle: "",
        noticeType: "",
        receiverUnit: [],
        noticeRemark: "",
        fileInfoList: "",
        sendTime: "",
      },
      receiverUnitModel: "",
      noticeTypeList: [
        {
          label: "月度评价通知",
          value: "0",
        },
        {
          label: "季度评价通知",
          value: "1",
        },
        {
          label: "年度评价通知",
          value: "2",
        },
      ],
      rules: {
        noticeTitle: [
          { required: true, message: "请输入通知标题", trigger: "blur" },
        ],
        noticeType: [
          { required: true, message: "请选择通知类型", trigger: "blur" },
        ],
        receiverUnit: [
          { required: true, message: "请输入接收单位", trigger: "blur" },
        ],
        noticeRemark: [
          { required: true, message: "请输入通知内容", trigger: "blur" },
        ],
        fileInfoList: [
          { required: true, message: "请上传附件", trigger: "blur" },
        ],
        sendTime: [
          { required: true, message: "请选择发送时间", trigger: "blur" },
        ],
      },
      checkboxCriteriaList: [],
      buttonList: [
        {
          id: 1,
          text: "保存通知",
          type: "primary",
        },
        {
          id: 2,
          text: "发送",
          type: "primary",
        },
      ],
      orgOptions: [],
      orgProps: {
        value: "id",
        label: "orgName",
        children: "children",
        // 只保留选中叶子节点
        emitPath: false,
        // 多选，父节点可以选中
        multiple: true,
      },
    };
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },

    handleSubmit(status) {
      this.form.sendTime = conversionDate(this.form.sendTime);
      this.form.sendStatus = status;
      // this.form.receiverUnit = this.form.receiverUnit.join(",");
      const params = {
        ...this.form,
        receiverUnit: this.form.receiverUnit.join(","),
      };
      this.$refs.form.validate((valid) => {
        if (valid) {
          comprehensiveAssessmentApi.createNotice(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.$emit("refreshTableData");
            }
          });
        }
      });
    },

    // 查询评价细则列表
    queryCriteriaList() {
      comprehensiveAssessmentApi
        .queryCriteriaList({
          page: 1,
          count: 50,
        })
        .then((res) => {
          this.criteriaListData = res.data.items;
        });
    },
    // 查询评价方案详情
    querySchemeById(row) {
      comprehensiveAssessmentApi
        .querySchemeById({
          schemeId: row.schemeId,
        })
        .then((res) => {
          if (res.code === 0) {
            console.log(res, "res");
            res.data.fileInfoList.forEach((element) => {
              element.name = element.fileName;
            });
            this.form = res.data;
            this.checkboxCriteriaList = res.data.criteriaList.map(
              (item) => item.criteriaId
            );
          }
        });
    },
    async customUpload(options) {
      const formData = new FormData();
      formData.append("file", options.file);
      try {
        const res = await emergencyKnowledgeBaseApi.uploadFile(formData);
        if (res.code === 0) {
          res.data.name = res.data.fileName;
          console.log(this.form, "uuuuuuuu");
          this.form.fileInfoList.push(res.data);
        }
      } catch (error) {
        console.error("上传失败", error);
      }
    },
    beforeUpload(file) {
      const ext = file.name.split(".").pop().toLowerCase();
      const targetExtensions = ["rar", "zip", "doc", "docx", "pdf", "jpg"];
      const includes = targetExtensions.includes(ext);
      if (!includes) {
        this.$message.warning(
          "上传文件只能是 rar、zip、doc、docx、pdf、jpg 格式!"
        );
      }
      return includes;
    },
    handleRemove(file, fileList) {
      this.form.fileInfoList = fileList;
    },
    handleCheckedChange(checkedList) {
      console.log(checkedList, "checkedList");

      this.form.criteriaList = checkedList.map((id) => ({ criteriaId: id }));
    },
    resetForm() {
      this.form = {
        schemeName: "",
        type: "",
        cycle: "",
        startTime: "",
        endTime: "",
        describe: "",
        criteriaList: [],
        fileInfoList: [],
      };
      this.checkboxCriteriaList = [];
    },
    handleButtonClick(item) {
      if (item.id === 1) {
        this.handleSubmit(0);
      } else if (item.id === 2) {
        this.handleSubmit(1);
      }
    },

    // 查询组织机构树形结构
    async queryOrg() {
      const res = await comprehensiveAssessmentApi.queryOrg({
        id: "",
        orderRule: 0,
        orgName: "",
        parentId: "",
      });
      if (res.code === 0) {
        this.orgOptions = res.data;
        this.removeEmptyChildren(this.orgOptions);
        console.log(res);
      }
    },
    // 去掉空children
    removeEmptyChildren(nodes) {
      if (!nodes || !Array.isArray(nodes)) return;
      nodes.forEach((node) => {
        if (node.children && node.children.length === 0) {
          delete node.children;
        } else {
          this.removeEmptyChildren(node.children);
        }
      });
    },
  },
  mounted() {
    this.queryCriteriaList();
    this.queryOrg();
  },
};
</script>

<style  lang="scss" scoped>
.criteria-list {
  max-height: 250px;
  overflow-y: auto;
  background-color: #f9fafb;
  padding: 10px;
  .criteria-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .score {
      font-size: 15px;
    }
  }
  .add-bottom {
    color: #409eff;
    text-align: right;
    cursor: pointer;
  }
}
.upload-demo {
  .el-upload__tip {
    line-height: 20px !important;
  }
}
::v-deep .el-upload-list__item {
  transition: none;
}
</style>