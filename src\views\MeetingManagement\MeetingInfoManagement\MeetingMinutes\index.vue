<!-- 会议纪要---HistoryMeeting -->
<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <!--    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
    >
    </general-dialog>-->

    <el-dialog
      title="预览"
      :custom-class="'preview-dialog'"
      :visible.sync="showIframe"
      v-if="showIframe"
      width="90%"
      :before-close="handleCloseIframe"
    >
      <iframe
        :src="preFileUrl"
        frameborder="0"
        width="100%"
        height="100%"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { dutyManagementApi, meetingManagementApi } from "@/api";

import { conversionDateNotSecond, getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils";

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "meetingName",
          label: "会议名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingTime",
          label: "会议日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260",
        },
        {
          prop: "meetingNotes",
          label: "会议说明",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingType",
          label: "会议类别",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "meetingName", label: "会议名称", text: true },
        { prop: "meetingDate", label: "会议日期", text: true },
        { prop: "meetingNotes", label: "会议说明", text: true },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "查看纪要",
              permission: "meetingMinutes:view",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "下载纪要",
              permission: "meetingMinutes:download",
              buttonClick: this.handleDownload,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "分享",
              permission: "meetingMinutes:share",
              buttonClick: this.handleSignUp,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "meetingMinutes:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      inspectionTypeList: [],
      inspectionResultList: [],
      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增会议纪要",

      form: {
        inspectionTime: "",
        inspectionType: "",
        inspectionUnit: "",
        inspectionJob: "",
        inspectionUser: "",
        inspectionResult: "",
        fileList: [],
      },
      rules: {
        inspectionTime: [
          { required: true, message: "检查时间不能为空", trigger: "blur" },
        ],
        inspectionType: [
          { required: true, message: "检查方式不能为空", trigger: "blur" },
        ],
        inspectionUnit: [
          { required: true, message: "被检查单位不能为空", trigger: "blur" },
        ],
        inspectionJob: [
          { required: true, message: "被检查岗位不能为空", trigger: "blur" },
        ],
        inspectionUser: [
          { required: true, message: "被检查人员不能为空", trigger: "blur" },
        ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
  },
  methods: {
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          this.searchItems[3].options = this.meetingTypeList.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      if (row.fileList && row.fileList.length > 0) {
        row.fileList.forEach((item) => {
          this.fileList.push({
            name: item.fileName,
            url: this.fileBaseUrl + item.fileUrl,
            id: item.id,
          });
        });
      } else {
        row.fileList = [];
      }
      this.form = {
        ...row,
      };
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.form = {};
      this.fileList = [];
      this.form.fileList = [];
      this.generalDialogTitle = "新增会议纪要";
    },

    //查看会议纪要
    handleEdit(row) {
      this.getRowDataInfo(row);
      this.handlePreview(this.fileList[0].url);
    },

    //删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let ids = row.fileList.map((item) => {
          return item.id;
        });
        const res = await meetingManagementApi.deleteMeetingFile({ ids: ids });
        const { code, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("删除成功");
        await this.getTableDataList();
      });
    },

    handleDownload(row) {
      const loading = this.$loading({
        lock: true,
        text: "正在下载...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        // 添加文件存在性校验
        if (!row.fileList || row.fileList.length === 0) {
          this.$message.warning("没有可下载的文件");
          loading.close();
          return;
        }

        // 修正文件地址拼接方式
        const fileInfo = row.fileList[0];
        if (!fileInfo.fileUrl) {
          this.$message.warning("文件链接无效");
          loading.close();
          return;
        }

        // 使用统一的地址拼接方式
        const fullUrl = this.fileBaseUrl + fileInfo.fileUrl;

        fetch(fullUrl)
          .then((response) => {
            if (!response.ok)
              throw new Error(`HTTP error! status: ${response.status}`);
            return response.blob();
          })
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = fileInfo.fileName; // 使用服务器返回的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          })
          .catch((error) => {
            console.error("文件下载失败:", error);
            this.$message.error("文件下载失败，请检查文件是否存在");
          })
          .finally(() => {
            loading.close();
          });
      } catch (error) {
        console.error("下载异常:", error);
        loading.close();
        this.$message.error("下载过程中发生错误");
      }
    },

    handleSignUp() {
      this.$confirm("确定分享此次会议, 是否继续?", "分享", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("分享成功");
      });
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        type: 1, //类型 1:会议纪要 2:会议材料
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams,
      };
      const res = await meetingManagementApi.queryMeetingFilePage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      console.log("查询列表", data);
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1;
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0]);
        row.endTime = conversionDateNotSecond(row.inspectionTime[1]);
        delete row.inspectionTime;
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    handlePreview(fileUrl) {
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(fileUrl);
      }
      //window.open(fileUrl, "_blank");
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
    },

    handleCloseIframe(){
      this.showIframe = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}

::v-deep{
  .preview-dialog{
    height: 90%;
    margin-top: 35px !important;

    .el-dialog__body{
      height: 93%;
    }
  }
}

</style>
