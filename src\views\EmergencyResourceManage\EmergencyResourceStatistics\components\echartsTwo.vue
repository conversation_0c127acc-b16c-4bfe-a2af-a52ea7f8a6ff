<!-- 
author：ghl
time:2025-01-23

应急演练分析统计
-->
<template>
    <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>
<script>
import jsonData from '../../../../utils/mapData.json';
// import axios from 'axios';
// import { color } from 'd3';
import * as echarts from 'echarts'
export default {
    props: {
        chartsTwoList: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            bjMap:''
        }
    },
    mounted() {
        this.initChart()
    },
    methods: {
        initChart() {
                this.bjMap = jsonData;
                var mapName = "bj";
                var xdata = []
                this.chartsTwoList.map((item) => {
                    xdata.push({name:item.item_name, value:item.groupCount, })
                })
                echarts.registerMap(mapName, eval(this.bjMap));
                const chartDom = this.$refs.chart;
                const myChart = echarts.init(chartDom);
                const option = {
                    tooltip: {
                        trigger: 'item', // 触发类型，'item' 表示数据项图形触发
                        formatter: function (params) {
                            // 自定义提示框内容
                            return `${params.name}<br/>数量: ${params.value || 0}`;
                        }
                    },
                    
                    series: [
                        {
                            type: "map",
                            map: mapName,
                            zoom: 1.2,

                            itemStyle: {
                                normal: {
                                    borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                        offset: 0,
                                        color: "#00FFE3",
                                        },
                                        {
                                        offset: 1,
                                        color: "#4693EC",
                                        },
                                    ]),
                                    areaColor: '#1059BF',
                                },

                                emphasis: {
                                    label: {
                                        show: true, // 悬停时显示标签
                                        color: '#fff', // 悬停时字体颜色为红色
                                        fontSize: 12 // 悬停时字体大小为16px
                                    },
                                    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00FFE3",
                                        },
                                        {
                                            offset: 1,
                                            color: "#4693EC",
                                        },
                                    ]),
                                    borderWidth: 2,
                                },
                            },

                            data: xdata
                        },
                    ],
                };
                myChart.setOption(option);
            
            
        }
    }
}

</script>
<style>
</style>