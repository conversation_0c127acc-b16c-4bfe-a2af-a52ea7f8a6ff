/**
 * 接报信息详情数据获取混入 - Mixin
 *
 * 主要功能：
 * - 提供统一的数据获取接口
 * - 处理详情数据、过程信息、过程类型选项的获取
 * - 统一错误处理和加载状态管理
 */

import { receiveInformationApi, systemManagementApi } from "@/api";

export default {
  data() {
    return {
      loading: false,
      detailLoading: false,
      dataLoaded: false,
      detailData: {},
      processData: [],
      processTypeOptions: [],
    };
  },
  methods: {
    // 重置数据状态
    resetDataState() {
      this.dataLoaded = false;
      this.detailData = {};
      this.processData = [];
      this.processTypeOptions = [];
      this.loading = false;
      this.detailLoading = false;
    },

    async loadDetailData(detailId) {
      if (!detailId || this.loading || this.dataLoaded) {
        return;
      }

      this.loading = true;
      this.dataLoaded = false;

      try {
        const [detailResult, processResult, typeOptionsResult] =
          await Promise.allSettled([
            this.fetchDetailData(detailId),
            this.fetchProcessList(detailId),
            this.loadProcessTypeOptions(),
          ]);

        let hasError = false;
        const errors = [];

        if (detailResult.status === "rejected") {
          hasError = true;
          errors.push(
            `详情数据加载失败: ${
              detailResult.reason?.message || detailResult.reason
            }`
          );
          console.error("详情数据加载失败:", detailResult.reason);
        }

        if (processResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程信息加载失败: ${
              processResult.reason?.message || processResult.reason
            }`
          );
          console.error("过程信息加载失败:", processResult.reason);
        }

        if (typeOptionsResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程类型选项加载失败: ${
              typeOptionsResult.reason?.message || typeOptionsResult.reason
            }`
          );
          console.error("过程类型选项加载失败:", typeOptionsResult.reason);
          this.processTypeOptions = [];
        }

        // 如果有错误，显示错误信息，但不阻止页面显示
        if (hasError) {
          this.$message.warning(
            `部分数据加载失败，但可以继续使用: ${errors.join("; ")}`
          );
        }

        this.dataLoaded = true;
      } catch (error) {
        console.error("加载详情数据失败:", error);
        this.$message.error("加载详情数据失败: " + (error.message || error));
        this.dataLoaded = true;
      } finally {
        this.loading = false;
      }
    },

    async fetchDetailData(detailId) {
      if (!detailId) return;

      try {
        const response = await receiveInformationApi.queryReportInfo({
          id: detailId,
        });

        if (response && response.code === 0) {
          this.detailData = response.data || {};
        } else {
          this.$message.error(response?.message || "获取详情失败");
          throw new Error(response?.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情出错:", error);
        this.$message.error("获取详情失败");
        throw error;
      }
    },

    async fetchProcessList(detailId, selectedProcessType = "") {
      if (!detailId) return;

      try {
        const params = {
          id: detailId, // 事件ID
        };

        if (selectedProcessType) {
          params.processType = selectedProcessType;
        }

        const response = await receiveInformationApi.queryEventProcessInfoList(
          params
        );

        if (response && response.code === 0) {
          let processData = [];
          if (Array.isArray(response.data)) {
            processData = response.data.map((item) => {
              return {
                id: item.id,
                eventId: detailId, // 使用当前事件ID
                reportId: item.reportId, // 报告ID
                createTime: item.courseTime || item.createTime, // 过程时间
                processType: item.courseType, // 过程类型
                processTypeName: item.courseTypeName, // 过程类型名称
                eventInfo: item.courseInfo, // 过程信息内容
                eventTitle: item.courseName, // 过程标题
                eventReportingUnit: item.courseName || "-", // 上报单位
                eventReportingUser: "-", // 上报用户
                responsibleDepartment: item.courseName || "-", // 责任部门
                updateTime: item.updateTime,
                courseName: item.courseName, // 用于显示
                courseInfo: item.courseInfo, // 用于显示
                // 保留原始数据
                originalData: item,
              };
            });
          } else if (response.data && Array.isArray(response.data.items)) {
            processData = response.data.items;
          } else if (response.data && Array.isArray(response.data.records)) {
            processData = response.data.records;
          }

          this.processData = processData || [];
        } else {
          this.$message.error(response?.message || "获取过程信息失败");
          throw new Error(response?.message || "获取过程信息失败");
        }
      } catch (error) {
        console.error("获取过程信息出错:", error);
        this.$message.error("获取过程信息列表失败");
        throw error;
      }
    },

    // 加载过程类型选项
    async loadProcessTypeOptions() {
      try {
        const response = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507051549",
        });

        if (response && response.code === 0) {
          this.processTypeOptions = (response.data || []).map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        } else {
          throw new Error(response?.message || "获取过程类型选项失败");
        }
      } catch (error) {
        console.error("获取过程类型选项失败:", error);
        throw error;
      }
    },

    async showProcessDetail(item) {
      if (!item) return null;

      const otherId = item.originalData?.otherId;
      const otherType = item.originalData?.otherType;

      this.detailLoading = true;

      try {
        const requestParams = {
          otherId,
          otherType,
        };

        const [detailResponse] = await Promise.allSettled([
          receiveInformationApi.queryEventProcessInfo(requestParams),
        ]);

        let response = null;
        if (detailResponse.status === "fulfilled") {
          response = detailResponse.value;
        } else {
          console.error("获取过程详情失败:", detailResponse.reason);
          throw detailResponse.reason;
        }

        if (response && response.code === 0 && response.data) {
          return {
            // 基本信息
            id: response.data.id,
            eventId: response.data.reportInfoId,
            eventTitle: response.data.infoTitle,
            eventInfo: response.data.eventInfo,
            createTime: response.data.createTime,
            updateTime: response.data.updateTime,

            // 地理位置信息
            infoDistrict: response.data.infoDistrict,
            infoTownshipStreet: response.data.infoTownshipStreet,
            infoLocationDetail: response.data.infoLocationDetail,
            infoLongitude: response.data.infoLongitude,
            infoLatitude: response.data.infoLatitude,

            // 事件分类信息
            infoType: response.data.infoType,
            infoChildType: response.data.infoChildType,
            infoThirdType: response.data.infoThirdType,

            // 伤亡统计
            deathNum: response.data.deathNum || 0,
            missingNum: response.data.missingNum || 0,
            severeInjuryNum: response.data.severeInjuryNum || 0,
            lightInjuryNum: response.data.lightInjuryNum || 0,

            // 上报信息
            infoReportingMethod: response.data.infoReportingMethod,
            infoReportingUnit: response.data.infoReportingUnit,
            infoReportingUser: response.data.infoReportingUser,
            eventReportingUnit: response.data.infoReportingUnit,

            // 责任部门
            responsibleDepartment: response.data.infoReportingUnit,

            // 保留原始数据
            originalData: response.data,
          };
        } else {
          // 接口返回失败或无数据
          console.error(
            "获取过程详情失败:",
            response?.message || "接口返回数据为空"
          );
          throw new Error(response?.message || "接口返回数据为空");
        }
      } catch (error) {
        console.error("获取过程详情出错:", error);
        throw error;
      } finally {
        this.detailLoading = false;
      }
    },
  },
};
