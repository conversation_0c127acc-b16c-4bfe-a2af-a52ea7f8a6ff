<template>
  <div style="padding: 20px;" class="user-index-container">
    <div>
      <el-select
        style="width: 300px; margin-bottom: 20px;"
        v-model="currentTemplate"
        clearable
        filterable
        :placeholder="'请选择'"
        @change="getTemplateListById(currentTemplate)"
      >
        <el-option
          v-for="item in postTemplateList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-button
        style="margin-left: 20px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleAddPostClick()"
      >
        新增岗位 
      </el-button>
    </div>

    <portal-table
      :tableHeight="tableHeight"
      :showAddButton="false"
      :columns="columns"
      :search-items="searchItems"
      :show-pagination="false"
      :showSelection=false
      :table-data="tableData"
      row-key="id"
      @handle-selection-change="handleSelectionChange"
    />
    <!-- 新增模版 -->
    <general-dialog
      :dialog-visible="templateDialog"
      :dialog-width="dialogWidth"
      :general-dialog-title="templateTitle"
      :showFooter=false
      @cancel="handleCancel"
    >
      <templateAdd ref="templateAddClient" @ok="loadData"/>
    </general-dialog>

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :showFooter=false
      @cancel="handleCancel"
    >
      <addEidt ref="addEditClient" @ok="loadData"/>
    </general-dialog>

  </div>
</template>

<script>

import { dutyManagementApi } from '@/api/index'
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import {conversionDate} from '@/utils/publicMethod'

import addEidt from './components/addEidt.vue'
import templateAdd from './components/templateAdd.vue'

export default {
  name: 'UserIndex',
  components: {PortalTable, GeneralDialog, templateAdd, addEidt},
  data() {
    return {
      tableHeight: 600,
      columns: [
        {text: true, prop: 'positionName', label: '岗位名称'},
        // {text: true, prop: 'level', label: '岗位级别'},
        {text: true, prop: 'num', label: '岗位人数'},
        {text: true, prop: 'sort', label: '位次'},
        {text: true, prop: 'createTime', label: '创建时间'},
        {text: true, prop: 'createName', label: '创建人'},
        {
          action: true, //是否显示操作
          label: '操作',
          operationList: [
            {
              label: '编辑',
              permission: 'dutyPost:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'dutyPost:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      searchItems: [],
      currentTemplate: "",
      postTemplateList: [
        {
          id: "1",
          name: "汛期岗位模板",
        },
        {
          id: "2",
          name: "森防期岗位模板",
        },
      ],
      tableData: [
        // {
        //   id:'1',
        //   name:'带班领导',
        //   num:2,
        //   sort:1,
        //   createTime:'2025-06-28',
        //   createName:'张三',
        //   children: [
        //     {
        //       id:'1-1',
        //       name:'主班',
        //       num:2,
        //       sort:1,
        //       createTime:'2025-06-28',
        //       createName:'张三',
        //     },
        //      {
        //       id:'1-2',
        //       name:'副班',
        //       num:1,
        //       sort:2,
        //       createTime:'2025-06-28',
        //       createName:'张三',
        //     }
        //   ]

        // }
      ],
      dialogVisible: false,
      dialogWidth: '560px',
      generalDialogTitle: '新增值班岗位',

      templateDialog: false,
      templateTitle: '新增值班模板'
    }
  },
  created() {

  },
  mounted() {
    const gaping = 50 + 40 + 12 + 20 + 45 + 60;
    this.tableHeight = window.innerHeight - gaping;
    this.registerHandlers();
    this.getAllTemplateList();
  },
  methods: {
    registerHandlers() {
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'add_top',
        handler: this.handleAddTemplateClick
      });
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'edit_top',
        handler: this.handleEditTemplateClick
      });
    },
    handleAddTemplateClick() {
      this.templateDialog = true;
      this.templateTitle = "新增值班模板"
      this.$nextTick(function () {
        this.$refs.templateAddClient.addFormFn()
      })
    },
    handleEditTemplateClick() {
      this.templateDialog = true;
      this.templateTitle = "编辑值班模板"
      this.$nextTick(function () {
        this.$refs.templateAddClient.edit(this.currentTemplate)
      })
    },

    // 获取所有模板
    async getAllTemplateList() {
      const res = await dutyManagementApi.queryPositionTemplateList()
      const {code, data, error} = res;
      
      if (code === 0) {
        this.postTemplateList = data;
        this.currentTemplate = this.postTemplateList[0].id;
        this.getTemplateListById(this.currentTemplate);
      } else {
        this.$message.error(error)
      }
    },
    // 获取当前模板下的值班信息
    async getTemplateListById(id) {
      const res = await dutyManagementApi.queryPositionList({id})
      const {code, data, error} = res;
      if (code === 0) {
        this.tableData = data;
      } else {
        this.$message.error(error)
      }
    },

    //选中数据
    handleSelectionChange(selection) {
      console.log(selection)
    },

    loadData(e) {
      this.dialogVisible = false;
      this.templateDialog = false;
      if (e === 1) {
        return
      }
      this.getAllTemplateList()
    },

    //新增
    handleAddPostClick() {
      // 新增岗位
      this.generalDialogTitle = '新增值班岗位'
      this.dialogVisible = true
      this.$nextTick(function () {
        this.$refs.addEditClient.addFormFn(this.currentTemplate)
      })
    },
    //编辑
    handleEdit(row) {
      this.generalDialogTitle = '修改值班岗位'
      this.dialogVisible = true
      this.$nextTick(function () {
        this.$refs.addEditClient.edit(row)
      })
    },

    //删除
    handleDelete(row) {
      let txt = '此操作将永久删除该条数据, 是否继续?'
      if (row.children && row.children.length > 0) {
        txt = '此操作将永久删除该条数据和子级数据，是否继续？'
      }
      this.$confirm(txt, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dutyManagementApi.deletePosition({id: row.id}).then((res) => {
          const {code, data, error} = res;
          if (code !== 0) return this.$message.error(res.data.message)
          this.$message.success('删除成功')
          this.getAllTemplateList()
        })
      })
    },

    handleCancel() {      
      this.dialogVisible = false;
      this.templateDialog = false;
    },
  }
}
</script>

<style lang="scss" scoped></style>
