<!-- 舆情详情---PublicOpinionEdit -->
<template>
  <div class="public-opinion-edit">
    <div class="public-opinion-edit__content">
      <!-- 标题 -->
      <div class="public-opinion-edit__title">
        <span>{{ publicOpinionData.title || "暂无标题" }}</span>
      </div>
      <!-- 信息 -->
      <div class="public-opinion-edit__information">
        <span>发布时间：{{ publicOpinionData.warningTime || "暂无时间" }}</span>
        <span>来源：{{ publicOpinionData.webName || "暂无来源" }}</span>
        <span>访问量：{{ publicOpinionData.visitCount || "暂无访问量" }}</span>
      </div>
      <!-- 摘要 -->
      <div class="public-opinion-edit__abstract">
        <div class="abstract">摘要</div>
        <div class="content">
          {{ publicOpinionData.summary || "暂无摘要" }}
        </div>
      </div>
      <!-- 正文 -->
      <div
        v-html="publicOpinionData.content"
        class="public-opinion-edit__content"
      ></div>
      <!-- 图片 -->
      <!--      <div class="public-opinion-edit__picture">
        <template v-if="publicOpinionData.imgUrls">
          <img v-for="item in publicOpinionData.imgUrls" :src="item" alt=""/>
        </template>
      </div>-->
      <!-- 表单 -->
      <div class="public-opinion-edit__form">
        <el-form ref="form" :model="form" label-width="110px">
          <el-form-item
            v-if="publicOpinionType === 'edit'"
            label="应急局处置"
            v-for="(item, index) in form.items"
            :key="index"
          >
            <div style="display: flex; align-items: center">
              <el-input v-model="item.value" style="flex: 1"></el-input>
              <el-button
                v-if="index !== form.items.length - 1"
                type="danger"
                circle
                icon="el-icon-minus"
                @click="removeItem(index)"
                style="margin-left: 10px"
              ></el-button>
              <el-button
                v-if="index === form.items.length - 1"
                type="primary"
                circle
                icon="el-icon-plus"
                @click="addItem"
                style="margin-left: 10px"
              ></el-button>
            </div>
          </el-form-item>
          <el-form-item label="添加位置" v-if="publicOpinionType === 'edit'">
            <div style="display: flex; align-items: center">
              <el-input v-model="form.name"></el-input>
              <el-button
                type="primary"
                circle
                icon="el-icon-map-location"
                style="margin-left: 10px"
                @click="mapLocation"
              ></el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button @click="close">返回</el-button>
            <el-button
              v-if="publicOpinionType === 'edit'"
              type="primary"
              @click="saveForm"
              >保存</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "PublicOpinionEdit",
  props: {
    publicOpinionData: {
      type: Object,
      default: () => ({}),
    },
    publicOpinionType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      form: {
        items: [{ value: "" }], // 初始包含一个空输入项
      },
    };
  },
  mounted() {
    /* const data = localStorage.getItem("PUBLIC-OPINION-DATA");
    const type = localStorage.getItem("PUBLIC-OPINION-TYPE");
    if (data) {
      this.publicOpinionData = JSON.parse(data);
      this.publicOpinionType = type;
    } else {
      this.$router.push({
        path: "/situationsManage/situations/publicOpinionList",
      });
    }*/
  },
  methods: {
    // 添加新的输入项
    addItem() {
      this.form.items.push({ value: "" });
    },

    // 删除最后一项
    removeItem(index) {
      if (this.form.items.length > 1) {
        this.form.items.splice(index, 1);
      }
    },

    //地图定位
    mapLocation() {
      console.log("地图定位");
    },

    //关闭返回
    close() {
      this.$emit("close");
    },

    // 保存表单数据
    saveForm() {
      console.log("Form Data:", this.form);
      // 在这里可以添加表单提交逻辑
    },
  },
  beforeDestroy() {
    localStorage.removeItem("PUBLIC-OPINION-DATA");
    localStorage.removeItem("PUBLIC-OPINION-TYPE");
  },
};
</script>
<style scoped lang="scss">
.public-opinion-edit {
  text-align: center;
  padding: 40px 110px 0 110px;

  &__content {
    width: 100%;
    margin: 0 auto;

    .public-opinion-edit__title {
      color: #333333;
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .public-opinion-edit__information {
      color: #999999;
      font-size: 16px;
      margin-bottom: 30px;
    }

    .public-opinion-edit__abstract {
      color: #333333;
      font-size: 18px;
      font-weight: 400;
      margin-bottom: 30px;
      padding: 0 50px;
      border-bottom: 1px solid #333333;
      display: flex;
      align-items: stretch; // 修改为拉伸对齐

      .abstract {
        width: 72px;
        height: auto;
        min-height: 100%; // 最小高度
        box-sizing: border-box; // 盒模型计算方式
        padding: 20px 10px;
        background-color: rgb(236, 236, 236);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .content {
        flex: 1;
        min-height: 50px;
        text-align: left;
        box-sizing: border-box; // 盒模型计算方式
        padding: 20px 10px; // 内边距保持对齐
      }
    }

    .public-opinion-edit__content {
      color: #333333;
      font-size: 18px;
      font-weight: 400;
      text-align: left;
      //text-indent: 2em;
      padding: 0 55px;
    }

    .public-opinion-edit__picture {
      width: 612px;
      //height: 293px;
      margin: 5px auto 30px auto;

      img {
        width: 100%;
        //height: 100%;
      }
    }
  }
}
</style>
