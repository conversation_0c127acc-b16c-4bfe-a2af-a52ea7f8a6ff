import request from "@/utils/request";

export default {
  /**
   * 普刊分页列表查询
   * @param {Object} params - 查询参数
   * @param {number} params.count - 每页数量 (1-999)
   * @param {number} params.page - 页码 (从1开始)
   * @param {string} params.periodicalTitle - 期号 (可选)
   * @param {string} params.startTime - 开始时间 (可选)
   * @param {string} params.endTime - 结束时间 (可选)
   * @param {number} params.status - 状态 (0=暂存, 1=待批示, 2=已批示)
   * @returns {Promise} API响应
   */
  queryPublicationPage(params) {
    return request({
      url: "/ds/publication/queryPublicationPage",
      method: "post",
      data: params,
    });
  },

  /**
   * 要情分页列表查询
   * @param {Object} params - 查询参数
   * @param {number} params.count - 每页数量 (1-999)
   * @param {number} params.page - 页码 (从1开始)
   * @param {string} params.periodicalTitle - 期号 (可选)
   * @param {string} params.startTime - 开始时间 (可选)
   * @param {string} params.endTime - 结束时间 (可选)
   * @param {number} params.status - 状态 (0=暂存, 1=待批示, 2=已批示)
   * @returns {Promise} API响应
   */
  queryPaginatedInfoPage(params) {
    return request({
      url: "/ds/publication/queryPaginatedInfoPage",
      method: "post",
      data: params,
    });
  },

  /**
   * 获取普刊信息文件地址
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  paginatedUrl(params) {
    return request({
      url: "/ds/publication/paginatedUrl",
      method: "post",
      data: params,
    });
  },
};
