/**
 * 短信后缀相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";

export default {
  // 获取短信后缀列表
  async getTextMessageSuffixList(params = {}) {
    return request({
      url: '/ds/smsCharactersSuffix/queryList',
      method: 'get',
      params: params
    })
  },
  // 新增短信后缀
  async createTextMessageSuffix(params = {}) {
    return request({
      url: '/ds/smsCharactersSuffix/create',
      method: 'post',
      data: params
    })
  },
  // 更新短信后缀
  async updateTextMessageSuffix(params = {}) {
    return request({
      url: '/ds/smsCharactersSuffix/update',
      method: 'post',
      data: params
    })
  },
    // 获取短信后缀详情
  async queryTextMessageSuffixById(params = {}) {
    return request({
      url: '/ds/smsCharactersSuffix/queryById',
      method: 'post',
      data: params
    })
  },
  // 删除短信后缀
  async removeTextMessageSuffix(params = {}) {
    return request({
      url: '/ds/smsCharactersSuffix/deleteById',
      method: 'post',
      data: params
    })
  },
};
