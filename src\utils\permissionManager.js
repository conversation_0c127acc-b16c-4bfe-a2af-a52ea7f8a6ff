/**
 * 权限管理器 - 处理用户权限和路由权限控制
 */

class PermissionManager {
  constructor() {
    this.userPermissions = new Set();
    this.routePermissions = new Map();
    this.buttonPermissions = new Map();
  }

  /**
   * 初始化用户权限
   * @param {Array} menuData - 用户菜单数据
   * @param {Array} buttonData - 用户按钮权限数据
   */
  initPermissions(menuData, buttonData = []) {
    // if (process.env.NODE_ENV === "development") {
    //   console.log("🔐 初始化权限系统", { menuData, buttonData });
    // }

    this.userPermissions.clear();
    this.routePermissions.clear();
    this.buttonPermissions.clear();

    // 处理菜单权限
    this.processMenuPermissions(menuData);

    // 处理按钮权限
    this.processButtonPermissions(buttonData);

    // if (process.env.NODE_ENV === "development") {
    //   console.log("✅ 权限系统初始化完成", {
    //     routes: this.routePermissions.size,
    //     buttons: this.buttonPermissions.size,
    //     userPermissions: this.userPermissions.size,
    //   });
    // }
  }

  /**
   * 递归处理菜单权限
   * @param {Array} menuData - 菜单数据
   * @param {string} parentPath - 父级路径
   */
  processMenuPermissions(menuData, parentPath = "") {
    if (!Array.isArray(menuData)) return;

    menuData.forEach((menu) => {
      const fullPath = menu.path;

      // 添加路由权限
      this.routePermissions.set(fullPath, {
        id: menu.id,
        name: menu.name,
        menuType: menu.menuType,
        parentPath,
      });

      // 添加到用户权限集合
      this.userPermissions.add(fullPath);

      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        this.processMenuPermissions(menu.children, fullPath);
      }
    });
  }

  /**
   * 处理按钮权限
   * @param {Array} buttonData - 按钮权限数据
   */
  processButtonPermissions(buttonData) {
    if (!Array.isArray(buttonData)) {
      if (process.env.NODE_ENV === "development") {
        console.warn("🔘 按钮权限数据不是数组:", buttonData);
      }
      return;
    }

    // if (process.env.NODE_ENV === "development") {
    //   console.log(`🔘 处理 ${buttonData.length} 个按钮权限`);
    // }

    buttonData.forEach((button) => {
      // 使用 permission 字段作为主要标识，如果没有则使用 path 或 name
      const buttonKey = button.permission || button.path || button.name;

      // 存储多种可能的键值组合，确保权限检查能够匹配
      const keys = [
        buttonKey, // 直接使用 permission 字段，如 "menu:add"
        `${button.parentPath || ""}:${buttonKey}`, // 父路径:权限标识
        `${button.parentPath || ""}:${button.path || button.name}`, // 原有逻辑兼容
      ];

      // 为每个可能的键都存储按钮权限
      keys.forEach((key) => {
        if (key && key !== ":") {
          this.buttonPermissions.set(key, {
            id: button.id,
            name: button.name,
            path: button.path,
            permission: button.permission,
            parentPath: button.parentPath,
            menuType: button.menuType,
          });
        }
      });
    });
  }

  /**
   * 检查路由权限
   * @param {string} routePath - 路由路径
   * @returns {boolean} 是否有权限
   */
  hasRoutePermission(routePath) {
    return (
      this.userPermissions.has(routePath) ||
      this.routePermissions.has(routePath)
    );
  }

  /**
   * 检查按钮权限（全局权限检查，不考虑页面隔离）
   * @param {string} buttonKey - 按钮标识
   * @param {string} parentPath - 父级路径
   * @returns {boolean} 是否有权限
   */
  hasButtonPermission(buttonKey, parentPath = "") {
    // 尝试多种可能的键值匹配
    const possibleKeys = [
      buttonKey, // 直接使用权限标识，如 "menu:add"
      `${parentPath}:${buttonKey}`, // 父路径:权限标识
      `${parentPath}:${buttonKey.split(":").pop()}`, // 父路径:操作类型（如果buttonKey包含冒号）
    ];
    return possibleKeys.some((key) => this.buttonPermissions.has(key));
  }

  /**
   * 检查页面级按钮权限（支持页面隔离）
   * @param {string} buttonKey - 按钮标识
   * @param {string} currentPagePath - 当前页面路径
   * @returns {boolean} 是否有权限
   */
  hasPageButtonPermission(buttonKey, currentPagePath = "") {
    if (!buttonKey || typeof buttonKey !== "string") {
      if (process.env.NODE_ENV === "development") {
        console.warn("🔐 权限验证失败: 权限标识无效", {
          buttonKey,
          currentPagePath,
        });
      }
      return false;
    }

    // 严格要求页面路径，不允许降级为全局权限检查
    if (!currentPagePath || typeof currentPagePath !== "string") {
      if (process.env.NODE_ENV === "development") {
        console.warn("🔐 权限验证失败: 页面路径无效，拒绝访问以确保权限隔离", {
          buttonKey,
          currentPagePath,
          reason: "页面级权限验证必须提供有效的页面路径",
        });
      }
      return false;
    }

    // 获取当前页面的所有按钮权限
    const pageButtons = this.getRouteButtons(currentPagePath);

    // if (process.env.NODE_ENV === "development") {
    //   console.log(`🔍 页面级权限验证: ${buttonKey} @ ${currentPagePath}`, {
    //     pageButtonsCount: pageButtons.length,
    //     pageButtons: pageButtons.map((btn) => ({
    //       name: btn.name,
    //       permission: btn.permission,
    //     })),
    //   });
    // }

    // 检查是否存在匹配的按钮权限
    const hasMatchingButton = pageButtons.some((button) => {
      // 精确匹配权限标识
      if (button.permission === buttonKey) {
        // if (process.env.NODE_ENV === "development") {
        //   console.log(`✅ 权限匹配成功: ${buttonKey} (精确匹配)`, button);
        // }
        return true;
      }

      // 严格权限匹配：只允许完全匹配的权限
      const buttonPermission = button.permission || button.path || button.name;
      if (buttonPermission === buttonKey) {
        // if (process.env.NODE_ENV === "development") {
        //   console.log(`✅ 严格权限匹配成功: ${buttonKey}`, button);
        // }
        return true;
      }

      return false;
    });

    // if (process.env.NODE_ENV === "development") {
    //   if (hasMatchingButton) {
    //     console.log(`✅ 严格权限检查通过: ${buttonKey} @ ${currentPagePath}`);
    //   } else {
    //     console.log(`❌ 严格权限检查失败: ${buttonKey} @ ${currentPagePath}`, {
    //       reason: "当前页面没有配置此权限（严格匹配模式）",
    //       requestedPermission: buttonKey,
    //       availablePermissions: pageButtons
    //         .map((btn) => btn.permission)
    //         .filter(Boolean),
    //     });
    //   }
    // }

    return hasMatchingButton;
  }

  /**
   * 获取用户可访问的路由列表
   * @returns {Array} 路由路径数组
   */
  getAccessibleRoutes() {
    return Array.from(this.userPermissions);
  }

  /**
   * 获取指定路径的按钮权限
   * @param {string} routePath - 路由路径
   * @returns {Array} 按钮权限数组
   */
  getRouteButtons(routePath) {
    const buttons = [];
    const addedButtons = new Set(); // 防止重复添加同一个按钮

    this.buttonPermissions.forEach((button) => {
      if (button.parentPath === routePath && !addedButtons.has(button.id)) {
        buttons.push(button);
        addedButtons.add(button.id);
      }
    });
    return buttons;
  }

  /**
   * 过滤菜单数据，只保留有权限的菜单
   * @param {Array} menuData - 原始菜单数据
   * @returns {Array} 过滤后的菜单数据
   */
  filterMenuByPermission(menuData) {
    if (!Array.isArray(menuData)) return [];

    return menuData.filter((menu) => {
      // 检查当前菜单权限
      if (!this.hasRoutePermission(menu.path)) {
        return false;
      }

      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = this.filterMenuByPermission(menu.children);
      }

      return true;
    });
  }

  /**
   * 清除所有权限数据
   */
  clearPermissions() {
    this.userPermissions.clear();
    this.routePermissions.clear();
    this.buttonPermissions.clear();
  }

  /**
   * 获取权限统计信息
   * @returns {Object} 权限统计
   */
  getPermissionStats() {
    return {
      totalRoutes: this.routePermissions.size,
      totalButtons: this.buttonPermissions.size,
      accessibleRoutes: this.userPermissions.size,
    };
  }

  /**
   * 导出权限数据
   * @returns {Object} 权限数据
   */
  exportPermissions() {
    return {
      routes: Object.fromEntries(this.routePermissions),
      buttons: Object.fromEntries(this.buttonPermissions),
      userPermissions: Array.from(this.userPermissions),
    };
  }
}

// 创建全局权限管理器实例
const permissionManager = new PermissionManager();

// 权限检查工具函数
export const hasPermission = (
  permission,
  parentPath = "",
  usePageIsolation = false
) => {
  // 如果权限为空或未定义，返回 false（拒绝访问），明确配置了权限的按钮才会显示
  if (
    !permission ||
    typeof permission !== "string" ||
    permission.trim() === ""
  ) {
    if (process.env.NODE_ENV === "development") {
      console.warn("权限标识无效，拒绝访问:", permission);
    }
    return false;
  }

  if (permission.startsWith("/")) {
    // 路由权限检查
    return permissionManager.hasRoutePermission(permission);
  } else {
    // 按钮权限检查
    if (usePageIsolation && parentPath) {
      // 使用页面级权限检查（推荐）
      return permissionManager.hasPageButtonPermission(permission, parentPath);
    } else {
      // 使用全局权限检查（向后兼容）
      return permissionManager.hasButtonPermission(permission, parentPath);
    }
  }
};

// 页面级权限检查工具函数（推荐使用）
export const hasPagePermission = (permission, currentPagePath = "") => {
  return hasPermission(permission, currentPagePath, true);
};

// 获取当前页面按钮权限
export const getPageButtons = (routePath) => {
  return permissionManager.getRouteButtons(routePath);
};

// 权限指令（Vue指令）
export const permissionDirective = {
  bind(el, binding, vnode) {
    const { value } = binding;
    const currentRoute = vnode.context.$route;

    if (!hasPermission(value, currentRoute.path)) {
      el.style.display = "none";
      // 或者直接移除元素
      // el.parentNode && el.parentNode.removeChild(el);
    }
  },
  update(el, binding, vnode) {
    const { value } = binding;
    const currentRoute = vnode.context.$route;

    if (!hasPermission(value, currentRoute.path)) {
      el.style.display = "none";
    } else {
      el.style.display = "";
    }
  },
};

export default permissionManager;
