<template>
  <div class="publication-edit-container">
    <!-- 刊物信息编辑/查看 - Component -->

    <div class="main-content">
      <div class="content-area">
        <!-- 根据选中的tab显示不同内容 -->
        <div class="tab-content-area">
          <!-- 全文报告 - 使用专门的编辑组件 -->
          <full-report-editor
            v-if="selectedAction === '全文报告'"
            :edit-data="editData"
            :is-edit-mode="isEditMode"
            @data-change="handleFullReportDataChange"
          />

          <!-- 其他tab - 使用文本编辑组件 -->
          <text-editor
            v-else-if="
              selectedAction && tabContents[selectedAction] !== undefined
            "
            :title="selectedAction"
            :content="tabContents[selectedAction]"
            :placeholder="`请输入${selectedAction}内容`"
            :is-edit-mode="isEditMode"
            :rows="12"
            @content-change="handleTextContentChange"
          />
        </div>
      </div>

      <!-- 右侧操作按钮区 -->
      <div class="action-area">
        <div class="action-buttons">
          <el-button
            :type="selectedAction === '全文报告' ? 'primary' : 'default'"
            @click="handleAction('全文报告')"
          >
            全文报告
          </el-button>
          <el-button
            :type="selectedAction === '防汛火灾' ? 'primary' : 'default'"
            @click="handleAction('防汛火灾')"
          >
            防汛火灾
          </el-button>
          <el-button
            :type="selectedAction === '突出情况' ? 'primary' : 'default'"
            @click="handleAction('突出情况')"
          >
            突出情况
          </el-button>
          <el-button
            :type="selectedAction === '全市调度' ? 'primary' : 'default'"
            @click="handleAction('全市调度')"
          >
            全市调度
          </el-button>
          <el-button
            :type="selectedAction === '天气气候' ? 'primary' : 'default'"
            @click="handleAction('天气气候')"
          >
            天气气候
          </el-button>
          <el-button
            :type="selectedAction === '联系信息' ? 'primary' : 'default'"
            @click="handleAction('联系信息')"
          >
            联系信息
          </el-button>
          <el-button
            :type="selectedAction === '公共交通' ? 'primary' : 'default'"
            @click="handleAction('公共交通')"
          >
            公共交通
          </el-button>
          <el-button
            :type="selectedAction === '大事动态' ? 'primary' : 'default'"
            @click="handleAction('大事动态')"
          >
            大事动态
          </el-button>
          <el-button
            :type="selectedAction === '舆情' ? 'primary' : 'default'"
            @click="handleAction('舆情')"
          >
            舆情
          </el-button>
          <el-button
            :type="selectedAction === '社情' ? 'primary' : 'default'"
            @click="handleAction('社情')"
          >
            社情
          </el-button>
          <el-button
            :type="selectedAction === '市情内容监测' ? 'primary' : 'default'"
            @click="handleAction('市情内容监测')"
          >
            市情内容监测
          </el-button>
          <el-button
            :type="selectedAction === '领导批示' ? 'primary' : 'default'"
            @click="handleAction('领导批示')"
          >
            领导批示
          </el-button>
          <el-button
            :type="selectedAction === '序言' ? 'primary' : 'default'"
            @click="handleAction('序言')"
          >
            序言
          </el-button>
          <el-button type="primary" @click="handleUploadAttachment">
            上传附件
          </el-button>
          <el-tooltip
            content="保存当前编辑内容到本地，下次进入时自动恢复，有效期七天"
            placement="left"
          >
            <el-button type="primary" @click="handleSaveDraft">暂存</el-button>
          </el-tooltip>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="footer-actions">
      <el-button @click="handleBack" size="large" class="back-button"
        >返回</el-button
      >
    </div>
  </div>
</template>

<script>
import FullReportEditor from "./components/FullReportEditor.vue";
import TextEditor from "./components/TextEditor.vue";

export default {
  name: "PublicationEdit",
  components: {
    FullReportEditor,
    TextEditor,
  },
  data() {
    return {
      publicationId: null,
      isEditMode: true,
      selectedAction: "全文报告", // 当前选中的操作按钮，默认选中第一个

      // 各个tab的内容数据
      tabContents: {
        全文报告:
          "这里是全文报告的内容编辑区域。可以编辑完整的报告内容，包括事故详情、处理过程、责任认定等。",
        防汛火灾:
          "这里是防汛火灾相关内容的编辑区域。可以编辑防汛预警、火灾预防、应急响应等相关信息。",
        突出情况:
          "这里是突出情况的编辑区域。可以编辑需要特别关注的重要事件、紧急情况等。",
        全市调度:
          "这里是全市调度信息的编辑区域。可以编辑调度指令、资源配置、人员安排等。",
        天气气候:
          "这里是天气气候信息的编辑区域。可以编辑天气预报、气候变化、气象预警等。",
        联系信息:
          "这里是联系信息的编辑区域。可以编辑相关部门联系方式、负责人信息等。",
        公共交通:
          "这里是公共交通信息的编辑区域。可以编辑交通状况、线路调整、运营信息等。",
        大事动态:
          "这里是大事动态的编辑区域。可以编辑重要事件进展、动态更新等。",
        舆情: "这里是舆情信息的编辑区域。可以编辑舆情监测、分析报告、应对措施等。",
        社情: "这里是社情信息的编辑区域。可以编辑社会情况、民情反馈、社会稳定等。",
        市情内容监测:
          "这里是市情内容监测的编辑区域。可以编辑监测数据、分析结果、趋势预测等。",
        领导批示:
          "这里是领导批示的编辑区域。可以编辑领导指示、批示内容、执行要求等。",
        序言: "这里是序言的编辑区域。可以编辑报告前言、背景介绍、总体概述等。",
      },

      // 编辑数据
      editData: {
        title: "接报四起交通事故，致4人死亡",
        events: [
          {
            number: "（一）",
            content:
              "6月21日7时41分，在大兴区黄村镇西白疙瘩村路口，一辆农用三轮机动车与一辆区属公交车发生碰撞，致农用车驾驶员死亡，公交车上无人受伤。",
            location: "（大兴区政府）",
          },
          {
            number: "（二）",
            content:
              "6月21日8时10分，在丰台区王佐镇街道营桥东路28号门口，一辆小客车与一行人发生碰撞，致行人死亡。",
            location: "（丰台区政府）",
          },
          {
            number: "（三）",
            content:
              "6月21日8时35分，在顺义区南法信镇京密路与顺沙文交口，一辆小货车与一辆摩托车发生碰撞，致摩托车车主死亡。",
            location: "（顺义区政府）",
          },
          {
            number: "（四）",
            content:
              "6月21日16时许，在海淀区苏家坨镇四季青桥附近，一辆小客车与一辆摩托车发生碰撞，致摩托车车主死亡。",
            location: "（市卫生健康委、海淀区政府）",
          },
        ],
        tableData: [
          {
            name: "交通事故数量",
            value: 4,
            unit: "起",
            date: "2025-06-21",
            status: "统计完成",
          },
          {
            name: "死亡人数",
            value: 4,
            unit: "人",
            date: "2025-06-21",
            status: "统计完成",
          },
        ],
        attachments: [
          {
            name: "事故现场照片.jpg",
            type: "图片",
            size: "2.5MB",
            uploadTime: "2025-06-21 10:30",
          },
        ],
      },

      // 原始数据（用于重置）
      originalData: {},
    };
  },
  computed: {
    hasChanges() {
      return (
        JSON.stringify(this.editData) !== JSON.stringify(this.originalData)
      );
    },
  },
  mounted() {
    this.initData();

    // 监听页面离开事件
    window.addEventListener("beforeunload", this.handleBeforeUnload);

    // 确保按钮区域可见
    this.$nextTick(() => {
      const actionArea = document.querySelector(".action-area");
      if (actionArea) {
        actionArea.style.display = "flex";
        actionArea.style.visibility = "visible";
        console.log("按钮区域已确保可见");
      }
    });

    // 调试信息
    console.log("组件挂载完成，selectedAction:", this.selectedAction);
    console.log("tabContents:", this.tabContents);
  },

  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("beforeunload", this.handleBeforeUnload);
  },
  methods: {
    // 初始化数据
    initData() {
      this.publicationId = this.$route.query.id;
      if (!this.publicationId) {
        this.$message.error("缺少刊物ID参数");
        this.$router.back();
        return;
      }

      // 尝试恢复暂存数据
      this.loadDraftData();

      // 保存原始数据
      this.originalData = JSON.parse(JSON.stringify(this.editData));
    },

    // 全文报告数据变更
    handleFullReportDataChange(newData) {
      this.editData = { ...newData };
      console.log("全文报告数据变更:", newData);
    },

    // 文本内容变更
    handleTextContentChange(newContent) {
      this.tabContents[this.selectedAction] = newContent;
      console.log(`${this.selectedAction}内容变更:`, newContent);
    },

    // 通用数据变更处理
    handleDataChange() {
      // 数据变更时的处理逻辑
      console.log("数据已变更");
    },

    // 处理操作按钮点击
    handleAction(action) {
      // 设置选中状态
      this.selectedAction = action;
    },

    // 上传附件
    handleUploadAttachment() {
      this.$message.info("上传附件功能");
    },

    // 暂存
    handleSaveDraft() {
      try {
        const draftData = {
          editData: this.editData,
          tabContents: this.tabContents,
          selectedAction: this.selectedAction,
          timestamp: new Date().getTime(),
        };

        const draftKey = `publication_draft_${this.publicationId}`;
        localStorage.setItem(draftKey, JSON.stringify(draftData));

        this.$message.success("暂存成功，数据已保存到本地");
      } catch (error) {
        console.error("暂存失败:", error);
        this.$message.error("暂存失败，请重试");
      }
    },

    // 加载暂存数据
    loadDraftData() {
      try {
        const draftKey = `publication_draft_${this.publicationId}`;
        const draftDataStr = localStorage.getItem(draftKey);

        if (draftDataStr) {
          const draftData = JSON.parse(draftDataStr);

          // 检查暂存数据是否过期（7天）
          const now = new Date().getTime();
          const draftTime = draftData.timestamp || 0;
          const expireTime = 7 * 24 * 60 * 60 * 1000; // 7天

          if (now - draftTime > expireTime) {
            // 数据过期，删除
            localStorage.removeItem(draftKey);
            return;
          }

          // 恢复数据
          if (draftData.editData) {
            this.editData = { ...this.editData, ...draftData.editData };
          }
          if (draftData.tabContents) {
            this.tabContents = {
              ...this.tabContents,
              ...draftData.tabContents,
            };
          }
          if (
            draftData.selectedAction &&
            this.tabContents[draftData.selectedAction] !== undefined
          ) {
            this.selectedAction = draftData.selectedAction;
          }

          this.$message.success("已恢复暂存数据");
        }
      } catch (error) {
        console.error("加载暂存数据失败:", error);
        // 静默失败，不影响正常使用
      }
    },

    // 清除暂存数据
    clearDraftData() {
      try {
        const draftKey = `publication_draft_${this.publicationId}`;
        localStorage.removeItem(draftKey);
      } catch (error) {
        console.error("清除暂存数据失败:", error);
      }
    },

    // 提交
    handleSubmit() {
      this.$confirm("确定要提交吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // 提交成功后清除暂存数据
        this.clearDraftData();
        this.$message.success("提交成功");
        // 可以在这里跳转到列表页面
        // this.$router.push('/informationSubmit/publicationInfo/publicationManagement');
      });
    },

    // 返回
    handleBack() {
      if (this.hasUnsavedChanges) {
        this.$confirm("当前有未保存的修改，是否先暂存再离开？", "提示", {
          confirmButtonText: "暂存并离开",
          cancelButtonText: "直接离开",
          distinguishCancelAndClose: true,
          type: "warning",
        })
          .then(() => {
            // 暂存并离开
            this.handleSaveDraft();
            setTimeout(() => {
              this.$router.back();
            }, 500);
          })
          .catch((action) => {
            if (action === "cancel") {
              // 直接离开
              this.$router.back();
            }
            // 如果是关闭对话框，则不做任何操作
          });
      } else {
        this.$router.back();
      }
    },

    // 页面离开前处理
    handleBeforeUnload(event) {
      if (this.hasUnsavedChanges) {
        const message = "当前有未保存的修改，确定要离开吗？";
        event.returnValue = message;
        return message;
      }
    },

    // 保存
    handleSave() {
      this.$confirm("确定要保存更改吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      }).then(() => {
        // 模拟保存API调用
        console.log("保存数据:", this.editData);
        // 更新原始数据
        this.originalData = JSON.parse(JSON.stringify(this.editData));
        this.$message.success("保存成功");
      });
    },
  },
};
</script>

<style scoped lang="scss">
// 全局防止横向滚动
* {
  box-sizing: border-box;
}

.publication-edit-container {
  padding: 15px;
  background: white;
  min-height: calc(100vh - 60px);
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;

  // 确保所有子元素不超出容器宽度
  * {
    max-width: 100%;
    box-sizing: border-box;
  }
}

.main-content {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  min-height: calc(100vh - 200px);
  max-width: 100%;
  overflow-x: hidden;
}

.content-area {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  background: white;
  min-width: 0; // 防止flex子元素溢出
  overflow-x: hidden;
}

.title-section {
  margin-bottom: 20px;

  .title-input {
    ::v-deep .el-input__inner {
      font-size: 18px;
      font-weight: bold;
      border: none;
      padding: 0;
      background: transparent;

      &:focus {
        border: none;
        box-shadow: none;
      }
    }
  }

  .title-display {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    line-height: 1.5;
  }
}

.tab-content-area {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;

  // 确保文本区域不会溢出
  ::v-deep .el-textarea {
    width: 100%;
    max-width: 100%;
  }

  ::v-deep .el-textarea__inner {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;
  }

  ::v-deep .el-input {
    width: 100%;
    max-width: 100%;
  }

  ::v-deep .el-input__inner {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}

.content-section {
  margin-bottom: 20px;
}

.content-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.content-text {
  line-height: 1.6;
  color: #606266;
  font-size: 14px;
}

.content-textarea {
  ::v-deep .el-textarea__inner {
    border: none;
    padding: 0;
    background: transparent;
    resize: none;
    font-size: 14px;
    line-height: 1.6;

    &:focus {
      border: none;
      box-shadow: none;
    }
  }
}

// 组件相关样式已移至各自组件中

.event-item {
  margin-bottom: 15px;

  .event-line {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    line-height: 1.6;

    .event-number {
      color: #303133;
      font-weight: normal;
      white-space: nowrap;
      margin-top: 8px;
    }

    .event-content-input {
      flex: 1;

      ::v-deep .el-textarea__inner {
        border: none;
        padding: 8px 0;
        background: transparent;
        resize: none;

        &:focus {
          border: none;
          box-shadow: none;
        }
      }
    }

    .event-content {
      flex: 1;
      color: #303133;
      padding: 8px 0;
    }

    .location-tag {
      color: #303133;
      white-space: nowrap;
      margin-top: 8px;
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.table-section,
.attachment-section {
  margin-bottom: 15px;
  overflow-x: auto;
  max-width: 100%;

  // 确保表格占满宽度且响应式
  ::v-deep .el-table {
    width: 100%;
    min-width: 600px; // 最小宽度确保表格可读性
  }

  ::v-deep .el-table__body-wrapper {
    width: 100%;
  }

  // 在小屏幕下优化表格显示
  @media (max-width: 768px) {
    ::v-deep .el-table {
      min-width: 500px;
      font-size: 12px;
    }

    ::v-deep .el-table th,
    ::v-deep .el-table td {
      padding: 8px 4px;
    }
  }
}

.action-area {
  width: 220px;
  min-width: 220px;
  max-width: 220px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  background: white;
  height: fit-content;
  display: flex !important;
  flex-direction: column;
  flex-shrink: 0;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
  z-index: 1;
  overflow-x: hidden;
  border-radius: 6px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .el-button {
    width: 100%;
    margin: 0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    height: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.el-button--primary {
      background: #409eff;
      border-color: #409eff;
      color: white;
    }

    &.el-button--success {
      background: #67c23a;
      border-color: #67c23a;
      color: white;
    }

    &.el-button--info {
      background: #909399;
      border-color: #909399;
      color: white;
    }

    &.el-button--warning {
      background: #e6a23c;
      border-color: #e6a23c;
      color: white;
    }

    &.el-button--default {
      background: white;
      border: 1px solid #dcdfe6;
      color: #606266;

      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background: #ecf5ff;
      }
    }
  }
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: white;
  max-width: 100%;
  overflow-x: hidden;

  .back-button {
    min-width: 160px;
    height: 40px;
  }

  .el-button {
    padding: 8px 16px;
    border-radius: 4px;

    &.el-button--primary {
      background: #409eff;
      border-color: #409eff;
    }
  }

  @media (max-width: 768px) {
    padding: 15px;
    gap: 10px;

    .back-button {
      padding: 8px 12px !important;
      font-size: 13px !important;
      min-width: 100px;
      height: 36px;
    }

    .el-button {
      padding: 6px 12px;
      font-size: 13px;
    }
  }
}

// 响应式设计 - 确保按钮区域始终可见且不出现横向滚动
@media (max-width: 1200px) {
  .publication-edit-container {
    padding: 10px;
  }

  .main-content {
    flex-direction: column;
    gap: 10px;
    max-width: 100%;
  }

  .action-area {
    width: 100% !important;
    min-width: auto !important;
    max-width: 100% !important;
    order: 2;
    display: flex !important;
    flex-direction: column;
    background: #f8f9fa;
    border-radius: 6px;
    margin-top: 10px;
    padding: 12px;
  }

  .content-area {
    order: 1;
    width: 100%;
    padding: 12px;
  }

  .action-buttons {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    max-width: 100%;

    .el-button {
      margin: 0 !important;
      width: 100% !important;
      font-size: 13px !important;
      padding: 8px 12px !important;
      height: 36px !important;
      min-width: 0;
    }
  }
}

@media (max-width: 768px) {
  .publication-edit-container {
    padding: 8px;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .main-content {
    gap: 8px;
    max-width: 100%;
  }

  .content-area,
  .action-area {
    padding: 10px;
    max-width: 100%;
  }

  .action-area {
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    width: 100% !important;
    min-width: auto !important;
    max-width: 100% !important;
    margin-top: 8px;
    overflow-x: hidden;
  }

  .action-buttons {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    max-width: 100%;

    .el-button {
      width: 100% !important;
      margin: 0 !important;
      font-size: 12px !important;
      padding: 8px 10px !important;
      height: 36px !important;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
