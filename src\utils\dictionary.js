import { systemManagementApi } from "@/api";

// 角色分级字典ID
const userClassDictionaryId = "202506231524";

// 用户职务字典ID
const userPositionDictionaryId = "202506231532";

// 短信模版类型
const textMessageTemplateType = "202506300935";

// 值班检查--检查方式类型
const inspectionDictionaryType = "202507021727";

// 值班检查--检查结果类型
const inspectionResultType = "202507021728";

// 信息接报/预警/典型案例--事件类型
const infoEventDictionaryType = "202507031421";

// 值班管理--工作通知--通知类型
const workNoticeType = "202507071426";

// 应急资源--物资类型
const equipmentType = "202501172216";

// 应急资源--应用领域
const applicationArea = "202501172217";

// 应急资源--物资状态
const combatReadinessStatus = "20250123173700";

// 应急资源--物资来源
const configurationMethodId = "202502051728";

// 应急资源--队伍等级
const teamLevelType = "202501172212";

// 避难场所--场所分级
const placeGradingType = "202507091633";

// 避难场所--依托资源类型
const resourceType = "202507091644";

// 避难场所--空间类型
const spaceType = "202507091655";

// 避难场所--总体功能
const allFunction = "202507091701";

// 避难场所--避难时长
const refugeTime = "202507091711";

// 预警信息-预警级别
const alertLevel = "202507101046";
// 事件类型
const eventType = "202507031421";
// 事件等级
const eventLevel = "202507181602";
// 信息同步类型
const infoSyncType = "202507271737";
//指挥部/现场指挥部
const commandCenterType = "202507261828";
//现场指挥部
const fieldCommandType = "202507261830";
//响应指令
const responseDirectiveType = "202507261048";

export {
  userClassDictionaryId,
  userPositionDictionaryId,
  textMessageTemplateType,
  inspectionDictionaryType,
  inspectionResultType,
  infoEventDictionaryType,
  workNoticeType,
  equipmentType,
  applicationArea,
  combatReadinessStatus,
  configurationMethodId,
  teamLevelType,
  placeGradingType,
  resourceType,
  spaceType,
  allFunction,
  refugeTime,
  alertLevel,
  eventType,
  eventLevel,
  infoSyncType,
  commandCenterType,
  fieldCommandType,
  responseDirectiveType,
};

/**
 * 获取字典铺底数据公共方法
 * systemDictionaryId 为字典主表的ID
 * @param {*} systemDictionaryId
 */
export function getItemList(systemDictionaryId) {
  return new Promise((resolve, reject) => {
    systemManagementApi
      .queryItemListById({
        systemDictionaryId,
      })
      .then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) {
          // 处理错误情况
          reject(new Error(message || error));
        } else {
          // 成功时返回数据
          resolve(data);
        }
      })
      .catch((err) => {
        // 处理请求异常
        reject(err);
      });
  });
}

/**
 * 获取行政区划数据公共方法
 * params {} 为产生对象
 * @param params
 */
export function getTownsBeijingList(params = {}) {
  return new Promise((resolve, reject) => {
    systemManagementApi
      .queryTownsBeijingList(params)
      .then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) {
          // 处理错误情况
          reject(new Error(message || error));
        } else {
          // 成功时返回数据
          resolve(data);
        }
      })
      .catch((err) => {
        // 处理请求异常
        reject(err);
      });
  });
}
