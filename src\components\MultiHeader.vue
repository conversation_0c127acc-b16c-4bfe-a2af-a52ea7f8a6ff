<template>
  <div class="dynamic-table-index">
    <el-table
      v-loading="loading"
      v-if="tableType === 1"
      :data="tableData"
      border
      style="width: 100%;"
      :max-height="tableHeight"
      :cell-class-name="cellClassName"
      @cell-click="handleCellClick"
    >
      <el-table-column
        :fixed="col.id === 'dateTime'"
        v-for="(col, index) in columns"
        :key="col.id || index"
        :label="col.positionName"
        :align="col.align || 'center'"
        :prop="col.id"
        :min-width= "col.id === 'dateTime' ? 140 : col.num * 70"
      >
        <template slot-scope="{ row, column, $index }">
          <div
            class="custom-column-div"
            v-if="col.num > 0"
          >
            <template v-for="(item, numIndex) in col.num">
              <button
                v-if="!isContainUser(row, col, numIndex)"
                :style="{
                  height: '40px',
                  width:  '70px !important',
                  flex: 'none',
                  backgroundColor: 'transparent',
                  borderLeft: numIndex === 0 ? 'none' : '1px solid #EBEEF5',
                }" 
                @click="handleClickAddUser(row, col, numIndex)">
                {{  }}
              </button>
              <el-popover
                v-if="isContainUser(row, col, numIndex)"
                placement="bottom"
                :width="290"
                trigger="click"
                :disabled="(col.id === 'dateTime' || beforeClickModel) ? true : false"
              >
                <!-- <div
                  :style="{ width: col.num > 1 ? `70px` : '100%', 
                    background: isBeforeCell(row, col, numIndex) ? 'antiquewhite' : 
                                isAfterCell(row, col, numIndex) ? 'cyan' : '#ffffff'
                  }"
                  slot="reference"
                > -->
                  <!-- {{ getTableUserName(row, col, numIndex) }} -->
                  <!-- {{ row[col.id][index]?.userName }} -->
                <!-- </div> -->

                <button
                  v-if="isContainUser(row, col, numIndex)"
                  :style="{
                    width: (col.id != 'dateTime' && col.num > 0) ? `70px` : '140px',
                    backgroundColor: isBeforeCell(row, col, numIndex) ? 'antiquewhite' : 'transparent',
                    borderLeft: numIndex === 0 ? 'none' : '1px solid #EBEEF5',
                  }" 
                  slot="reference" @click="handleCellBtnClick(row, col, numIndex)">
                  {{ getTableUserName(row, col, numIndex) }}
                </button>
                <div class="popover-content">
                  <el-button type="primary" size="small" @click="handleButtonClick(row, col, numIndex, 1)" > 替班 </el-button>
                  <el-button type="primary" size="small" @click="handleButtonClick(row, col, numIndex, 2)" > 换班 </el-button>
                  <el-button type="primary" size="small" @click="handleButtonClick(row, col, numIndex, 3)" > 修改 </el-button>
                  <el-button type="primary" size="small" @click="handleButtonClick(row, col, numIndex, 4)" > 删除 </el-button>
                </div>
              </el-popover>
            </template>
          </div>
          <!-- <template v-else>
            <template v-if="row[col.id]">
              <el-popover
                  placement="bottom"
                  :width="`${col.num * 70}`"
                  trigger="click"
              >
                <div slot="reference">
                  {{ row[col.id] }}
                </div>
                <div class="popover-content">姓名：{{ row[col.id] }}</div>
              </el-popover>
            </template>
            <template v-else> - </template>
          </template> -->
        </template>

        <el-table-column
          v-for="(subCol, subIndex) in col.children"
          :key="subCol.id || subIndex"
          :label="subCol.positionName"
          :align="subCol.align || 'center'"
          :prop="subCol.id"
          :min-width="subCol.num * 70"
        >
          <template slot-scope="{ row, column, $index }">
            <div
              class="custom-column-div"
              v-if="subCol.num > 0"
            >
              <template v-for="(item, subNumIndex) in subCol.num">
                <button
                  v-if="!isContainUser(row, subCol, subNumIndex)"
                  :style="{
                    height: '40px',
                    width:  '70px !important',
                    flex: 'none',
                    backgroundColor: 'transparent',
                    borderLeft: subNumIndex === 0 ? 'none' : '1px solid #EBEEF5',
                  }" 
                  @click="handleClickAddUser(row, subCol, subNumIndex)">
                  {{  }}
                </button>
                <el-popover
                  v-if="isContainUser(row, subCol, subNumIndex)"
                  placement="bottom"
                  :width="290"
                  trigger="click"
                  :disabled="beforeClickModel ? true : false"
                >
                  <!-- <div
                    :style="{ width: subCol.num > 1 ? `70px` : '100%'}"
                    slot="reference"
                  >
                    {{ getTableUserName(row, subCol, subNumIndex) }}
                  </div> -->
                  <button 
                    v-if="isContainUser(row, subCol, subNumIndex)"
                    :style="{
                      width: `70px`,
                      backgroundColor: isBeforeCell(row, subCol, subNumIndex) ? 'antiquewhite' : 'transparent',
                      borderLeft: subNumIndex === 0 ? 'none' : '1px solid #EBEEF5',
                    }" 
                    slot="reference" @click="handleCellBtnClick(row, subCol, subNumIndex)">
                    {{ getTableUserName(row, subCol, subNumIndex) }}
                  </button>
                  <div class="popover-content">
                    <el-button type="primary" size="small" @click="handleButtonClick(row, subCol, subNumIndex, 1)" > 替班 </el-button>
                    <el-button type="primary" size="small" @click="handleButtonClick(row, subCol, subNumIndex, 2)" > 换班 </el-button>
                    <el-button type="primary" size="small" @click="handleButtonClick(row, subCol, subNumIndex, 3)" > 修改 </el-button>
                    <el-button type="primary" size="small" @click="handleButtonClick(row, subCol, subNumIndex, 4)" > 删除 </el-button>
                  </div>
                  <!-- <div v-if="!isContainUser(row, subCol, subNumIndex)">
                    <el-cascader :options="options" :show-all-levels="false"></el-cascader>
                  </div> -->
                </el-popover>
              </template>
            </div>
            <!-- <template v-else>
              <template v-if="row[subCol.id]">
                <el-popover
                    placement="bottom"
                    :width="`${col.num * 70}`"
                    trigger="click"
                >
                  <div slot="reference">
                    {{ row[subCol.id] }}
                  </div>
                  <div class="popover-content">姓名：{{ row[subCol.id] }}</div>
                </el-popover>
              </template>
              <template v-else> - </template>
            </template> -->
          </template>

          <el-table-column
            v-for="(subSubCol, subSubIndex) in subCol.children"
            :key="subSubCol.id || subSubIndex"
            :label="subSubCol.positionName"
            :align="subSubCol.align || 'center'"
            :prop="subSubCol.id"
            :min-width="subSubCol.num * 70"
          >
            <template slot-scope="{ row, column, $index }">
              <div
                class="custom-column-div"
                v-if="subSubCol.num > 0"
              >
                <template v-for="(item, subsubNumIndex) in subSubCol.num">
                  <button
                    v-if="!isContainUser(row, subSubCol, subsubNumIndex)"
                    :style="{
                      height: '40px',
                      width:  '70px !important',
                      flex: 'none',
                      backgroundColor: 'transparent',
                      borderLeft: subsubNumIndex === 0 ? 'none' : '1px solid #EBEEF5',
                    }" 
                    @click="handleClickAddUser(row, subSubCol, subsubNumIndex)">
                    {{  }}
                  </button>
                  <el-popover
                    v-if="isContainUser(row, subSubCol, subsubNumIndex)"
                    placement="bottom"
                    :width="290"
                    trigger="click"
                    :disabled="beforeClickModel ? true : false"
                    >
                    <!-- <div 
                      :style="{width: subSubCol.num > 1 ? `70px` : '100%'}"
                      slot="reference">
                      {{ getTableUserName(row, subSubCol, subsubNumIndex) }}
                    </div> -->
                    <el-button 
                      v-if="isContainUser(row, subSubCol, subsubNumIndex)"
                      :style="{
                        width: '70px',
                        backgroundColor: isBeforeCell(row, subSubCol, subsubNumIndex) ? 'antiquewhite' : 'transparent',
                        borderLeft: subsubNumIndex === 0 ? 'none' : '1px solid #EBEEF5',
                      }" 
                      slot="reference" @click="handleCellBtnClick(row, subSubCol, subsubNumIndex)">
                      {{ getTableUserName(row, subSubCol, subsubNumIndex) }}
                    </el-button>
                    <div class="popover-content">
                      <el-button type="primary" size="small" @click="handleButtonClick(row, subSubCol, subsubNumIndex, 1)" > 替班 </el-button>
                      <el-button type="primary" size="small" @click="handleButtonClick(row, subSubCol, subsubNumIndex, 2)" > 换班 </el-button>
                      <el-button type="primary" size="small" @click="handleButtonClick(row, subSubCol, subsubNumIndex, 3)" > 修改 </el-button>
                      <el-button type="primary" size="small" @click="handleButtonClick(row, subSubCol, subsubNumIndex, 4)" > 删除 </el-button>
                    </div>
                  </el-popover>
                </template>
              </div>
              <!-- <template v-else>
                <template v-if="row[subSubCol.id]">
                  <el-popover
                      placement="bottom"
                      :width="`${col.num * 70}`"
                      trigger="click"
                  >
                    <div slot="reference">
                      {{ row[subSubCol.id] }}
                    </div>
                    <div class="popover-content">
                      姓名：{{ row[subSubCol.id] }}
                    </div>
                  </el-popover>
                </template>
                <template v-else> - </template>
              </template> -->
            </template>
          </el-table-column>
        </el-table-column>
      </el-table-column>
    </el-table>

    <!-- 其他部门的值班表 -->
    <portal-table
      v-if="tableType === 2"
      :isTableBorder="true"
      :tableHeight="570"
      :showIndex="false"
      :showAddButton="false"
      :columns="columns"
      :show-pagination="false"
      :showSelection=false
      :table-data="otherTableData"
      row-key="id"
    />
    
    <!-- 表尾备注 -->
    <div class="table-footer">
      <div
        class="table-column"
        :style="{
          gridTemplateColumns: '50px',
        }"
      >
        <div class="table-header">备注</div>
      </div>

      <div
        class="table-column"
        v-for="(item, index) in tableFooterData"
        :key="index"
      >
        <div style="width: 80px;" class="table-header">{{ item.name }}</div>
        <div :style="{ width: item.name === '预警' ? '230px' : 'auto' }" class="table-cell">
          <div class="contact-grid">
            <div
              class="contact-item"
              v-for="(contact, cIndex) in item.contacts"
              :key="cIndex"
              v-html="
                contact.type
                  ? `${contact.type}: ${contact.value}`
                  : contact.value
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

import PortalTable from "./PortalTable/index.vue";


export default {
  name: "DynamicTable",
  components: {PortalTable},
  props: {
    tableType: {
      type: Number,
      required: 0,
    },
    columns: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    otherTableData: {
      type: Array,
      required: true,
    },
    tableFooterData: {
      type: Array,
      default: () => [
        {
          name: "市委、市政府总机",
          contacts: [
            { type: "电话", value: "55569696" },
            { type: "内线", value: "114" },
          ],
        },
        {
          name: "市应急指挥中心",
          contacts: [
            { type: "电话", value: "55573000" },
            { type: "传真", value: "55573045" },
          ],
        },
        {
          name: "预警",
          contacts: [
            {
              type: "电话",
              value:
                "68400695、13811875891<br>&nbsp;&nbsp;&nbsp;&nbsp; 13240212379、13240212379",
            },
            { type: "传真", value: "15321212379" },
          ],
        },
        {
          name: "城区应急办公点",
          contacts: [
            { type: "电话", value: "55589555" },
            { type: "传真", value: "55589255" },
          ],
        },
        {
          name: "高危监测",
          contacts: [
            { type: "电话", value: "55573948" },
            { type: "传真", value: "55573485" },
          ],
        },
        {
          name: "司机",
          contacts: [
            { type: "电话", value: "55579988" },
            { value: "" }, // 空值项
          ],
        },
      ],
    },
  },
  data() {
    return {
      tableHeight: 600,
      loading: true,
      componentKey: 0,
      value: "",
      isHaveNameBtn: false,
      currentMsgTip: null,
      currentClickCell: null,
      beforePath:'',
      beforeClickModel: null,
      afterClickModel: null,
      // options: [
      //   { value: "option1", label: "Option 1" },
      //   { value: "option2", label: "Option 2" },
      //   { value: "option3", label: "Option 3" },
      // ],
    };
  },
  mounted() {
    // setTimeout(() => {
    //   this.setSpanHeight();
    // }, 1000);
  },
  computed: {
    isBeforeCell() {
      return (row, col, index) => {
        const colData = this.getTableUserModel(row, col, index)
        if (col.id != 'dateTime' && colData && colData.id === this.beforeClickModel?.id) {
          return true;
        }
        return false;
      }
    },
    // isAfterCell() {
    //   return (row, col, index) => {
    //     const colData = this.getTableUserModel(row, col, index)
    //     if (col.id != 'dateTime' && colData && colData.id === this.afterClickModel?.id) {
    //       return true;
    //     }
    //     return false;
    //   }
    // }
  },
  watch: {
    tableData(value) {
      if (value) {
        this.loading = false;
      }
    }
  },
  methods: {
    resetHandleData() {
      this.currentMsgTip = null;
      this.beforePath = '';
      this.beforeClickModel = null;
      this.afterClickModel = null;
    },
    getTableUserModel(row, col, index) {
      if (row && col.id && row[col.id]) {
        const colList = row[col.id];
        if (colList.length > index) {
          const colData = colList[index];
          if (colData.userName) {
            return colData;
          }
        }
      }
    },
    
    getTableUserName(row, col, index) {
      const colData = this.getTableUserModel(row, col, index);
      if (colData) {
        return colData.userName;
      }
    },
    isContainUser(row, col, index) {
      if (row && col.id && row[col.id]) {
        const colList = row[col.id];
        if (colList.length > index) {
          const colData = colList[index];
          if (colData.userName) {
            return true;
          }
        }
      }
      return false;
    },
    setSpanHeight() {
      this.$nextTick(() => {
        const nameColumnListCells = document.querySelectorAll(
            ".el-table__cell.name-column-list"
        );
        nameColumnListCells.forEach((cell) => {
          const cellHeight = cell.offsetHeight;
          const customDivs = cell.querySelectorAll(".custom-column-div");
          customDivs.forEach((div) => {
            const spans = div.querySelectorAll("span");
            spans.forEach((span) => {
              span.style.height = `${cellHeight}px`;
              span.style.lineHeight = `${cellHeight}px`;
            });
          });
        });
      });
    },

    //为单元格添加自定义class
    cellClassName({ row, column, rowIndex, columnIndex }) {
      // console.log('----------------------',row, column);
      
      // let rowValue = row[column.property];
      // if (rowValue) {
      if (column.minWidth/70 > 1) {
        return "name-column-list";
      }
      // }
    },

    handleClickAddUser(row, column) {
      console.log('没有名字空button的点击事件',row, column);
      if (this.beforeClickModel) {
        this.currentMsgTip.close();
        this.$message({
          showClose: true,
          message: '提示：当前点击的表格内没有值班人员，请重新选择！',
          type: 'warning',
        });
      } else {
        let clickPath = column.positionName;
        if (column.parentId != '0') {
          clickPath = this.findDepartmentPath(this.columns, column.id);
        }
        const params = {
          path: clickPath,
          rowData: row,                       // 被操作人的这一行数据
          colData: column,                    // 被操作人的这一列的表头数据
          type: 0,                            // 操作类型 （1=替班、2=换班 3=修改、4=删除）
        }
        this.$emit("handle-click-cell", params);
      }
    },

    handleCellClick(row, column, cell, event) {
      // console.log('el-table的cell点击事件',row, column, cell, event,this.afterClickModel);
      // if (!this.isHaveNameBtn) {
      //   if (this.beforeClickModel) {
      //     this.currentMsgTip.close();
      //     this.$message({
      //       showClose: true,
      //       message: '提示：当前点击的表格内没有值班人员，请重新选择！',
      //       type: 'warning',
      //     });
      //   } else {
      //     const path = this.findDepartmentPath(this.columns, column.property);
      //     // this.$emit("handle-click-cell", row, column, 0);
      //     const params = {
      //       path: path,
      //       rowData: row,                       // 被操作人的这一行数据
      //       colData: column,                    // 被操作人的这一列的表头数据
      //       type: 0,                            // 操作类型 （1=替班、2=换班 3=修改、4=删除）
      //     }
      //     this.$emit("handle-click-cell", params);
      //   }
      // }
      // this.isHaveNameBtn = false;
    },

    // 处理单元格点击事件
    handleCellBtnClick(row, col, index) {
      console.log("存在名字触发气泡的按钮事件-------", row, col, index);
      this.isHaveNameBtn = true;
      this.currentClickCell = this.getTableUserModel(row, col, index);
      // 如果之前点击了换班操作，则下次点击时，直接执行换班流程
      if (this.beforeClickModel) {
        this.currentMsgTip.close();
        // 如果换班的时候点击的是没有名字的单元格，则给出提示
        if (!this.currentClickCell || !this.currentClickCell.id) {
          this.currentMsgTip = this.$message({
            showClose: true,
            message: '提示：当前点击的表格内没有值班人员，请重新选择！',
            type: 'warning',
          });
          return;
        }
        // 如果点击换班后再次点击的是自己，则取消换班流程
        if (this.currentClickCell.id === this.beforeClickModel.id) {
          this.beforeClickModel = null;
          this.afterClickModel = null;
          return;
        }
        this.afterClickModel = this.currentClickCell;
        // this.$emit("handle-click-cell", row, column, 2);
        const params = {
          path: this.beforePath,
          arrangement: this.beforeClickModel, // 被操作人
          arrangementReplace: this.afterClickModel,  // 选中的换班人
          rowData: row,                       // 被操作人的这一行数据
          colData: col,                       // 被操作人的这一列的表头数据
          type: 2,                            // 操作类型 （1=替班、2=换班 3=修改、4=删除）
        }
        this.$emit("handle-click-cell", params);
        return;
      }
    },

    // 替班换班修改删除点击事件
    handleButtonClick(row, col, index, type) {
      console.log('气泡内替班换班按钮的操作事件',row, col, type);
      // 获取当前的完整表头层级
      this.beforePath = col.positionName;
      if (col.parentId != '0') {
        this.beforePath = this.findDepartmentPath(this.columns, col.id);
      }
      
      // // 如果之前点击了换班操作，则下次点击时，无论什么类型都是换班流程
      // if (this.beforeClickModel) {
      //   this.afterClickModel = this.getTableUserModel(row, col, index);
      //   console.log('2222222222222',row, col, type,this.afterClickModel);
      //   this.$message.close();
      //   return;
      // }
      // 如果是点击换班，则给出提示，直接在表格内选择换班人员
      if (type === 2) {
        this.beforeClickModel = this.getTableUserModel(row, col, index);
        this.currentMsgTip = this.$message({
          showClose: true,
          message: '提示：请点击表格内的某个值班人员进行换班！',
          type: 'warning',
          duration : 0
        });
        return;
      }
      // if (row[col.id]) {
        const params = {
          path: this.beforePath,
          arrangement: this.currentClickCell, // 被操作人
          rowData: row,                       // 被操作人的这一行数据
          colData: col,                       // 被操作人的这一列的表头数据
          type: type,                         // 操作类型 （1=替班、2=换班 3=修改、4=删除）
        }
        this.$emit("handle-click-cell", params);
      // }
    },

    // 获取当前点击的单元格所属的完整表头数据
    findDepartmentPath(departments, targetId, path = []) {
      for (const dept of departments) {
        // 创建当前路径副本
        const currentPath = [...path, dept.positionName];
        
        // 如果找到目标ID，返回当前路径
        if (dept.id === targetId) {
          return currentPath.join('/');
        }
        
        // 如果有子部门，递归查找
        if (dept.children && dept.children.length) {
          const foundPath = this.findDepartmentPath(dept.children, targetId, currentPath);
          if (foundPath) return foundPath;
        }
      }
      
      return null;
    },
  },
};
</script>

<style lang="scss" scoped>
.el-table ::v-deep th {
  /* background-color: #cfe2ff; */
  background: var(--lightGray3) !important;
  text-align: center;
  color: var(--black) !important;
}

.el-table ::v-deep .el-table__cell {
  padding: 0 !important;
  /* height: 40px;
  line-height: 40px; */
}

.el-table ::v-deep td.el-table__cell {
  padding: 0 !important;
  height: 40px;
  line-height: 40px;
}

.el-table {
  ::v-deep{
    td.el-table__cell {
      .cell {
        /* white-space: pre-wrap; */
        /* height: 100%; */
        padding: 0 !important;
        .custom-column-div {
          display: flex !important;
          button { /* 修改 span 为 button，因为你的模板使用的是 button */
            padding: 0 !important;
            flex: 1; /* 关键：均等分配空间 */
            min-width: 0; /* 防止内容溢出影响分配 */
            /* height: 40px; */
            cursor: pointer;
            line-height: 40px;
            border-top: none;
            border-bottom: none;
            border-right: none;
            color: #606266;
            text-align: center; /* 文字居中 */
            border-radius: 0 !important; /* 移除圆角 */
            /* &:first-child {
              border-left: none;
            } */
          }
        }
      }
    } 
  } 
} 
  /* span {
    width: 100%;
    display: flex;
    line-height: 40px;
    justify-content: center;
    cursor: pointer;
  } */
/* } */

/*为 name-column-list 类的单元格添加样式*/
.el-table ::v-deep .name-column-list {
  padding: 0 !important;
}

.el-table ::v-deep .el-table__cell.name-column-list .cell {
  padding: 0 !important;
  .custom-column-div {
    display: flex !important;

    /* span {
      height: 40px;
      cursor: pointer;
      line-height: 40px;
      border-left: 1px solid #EBEEF5;

      &:first-child {
        border-left: none;
      }
    } */

    button { /* 修改 span 为 button，因为你的模板使用的是 button */
      padding: 0 !important;
      flex: 1; /* 关键：均等分配空间 */
      min-width: 0; /* 防止内容溢出影响分配 */
      /* height: 40px; */
      cursor: pointer;
      line-height: 40px;
      border-top: none;
      border-bottom: none;
      border-right: none;
      color: #606266;
      text-align: center; /* 文字居中 */
      border-radius: 0 !important; /* 移除圆角 */
      /* &:first-child {
        border-left: none;
      } */
    }
  }
}

.table-footer {
  display: grid;
  grid-template-columns: 50px repeat(6, 1fr); /* 6个机构横向排列 */
  border-collapse: collapse;
  width: 100%;
  overflow-x: auto; /* 支持水平滚动 */
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
  border-bottom: 1px solid var(--table-border);

  .table-column {
    display: grid;
    grid-template-columns: auto 1fr; /* 机构名称行 + 联系方式行 */

    .table-header {
      /* padding: 8px; */
      text-align: center;
      font-weight: bold;
      /* color: #314dc3 !important; */
      color: var(--black) !important;
      border-right: 1px solid #ebeef5;
      background: var(--lightGray3) !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .table-cell {

      text-align: left;

      .contact-grid {
        display: grid;
        height: 100%;
        grid-template-columns: 1fr;
        gap: 5px;

        .contact-item {
          display: flex;
          align-items: center;
          padding: 5px 5px;
          border-bottom: 1px solid #eee;
        }

        .contact-item:last-child {
          border-bottom: none;
        }
      }
    }
  }
}

</style>
