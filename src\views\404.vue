<template>
  <div class="error-page">
    <!-- 404 - Page -->
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <i class="el-icon-warning-outline"></i>
        </div>
        <h1 class="error-title">页面未找到</h1>
        <p class="error-message">抱歉，您访问的页面不存在或已被移除</p>
        <div class="error-details">
          <p>
            请求的路径：<code>{{ $route.fullPath }}</code>
          </p>
          <p>可能的原因：</p>
          <ul>
            <li>URL地址输入错误</li>
            <li>页面已被删除或移动</li>
            <li>您没有访问权限</li>
          </ul>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <i class="el-icon-house"></i>
            返回首页
          </el-button>
          <el-button @click="goLogin">
            <i class="el-icon-user"></i>
            重新登录
          </el-button>
          <el-button @click="goBack">
            <i class="el-icon-back"></i>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NotFound",
  methods: {
    goHome() {
      this.$router.push("/");
    },
    goLogin() {
      this.$router.push("/login");
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  font-size: 80px;
  color: #f56c6c;
  margin-bottom: 20px;
}

.error-title {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  font-weight: 600;
}

.error-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.6;
}

.error-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: left;
}

.error-details p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.error-details code {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
}

.error-details ul {
  margin: 10px 0 0 20px;
  color: #909399;
}

.error-details li {
  margin-bottom: 5px;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .el-button {
  min-width: 120px;
}

@media (max-width: 768px) {
  .error-container {
    padding: 40px 20px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-icon {
    font-size: 60px;
  }

  .error-actions {
    flex-direction: column;
  }

  .error-actions .el-button {
    width: 100%;
  }
}
</style>
