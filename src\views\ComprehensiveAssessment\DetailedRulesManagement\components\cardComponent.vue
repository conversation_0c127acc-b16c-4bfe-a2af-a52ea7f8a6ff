<template>
  <div class="card-component">
    <el-row :gutter="10">
      <el-col
        :span="24 / pieceList.length"
        v-for="(item, index) in pieceList"
        :key="index"
      >
        <el-card shadow="hover">
          <div class="card-item">
            <div class="card-content">
              <div class="title">{{ item.title }}</div>
              <div class="text">{{ item.text }}</div>
              <div
                class="status"
                :class="{
                  positive: item.status > 0,
                  negative: item.status < 0,
                  zero: item.status == 0,
                }"
              >
                <i class="el-icon-top" v-if="item.status > 0"></i>
                <i class="el-icon-bottom" v-else-if="item.status < 0"></i>
                {{ formatStatus(item.status) }}
              </div>
            </div>
            <div class="card-img">
              <el-image
                :src="require(`@/assets/images/card_img${index + 1}.png`)"
                alt=""
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "cardComponent",
  props: {
    pieceList: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    formatStatus(status) {
      const space = "\u00A0".repeat(5);
      if (status > 0) {
        return `较上月${space}+${status}%`;
      } else if (status < 0) {
        return `较上月${space}${status}%`;
      } else {
        return "与上月持平";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card-component {
  margin: 0;
  padding: 0;
  width: 100%;
  .card-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .card-content {
      display: flex;
      flex-direction: column;
      gap: 10px;
      .title {
        font-size: 14px;
        font-weight: 500;
      }
      .text {
        font-size: 30px;
        font-weight: bold;
      }
      .status {
        font-size: 14px;
        font-weight: 500;
        &.positive {
          color: #67c23a; /* 正数绿色 */
        }
        &.negative {
          color: #f56c6c; /* 负数红色 */
        }
        &.zero {
          color: #909399; /* 零灰色 */
        }
      }
    }
    .card-img {
      width: 50px;
      height: 50px;
    }
  }
}
</style>