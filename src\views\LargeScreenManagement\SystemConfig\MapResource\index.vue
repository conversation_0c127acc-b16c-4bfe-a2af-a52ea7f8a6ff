<template>
  <div class="user-index-container">
    <portal-table
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="资源分类名称" prop="name">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 430px;" 
            v-model="form.name" 
            :rows="2" />
        </el-form-item>

        <el-form-item label="绑定资源" prop="sourceList">
          <el-select 
            :disabled="styleType === 3" 
            v-model="form.sourceList" 
            multiple 
            placeholder="请选择需要绑定的地图资源" 
            style="width: 430px;">
            <el-option
              v-for="item in mapSourceList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="排序" prop="sort">
          <el-input
            :disabled="styleType === 3"
            style="width: 430px;" 
            v-model="form.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="number"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="资源描述" prop="description">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 430px;" 
            v-model="form.description" 
            placeholder="请输入" 
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5}" />
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, systemConfigApi } from "@/api";
import { getItemList, infoEventDictionaryType } from "@/utils/dictionary";

import { conversionDateNotSecond } from "@/utils/publicMethod";


export default { 
  name: "MapResource",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          label: "资源分类名称",
          prop: "name",
          type: "input",
          placeholder: "请输入",
          width: "200",
        },
      ],
      columns: [
        { prop: "name", label: "资源类型名称", text: true },
        { prop: "sourceList", label: "绑定资源", text: true },
        { prop: "sort", label: "排序", text: true },
        // { prop: "infoType", label: "事件类型", text: true },
        // { prop: "infoStatus", label: "事件状态", text: true },
        // { prop: "status", label: "状态", text: true },
        // { prop: "recordMessage", label: "记录信息", text: true },
        // {
        //   prop: 'attentionStatus',
        //   label: '是否关注',
        //   switch: true,
        //   activeValue: 1,
        //   inactiveValue: 0,
        // },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '230px',
          operationList: [
            {
              label: '查看',
              permission: 'mapResource:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'mapResource:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'mapResource:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：编辑，2：查看

      mapSourceList: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增地图资源分类",

      form: {
        name: "",
        sourceList: [],
        sort: "",
        description: ""
      },
      rules: {
        name: [
          {required: true, message: '资源分类名称不能为空', trigger: 'blur'}
        ],
        // inspectionType: [
        //   {required: true, message: '检查方式不能为空', trigger: 'blur'}
        // ],
        // inspectionUnit: [
        //   {required: true, message: '被检查单位不能为空', trigger: 'blur'}
        // ],
        // inspectionJob: [
        //   {required: true, message: '被检查岗位不能为空', trigger: 'blur'}
        // ],
        // inspectionUser: [
        //   {required: true, message: '被检查人员不能为空', trigger: 'blur'}
        // ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    // handleChangeMonth(value) {
    //   this.form.inspectionTime = conversionDateNotSecond(value);
    // },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await systemConfigApi.querySourceList();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.mapSourceList = data;
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionTypeList = await getItemList(infoEventDictionaryType);
      //   this.searchItems[2].options = this.inspectionTypeList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    //查看详情
    getRowDataInfo(row) {
      systemConfigApi.queryResClassInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.form = data;
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.resetFormData();
      this.generalDialogTitle = "新增地图资源分类";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑地图资源分类";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看地图资源分类";
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await systemConfigApi.queryResClassPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      // if (row.inspectionTime && row.inspectionTime.length > 0) {
      //   row.startTime = conversionDateNotSecond(row.inspectionTime[0])
      //   row.endTime = conversionDateNotSecond(row.inspectionTime[1])
      //   delete row.inspectionTime
      // }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await systemConfigApi.createResClass(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else if (this.styleType === 2) {
            const res = await systemConfigApi.updateResClass(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            this.dialogVisible = false;
            this.resetFormData();
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.resetFormData();
    },

    //删除
    handleDelete(row) {
      let txt = "此操作将永久删除该条数据, 是否继续?";
      this.$confirm(txt, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        systemConfigApi.deleteResClass({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success("删除成功");
          this.getTableDataList();
        });
      });
    },

    resetFormData() {
      this.form = {
        name: "",
        sourceList: [],
        sort: "",
        description: ""
      };
    },
  },

};
</script>

<style lang="scss" scoped>
.portal-table {
  padding: 20px !important;
  ::v-deep {
    .cell {
      white-space: normal !important;
    }
  }
}

.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
