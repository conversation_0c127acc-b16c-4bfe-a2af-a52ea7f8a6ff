<template>
  <div class="duty-evaluation-statistics-container">
    <div v-show="dutyVisible">
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <el-form
              :inline="true"
              :model="searchData"
              class="demo-form-inline"
            >
              <el-form-item label="关键字">
                <el-input
                  v-model="searchData.orgName"
                  placeholder="请输入关键字"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="onSubmit"
                  >高级筛选</el-button
                >
                <el-button
                  type="primary"
                  icon="el-icon-refresh"
                  @click="resetSubmit"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <div class="Statistics">
              <div
                class="Statistics-item"
                v-for="(item, index) in StatisticsData"
                :key="item.title"
              >
                <div
                  class="vertical-line"
                  :class="`bg-color-${index % 4}`"
                ></div>
                <div class="content">
                  <div class="image">
                    <el-image
                      :src="
                        require(`@/assets/images/statistics_${index + 1}.png`)
                      "
                    />
                  </div>
                  <div class="content-text">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="data">
                      <div class="number">{{ item.data }}</div>
                      <div class="unit">条</div>
                    </div>
                    <div
                      class="percentage"
                      :style="{ color: getPercentageColor(item.percentage) }"
                    >
                      {{ item.percentage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">评价完成情况</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <echarts-component :options="completionOptions" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">评价类型分布</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <div class="left">
                <echarts-component :options="typeOptions" />
              </div>
              <div class="right">
                <div class="percentage">
                  <div
                    class="percentage-item"
                    v-for="item in percentageData"
                    :key="item.title"
                  >
                    <div class="percentage-text">
                      <div class="title">{{ item.title }}</div>
                      <div class="number">{{ item.number }}%</div>
                    </div>
                    <el-progress
                      :show-text="false"
                      :stroke-width="10"
                      :percentage="item.number"
                      :color="item.color"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <div class="table-title">评价记录列表</div>
            <div class="table-container">
              <PortalTable
                :tableHeight="350"
                :columns="columns"
                :table-data="tableData"
                :showSelection="false"
                row-key="code"
                :pagination="pagination"
                :showAddButton="false"
                @handle-size-change="handleSizeChange"
                @handle-current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <public-dialog ref="publicDialogRef" @dutyShow="dutyShow" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import echartsComponent from "@/components/echarts.vue";
import PublicDialog from "@/views/ComprehensiveAssessment/ComprehensiveEvaluationManagement/components/publicDialog.vue";
import * as echarts from "echarts/core";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  name: "DutyEvaluationStatistics",
  components: {
    PortalTable,
    echartsComponent,
    PublicDialog,
  },
  data() {
    return {
      dutyVisible: true,
      searchData: {
        orgName: "",
      },
      StatisticsData: [
        {
          title: "年度评价统计",
          data: "248",
          percentage: "12%",
        },
        {
          title: "季度评价统计",
          data: "248",
          percentage: "-12%",
        },
        {
          title: "月度评价统计",
          data: "248",
          percentage: "12%",
        },
        {
          title: "周度评价统计",
          data: "248",
          percentage: "12%",
        },
      ],
      completionOptions: {
        // 修改为两种颜色：绿色(#80FFA5)和灰色(#CCCCCC)
        color: ["#80FFA5", "#CCCCCC"],
        title: {
          text: "", // 清空标题（根据原图无标题）
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        // 修改图例为两个数据系列
        legend: {
          data: ["自评完成", "系统评价"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            // 修改为12个月份
            data: [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            // 坐标轴线样式
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#333", // 深色坐标轴
              },
            },
            // 坐标轴标签
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            // 禁用网格线（无网格线）
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          // 系统评价（灰色系列）
          {
            name: "系统评价",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#CCCCCC" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [5, 1, 6, 5, 6, 17, 5, 8, 5, 2, 9, 6],
          },
          // 自评完成（绿色系列）
          {
            name: "自评完成",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(141, 204, 171, 0.8)" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [0, 5, 15, 20, 45, 20, 10, 8, 12, 15, 10, 0], // 模拟峰值数据
          },
        ],
      },
      typeOptions: {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {d}%",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542"],
        series: [
          {
            name: "评价分布",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 40, name: "系统评价" },
              { value: 35, name: "自评" },
              { value: 25, name: "待审核" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "84.5",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "平均分",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData: [
        {
          title: "系统评价",
          number: 40,
          color: "#fb788a",
        },
        {
          title: "自评",
          number: 35,
          color: "#52c1f5",
        },
        {
          title: "待审核",
          number: 25,
          color: "#ffc542",
        },
      ],
      columns: [
        { text: true, prop: "code", label: "评价ID" },
        { text: true, prop: "createTime", label: "评价时间" },
        { text: true, prop: "orgName", label: "单位名称" },
        { text: true, prop: "totalFinalScore", label: "综合得分" },
        { text: true, prop: "statusFormat", label: "评价状态" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "详情",
              permission: "dutyEvaluationStatistics:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          id: "20250625-001",
          date: "2025-06-25",
          region: "东城区",
          department: "应急管理局",
          type: "系统评价",
          score: 97.2,
          status: "已通过",
          action: "详情",
        },
        {
          id: "20250625-002",
          date: "2025-06-24",
          region: "海淀区",
          department: "公安局",
          type: "自评",
          score: 95.8,
          status: "待审核",
          action: "详情",
        },
        {
          id: "20250625-003",
          date: "2025-06-23",
          region: "西城",
          department: "消防局",
          type: "系统评价",
          score: 94.8,
          status: "已完成",
          action: "详情",
        },
        {
          id: "20250625-004",
          date: "2025-06-22",
          region: "北京市",
          department: "市消防救援总队",
          type: "自评",
          score: 93.8,
          status: "已驳回",
          action: "详情",
        },
        {
          id: "20250625-005",
          date: "2025-06-21",
          region: "丰台区",
          department: "应急管理局",
          type: "系统评价",
          score: 92.8,
          status: "已完成",
          action: "详情",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
      statusList: [
        {
          label: "草稿",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "审核驳回",
          value: 2,
        },
        {
          label: "审核通过",
          value: 3,
        },
        {
          label: "待录入",
          value: 4,
        },
      ],
    };
  },
  methods: {
    // 值守统计评价，记录分页列表
    queryEvaluationRecordPage() {
      comprehensiveAssessmentApi
        .queryEvaluationRecordPage({
          count: this.pagination.pageSize,
          page: this.pagination.currentPage,
          ...this.searchData,
        })
        .then((res) => {
          this.tableData = res.data.items.map((item) => {
            item.statusFormat =
              this.statusList.find((status) => status.value === item.status)
                ?.label || "";
            return item;
          });
          this.pagination.total = res.data.total;
        });
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.queryEvaluationRecordPage();
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.queryEvaluationRecordPage();
    },
    handleSearch(params) {
      console.log(params);
    },
    getPercentageColor(percentage) {
      const value = parseFloat(percentage);
      if (value > 1) return "#67C23A";
      if (value < 1) return "#F56C6C";
      return "#999"; // 等于1时的颜色
    },

    handleDetail(row) {
      this.dutyVisible = false;
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "detail";
      this.$refs.publicDialogRef.title = "评价详情";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryEvaluationRecordPage();
      console.log("提交");
    },
    resetSubmit() {
      this.searchData = {
        orgName: "",
      };
      this.pagination.currentPage = 1;
      this.queryEvaluationRecordPage();
    },
    dutyShow() {
      this.dutyVisible = true;
    },
  },
  mounted() {
    this.queryEvaluationRecordPage();
  },
};
</script>

<style lang="scss" scoped>
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
  .Statistics {
    display: flex;
    gap: 30px;
    .Statistics-item {
      padding-left: 15px;
      height: 140px;
      display: flex;
      gap: 15px;
      .vertical-line {
        width: 6px;
        height: 120px;
      }
      .bg-color-0 {
        background-color: #007bff; // 蓝色
      }
      .bg-color-1 {
        background-color: #9f47ec; // 绿色
      }
      .bg-color-2 {
        background-color: #0ce63f; // 黄色
      }
      .bg-color-3 {
        background-color: #cc3b4e; // 红色
      }

      .content {
        height: 100px;
        display: flex;
        align-items: center;
        margin-top: 10px;
        gap: 20px;
        .image {
          width: 50px;
          height: 50px;
        }
        .content-text {
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-bottom: 1px solid #3b3a3a;
          width: 200px;
          .title {
            font-size: 15px;
            margin-bottom: 5px;
          }
          .data {
            display: flex;
            gap: 40px;
            align-items: flex-end;
            margin-bottom: 5px;
            .number {
              font-size: 20px;
              font-weight: bold;
              color: #007bff;
            }
            .unit {
              font-size: 10px;
              color: #999;
            }
          }
          .percentage {
            font-size: 15px;
            color: #999;
          }
        }
      }
    }
  }
  .chart-title,
  .table-title {
    font-size: 20px;
    font-weight: bold;
  }
  .chart-container {
    height: 300px;
    display: flex;
    gap: 20px;
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 15%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .table-container {
    height: 400px;
    margin-top: 30px;
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>