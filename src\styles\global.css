/* 全局样式 */

/* ==================== 主题变量定义 ==================== */
:root {
  /* 顶部导航栏变量 */
  --nav-header-bg: linear-gradient(to right, #4569af, #507ac2);
  --nav-menu-active-bg: #36569a;
  --nav-menu-active-text: #ffffff;
  --breadcrumb-bg: #ffffff;
  --breadcrumb-text-normal: #5e5e5e;
  --breadcrumb-text-active: #ffffff;

  /* 主内容区域变量 */
  --main-bg: #eff3f7;
  --content-bg: #ffffff;
  --content-border-radius: 4px;
  --content-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 左侧树结构面板变量 */
  --tree-panel-border: none;
  --tree-panel-radius: 5px;
  --tree-panel-bg: #ffffff;
  --tree-node-hover-bg: rgba(161, 199, 255, 0.3);
  --tree-node-active-bg: linear-gradient(to right, #a1c7ff, #ffffff);
  --tree-node-active-text: #314dc3;
  --tree-node-active-border: 2px solid #314dc3;

  /* 搜索框变量 */
  --search-border: #dcdfe6;
  --search-radius: 4px;
  --search-bg: #ffffff;
  --search-icon-color: #c0c4cc;

  /* 表格区域变量 - 更新表头颜色 */
  --table-header-bg: #c4d5f6;
  --table-border: #c4d5f6;
  --table-row-odd: #ffffff;
  --table-row-even: #fafafa;
  --table-row-hover: rgba(68, 105, 175, 0.05);

  /* 分页控件变量 */
  --pagination-bg: #ffffff;
  --pagination-border: #dcdfe6;
  --pagination-active-bg: #36569a;
  --pagination-active-text: #ffffff;

  /* 按钮样式变量 - 更新主色调 */
  --btn-primary-bg: #4569af;
  --btn-primary-text: #ffffff;
  --btn-primary-radius: 4px;
  --btn-secondary-bg: #ffffff;
  --btn-secondary-border: 1px solid #4569af;
  --btn-secondary-text: #4569af;
  --btn-danger-bg: #f56c6c;
  --btn-danger-text: #ffffff;

  /* 图标颜色变量 */
  --icon-primary: #4569af;
  --icon-success: #67c23a;
  --icon-danger: #f56c6c;

  /* 文字颜色层级变量 */
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-auxiliary: #909399;
  --text-placeholder: #c0c4cc;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: auto;
}

/* 主体内容样式 */
#app {
  height: 100%;
  width: 100%;
  min-width: 1100px;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  cursor: pointer;
}

::-webkit-scrollbar-track {
  background: rgba(245, 247, 250, 0.5);
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.6);
  border-radius: 5px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.8);
  cursor: pointer;
}

/* Element UI Scrollbar 组件全局样式 */
.el-scrollbar__bar {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.el-scrollbar:hover .el-scrollbar__bar {
  opacity: 1;
}

.el-scrollbar__thumb {
  background-color: rgba(192, 196, 204, 0.6) !important;
  border-radius: 5px !important;
  transition: background-color 0.3s ease;
}

.el-scrollbar__thumb:hover {
  background-color: rgba(144, 147, 153, 0.8) !important;
}

.el-scrollbar__bar {
  border-radius: 5px !important;
}

.el-scrollbar__bar.is-vertical {
  width: 8px !important;
  border-radius: 5px !important;
}

.el-scrollbar__bar.is-horizontal {
  height: 8px !important;
  border-radius: 5px !important;
}

/* 隐藏树形组件的横向滚动条 */
.tree-scrollbar .el-scrollbar__bar.is-horizontal {
  display: none !important;
}

/* 原生滚动条优化（用于下拉框等） */
.el-select-dropdown__wrap::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-track {
  background: rgba(245, 247, 250, 0.3);
  border-radius: 5px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.5);
  border-radius: 5px;
  transition: background 0.3s ease;
  border: 1px solid transparent;
  background-clip: content-box;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.8);
}

/* 修复表格滚动内容区域 */
.el-table__body-wrapper {
  overflow-y: auto !important;
}

/* 表格滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  cursor: pointer;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: rgba(245, 247, 250, 0.5);
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.6);
  border-radius: 5px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  background-clip: content-box;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.8);
  cursor: pointer;
}

/* ==================== Element UI 组件样式覆盖 ==================== */

/* 表格样式 */
.el-table th {
  background: var(--table-header-bg) !important;
  color: #314dc3 !important;
  font-weight: 500;
  font-size: 13px;
  height: 45px !important;
  padding: 8px 0;
  border-bottom: 1px solid var(--table-border);
}

.el-table .el-table__header-wrapper th {
  background: var(--table-header-bg) !important;
  color: #314dc3 !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: var(--table-row-even);
}

.el-table tbody tr:hover > td {
  background-color: var(--table-row-hover) !important;
}

.el-table td,
.el-table th {
  border-bottom: 1px solid var(--table-border);
}

.el-table::before {
  background-color: var(--table-border);
}

.el-table--border::after,
.el-table--group::after {
  background-color: var(--table-border);
}

.el-table__body tr:hover > td {
  background-color: var(--table-row-hover) !important;
  transition: all 0.2s ease;
}

.el-table td {
  font-size: 13px;
  padding: 10px 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--table-border);
}

.el-table {
  border: 1px solid var(--table-border);
  border-radius: var(--content-border-radius);
  overflow: hidden;
}

.el-table::before,
.el-table::after {
  display: none;
}

.el-table__fixed-right::before,
.el-table__fixed::before {
  display: none;
}

/* 树形组件样式优化 */
.el-tree {
  color: var(--text-secondary);
  font-size: 14px;
  overflow-x: hidden;
}

.el-tree-node__content {
  height: 36px;
  border-radius: 4px;
  margin: 2px 0;
  transition: all 0.2s ease;
}

.el-tree-node__content:hover {
  background-color: var(--tree-node-hover-bg);
  transition: all 0.2s ease;
}

.el-tree-node.is-current > .el-tree-node__content {
  background-color: var(--tree-node-active-bg);
  color: var(--tree-node-active-text);
  border-left: var(--tree-node-active-border);
}

.el-tree-node__expand-icon {
  color: var(--icon-primary);
  padding: 0 !important;
}

.el-tree-node__label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 隐藏树形组件的横向滚动条 */
.tree-panel .el-scrollbar__bar.is-horizontal {
  display: none !important;
}

/* 分页组件样式 - 高度还原设计图 */
.el-pagination {
  padding: 0;
  font-weight: normal;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

/* 总条数显示 */
.el-pagination__total {
  font-size: 13px;
  color: var(--text-secondary);
  margin-right: 8px;
  font-weight: normal;
}

/* 每页显示条数选择器 */
.el-pagination__sizes {
  margin-right: 8px;
}

.el-pagination__sizes .el-select {
  width: 80px;
}

.el-pagination__sizes .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: transparent;
  color: var(--text-secondary);
  padding-right: 25px;
}

.el-pagination__sizes .el-select .el-input__inner:hover {
  border-color: var(--icon-primary);
}

/* 上一页下一页按钮 */
.el-pagination .btn-prev,
.el-pagination .btn-next {
  min-width: 28px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 13px;
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-pagination .btn-prev:hover:not(:disabled),
.el-pagination .btn-next:hover:not(:disabled) {
  border-color: var(--icon-primary);
  color: var(--icon-primary);
}

.el-pagination .btn-prev:disabled,
.el-pagination .btn-next:disabled {
  color: #c0c4cc;
  border-color: #dcdfe6;
  background-color: #f5f7fa;
  cursor: not-allowed;
}

/* 页码按钮 */
.el-pagination .el-pager {
  display: flex;
  align-items: center;
  margin: 0 2px;
}

.el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 26px;
  font-size: 13px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: transparent;
  color: var(--text-secondary);
  margin: 0 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: none;
  font-weight: normal;
}

.el-pagination .el-pager li.active {
  background-color: transparent;
  color: var(--icon-primary);
  border-color: var(--icon-primary);
  font-weight: 500;
}

.el-pagination .el-pager li:hover:not(.active):not(.disabled) {
  color: var(--icon-primary);
  border-color: var(--icon-primary);
  background-color: transparent;
}

.el-pagination .el-pager li.btn-quicknext,
.el-pagination .el-pager li.btn-quickprev {
  border: 1px solid #dcdfe6;
  background: transparent;
  color: var(--text-auxiliary);
}

.el-pagination .el-pager li.btn-quicknext:hover,
.el-pagination .el-pager li.btn-quickprev:hover {
  color: var(--icon-primary);
  border-color: var(--icon-primary);
}

/* 跳转输入框 */
.el-pagination__jump {
  margin-left: 8px;
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.el-pagination__jump .el-input {
  width: 50px;
  margin: 0 4px;
}

.el-pagination__jump .el-input .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: transparent;
}

.el-pagination__jump .el-input .el-input__inner:hover {
  border-color: var(--icon-primary);
}

/* 移除背景样式 */
.el-pagination.is-background .el-pager li:not(.disabled) {
  background-color: transparent;
  border: 1px solid #dcdfe6;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: transparent;
  border-color: var(--icon-primary);
  color: var(--icon-primary);
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  background-color: transparent;
  border-color: var(--icon-primary);
  color: var(--icon-primary);
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next {
  background-color: transparent;
  border: 1px solid #dcdfe6;
}

/* 按钮样式优化 */
.el-button {
  border-radius: var(--btn-primary-radius);
  font-weight: 400;
  padding: 10px 20px;
  transition: none;
  position: relative;
  overflow: hidden;
}

.el-button--primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  transform: none;
}

.el-button--success {
  background-color: var(--icon-success);
  border-color: var(--icon-success);
}

.el-button--success:hover,
.el-button--success:focus {
  background-color: var(--icon-success);
  border-color: var(--icon-success);
}

.el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.el-button--warning:hover,
.el-button--warning:focus {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.el-button--danger {
  background-color: var(--btn-danger-bg);
  border-color: var(--btn-danger-bg);
  color: var(--btn-danger-text);
}

.el-button--danger:hover,
.el-button--danger:focus {
  background-color: var(--btn-danger-bg);
  border-color: var(--btn-danger-bg);
}

.el-button--text {
  color: var(--icon-primary);
  padding: 0;
}

.el-button--text:hover,
.el-button--text:focus {
  color: var(--icon-primary);
  transform: none;
}

.el-button--small {
  padding: 8px 15px;
  font-size: 13px;
  border-radius: var(--btn-primary-radius);
}

.el-button--mini {
  padding: 6px 10px;
  font-size: 12px;
  border-radius: var(--btn-primary-radius);
}

/* 表单控件样式 */
.el-input__inner,
.el-textarea__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: border-color 0.3s;
}

.el-input__inner:hover,
.el-textarea__inner:hover {
  border-color: #c0c4cc;
}

.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: none;
}

.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  font-size: 13px;
}

.el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.el-dialog__body {
  padding: 20px;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

/* 次要按钮样式 */
.el-button--default {
  background-color: var(--btn-secondary-bg);
  border: var(--btn-secondary-border);
  color: var(--btn-secondary-text);
  border-radius: var(--btn-primary-radius);
}

.el-button--default:hover,
.el-button--default:focus {
  background-color: var(--btn-secondary-bg);
  border-color: var(--btn-secondary-border);
  color: var(--btn-secondary-text);
}

/* 搜索框样式 */
.el-input__inner {
  border: 1px solid var(--search-border);
  border-radius: var(--search-radius);
  background-color: var(--search-bg);
  color: var(--text-primary);
}

.el-input__inner::placeholder {
  color: var(--text-placeholder);
}

.el-input__inner:focus {
  border-color: var(--icon-primary);
  box-shadow: none;
}

.el-input__icon {
  color: var(--search-icon-color);
}

/* 全局移除输入框阴影 */
.el-input__inner:focus,
.el-textarea__inner:focus,
.el-select .el-input__inner:focus {
  box-shadow: none !important;
}

/* 主体样式 */
body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-size: 14px;
  color: var(--text-primary);
  background: var(--main-bg);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 页面通用布局样式 */
.app-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  display: flex;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

@media (max-width: 768px) {
  .page-content {
    padding: 12px;
  }
}
