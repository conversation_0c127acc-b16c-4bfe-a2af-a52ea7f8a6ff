<!--
  过程信息时间轴组件 - Component

  主要功能：
  - 展示事件处理过程的时间线
  - 支持排序、筛选、全选等功能
  - 提供过程项的选择和详情查看功能
-->

<template>
  <div class="process-section">
    <div class="section-header">
      <div class="section-title-area">
        <h3
          class="section-title"
          @click="toggleProcessSort"
          :class="{ sortable: true }"
        >
          <i class="el-icon-time"></i>
          过程信息
          <i class="el-icon-sort" style="margin-left: 8px; font-size: 14px"></i>
        </h3>
      </div>
      <div class="header-controls">
        <div class="filter-controls">
          <el-select
            v-model="selectedProcessType"
            placeholder="全部"
            size="small"
            style="width: 120px"
            @change="handleProcessTypeChange"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="option in processTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>

          <div class="control-actions">
            <el-checkbox v-model="selectAll" @change="handleSelectAll">
              全选
            </el-checkbox>
          </div>
        </div>
      </div>
    </div>

    <div class="process-timeline-container">
      <div
        v-if="filteredProcessData && filteredProcessData.length > 0"
        class="process-timeline"
        :class="{
          'has-multiple-items': filteredProcessData.length > 1,
        }"
        :style="timelineStyle"
      >
        <transition-group
          name="timeline-sort"
          tag="div"
          class="timeline-transition-group"
        >
          <div
            v-for="(item, index) in filteredProcessData"
            :key="item.id || `timeline-${index}-${item.createTime}`"
            class="process-timeline-item"
            :ref="`timelineItem${index}`"
          >
            <div class="timeline-number-wrapper">
              <div class="timeline-number">
                {{ getTimelineNumber(index) }}
              </div>
              <div
                class="timeline-line"
                v-if="index < filteredProcessData.length - 1"
                :style="getTimelineLineStyle()"
              ></div>
            </div>

            <div class="timeline-content" @click="showProcessDetail(item)">
              <div class="timeline-left">
                <div class="timeline-header">
                  <div class="timeline-time">
                    {{ formatDateTime(item.createTime) }}
                  </div>
                  <div class="timeline-unit">
                    {{ getOperatorDisplayName(item) }}
                  </div>
                </div>

                <div class="timeline-description">
                  {{ getProcessContent(item) }}
                </div>
              </div>

              <div class="timeline-right" @click.stop>
                <div class="timeline-actions">
                  <div class="timeline-type">
                    {{
                      item.processTypeName ||
                      item.originalData?.courseTypeName ||
                      "未知类型"
                    }}
                  </div>
                  <el-checkbox
                    :value="item.selected || false"
                    @change="handleItemSelect(item, $event)"
                    @click.stop
                    class="timeline-checkbox"
                  ></el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <div v-else class="no-data">
        <i class="el-icon-document"></i>
        <p>{{ loading ? "加载过程信息中..." : "暂无过程信息" }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProcessTimeline",
  props: {
    processData: {
      type: Array,
      default: () => [],
    },
    processTypeOptions: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filteredProcessData: [],
      selectedProcessType: "",
      selectAll: false,
      isAscending: false, // 排序状态：false为降序，true为升序
    };
  },
  watch: {
    processData: {
      handler() {
        this.filterProcessData();
      },
      immediate: true,
    },
  },
  computed: {
    timelineStyle() {
      const itemCount = this.filteredProcessData?.length || 0;

      if (itemCount <= 1) {
        return {
          "--timeline-height": "0px",
        };
      }

      return {};
    },
  },
  methods: {
    getTimelineLineStyle() {
      return {};
    },

    handleSelectAll(value) {
      console.log("全选状态:", value);
      this.filteredProcessData.forEach((item) => {
        this.$set(item, "selected", value);
      });
      this.$emit("select-all", value);
    },

    getOperatorDisplayName(item) {
      return item.courseName;
    },

    getProcessContent(item) {
      return item.courseInfo;
    },

    handleItemSelect(item, value) {
      this.$set(item, "selected", value);
      const allSelected = this.filteredProcessData.every(
        (item) => item.selected
      );
      this.selectAll = allSelected;
      this.$emit("item-select", { item, value });
    },

    toggleProcessSort() {
      try {
        if (this.filteredProcessData && this.filteredProcessData.length > 0) {
          this.isAscending = !this.isAscending;

          this.$nextTick(() => {
            this.filteredProcessData.reverse();

            this.$forceUpdate();
          });

          const sortText = this.isAscending ? "升序" : "降序";
          this.$message.success(`排序已更新为${sortText}`);
        }
      } catch (error) {
        console.error("排序失败:", error);
        this.$message.error("排序失败，请重试");
      }
    },

    getTimelineNumber(index) {
      if (this.isAscending) {
        // 升序：从1开始递增
        return index + 1;
      } else {
        // 降序：从总数开始递减
        return this.filteredProcessData.length - index;
      }
    },

    showProcessDetail(item) {
      this.$emit("show-detail", item);
    },

    handleProcessTypeChange() {
      this.filterProcessData();
      this.$emit("type-change", this.selectedProcessType);
    },

    filterProcessData() {
      if (!this.selectedProcessType) {
        this.filteredProcessData = [...this.processData];
      } else {
        this.filteredProcessData = this.processData.filter(
          (item) => item.processType === this.selectedProcessType
        );
      }

      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    formatDateTime(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.process-section {
  // background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  flex: 1;
  height: calc(100% - 20px);
  min-width: 580px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  margin-bottom: 20px;

  @media (max-width: 1600px) {
    min-width: 530px;
  }

  @media (max-width: 1400px) {
    min-width: 480px;
  }

  @media (max-width: 1200px) {
    min-width: 430px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, #4299e1, #3182ce, #805ad5);
    }

    .section-title-area {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .section-title {
      font-size: 16px;
      font-weight: 700;
      color: #1a202c;
      margin: 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 6px 8px;
      border-radius: 6px;
      position: relative;

      &.sortable {
        &:hover {
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.1) 0%,
            rgba(49, 130, 206, 0.1) 100%
          );
          color: #3182ce;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
        }
      }

      i {
        margin-right: 10px;
        color: #4299e1;
        font-size: 20px;
        transition: all 0.3s ease;
      }

      &:hover i {
        color: #3182ce;
        transform: scale(1.1);
      }
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .filter-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-select {
          .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;

            &:hover {
              border-color: #4299e1;
            }

            &:focus {
              border-color: #3182ce;
              box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
            }
          }
        }

        .control-actions {
          display: flex;
          align-items: center;
          gap: 12px;

          .el-checkbox {
            font-size: 14px;
            font-weight: 500;

            .el-checkbox__label {
              color: #4a5568;
            }

            &:hover .el-checkbox__label {
              color: #3182ce;
            }
          }
        }
      }
    }
  }

  .process-timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px 20px 20px;
    min-height: auto;
  }
}

.process-timeline {
  position: relative;

  .timeline-transition-group {
    position: relative;
  }

  .process-timeline-item {
    display: flex;
    margin-bottom: 14px;
    position: relative;

    &:last-child {
      margin-bottom: 20px;
    }

    .timeline-number-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 16px;
      position: relative;
      flex-shrink: 0;
      z-index: 2;

      .timeline-number {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: #ffffff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        border: 2px solid #ffffff;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      }

      .timeline-line {
        width: 2px;
        background: linear-gradient(to bottom, #4299e1 0%, #3182ce 100%);
        margin-top: 12px;
        opacity: 0.6;
        transition: all 0.3s ease;
        flex: 1;
      }
    }

    .timeline-content {
      flex: 1;
      background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      cursor: pointer;

      &:hover {
        border-color: #4299e1;
        box-shadow: 0 8px 20px rgba(66, 153, 225, 0.15);
        transform: translateY(-2px);
      }

      .timeline-left {
        flex: 1;
        min-width: 0; // 允许内容收缩
        margin-right: 3px;

        .timeline-header {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          flex-wrap: wrap; // 允许换行
          gap: 20px;

          .timeline-time {
            font-size: 14px;
            color: #4a5568;
            font-weight: 600;
            white-space: nowrap; // 时间不换行

            // 响应式调整
            @media (max-width: 1200px) {
              font-size: 13px;
            }
          }

          .timeline-unit {
            font-size: 12px;
            color: #4299e1;
            background: rgba(66, 153, 225, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(66, 153, 225, 0.2);
            white-space: nowrap; // 单位不换行
            text-align: center;

            @media (max-width: 1200px) {
              font-size: 11px;
              padding: 3px 6px;
            }
          }
        }

        .timeline-description {
          font-size: 14px;
          color: #2d3748;
          line-height: 1.5;
          word-break: break-word;
          overflow-wrap: break-word;

          @media (max-width: 1200px) {
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }

      .timeline-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
        flex-shrink: 0; // 防止被压缩
        min-width: 80px; // 确保最小宽度

        .timeline-actions {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;

          .timeline-type {
            font-size: 12px;
            color: #4299e1;
            background: rgba(66, 153, 225, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(66, 153, 225, 0.2);
            white-space: nowrap; // 类型标签不换行
            text-align: center;

            // 响应式调整
            @media (max-width: 1200px) {
              font-size: 11px;
              padding: 3px 6px;
            }
          }

          .timeline-checkbox {
            margin: 0;
            flex-shrink: 0; // 防止复选框被压缩
          }
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #999;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
    color: #d0d7de;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #656d76;
  }
}

/* 排序过渡动画 */
.timeline-sort-move {
  transition: all 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.timeline-sort-enter-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.timeline-sort-leave-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: absolute;
  width: calc(100% - 52px);
  left: 52px;
}

.timeline-sort-enter {
  opacity: 0;
  transform: translateX(-50px) scale(0.9);
}

.timeline-sort-leave-to {
  opacity: 0;
  transform: translateX(50px) scale(0.9);
}

/* 确保过渡组容器的相对定位 */
.timeline-transition-group {
  position: relative;
}
</style>
