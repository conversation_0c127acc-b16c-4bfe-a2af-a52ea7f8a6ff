<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="菜单类型" prop="menuTypeModel">
          <el-select
            v-model="formData.menuTypeModel"
            :disabled="styleType === 2"
            placeholder="请选择"
            value-key="menuType"
            @change="changeMenuType"
          >
            <el-option
              v-for="data in menuTypeList"
              :key="data.menuType"
              :label="data.name"
              :value="data"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            formData.menuTypeModel == null
              ? '菜单名称'
              : formData.menuTypeModel.menuType === 0
              ? '菜单名称'
              : formData.menuTypeModel.menuType === 1
              ? '按钮名称'
              : '模块名称'
          "
          prop="name"
        >
          <el-input
            v-model="formData.name"
            autocomplete="off"
            placeholder="请输入菜单名称"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item
          v-if="formData.menuTypeModel && formData.menuTypeModel.menuType === 0"
          label="选择图标"
          prop="icon"
        >
          <el-input
            v-model="formData.icon"
            :disabled="true"
            autocomplete="off"
            class="svg-input-box"
            placeholder="请选择图标"
          >
          </el-input>
          <div v-if="formData.icon && formData.icon !== ''" class="img-svg">
            <svg-icon
              :icon-class="formData.icon"
              class="svg-color"
              style="cursor: pointer"
              @click="iconClick"
            ></svg-icon>
          </div>
          <el-button type="primary" @click="iconClick">选择图标</el-button>
        </el-form-item>

        <el-form-item label="父级菜单" prop="parentMenuModel">
          <el-select
            v-model="formData.parentMenuModel"
            clearable
            filterable
            placeholder="请选择"
            value-key="id"
          >
            <!-- <el-option label="全部" :value="''"></el-option> -->
            <el-option
              v-for="data in parentMenuList"
              :key="data.id"
              :label="data.name"
              :value="data"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="访问路径（顶部面包屑按钮访问路径）" prop="path">
          <el-input
            v-model="formData.path"
            autocomplete="off"
            placeholder="请输入访问路径"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="菜单权限标识(列表按钮标识)" prop="permission">
          <el-input
            v-model="formData.permission"
            autocomplete="off"
            placeholder="请输入菜单权限标识"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="formData.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="tel"
            @input="handlePhoneInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>

      <el-dialog
        :visible.sync="iconShow"
        append-to-body
        title="菜单图标选择"
        width="50%"
      >
        <div class="icon-box">
          <svg-icon
            v-for="i in iconArr"
            :icon-class="i"
            class="svg-color"
            @click="iconSelect(i)"
          ></svg-icon>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import $api from "@/api/index";
// import {iconSvgList} from '@/icons/index.js'

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 1新增; 2修改
      iconShow: false,
      iconArr: [],
      menuTypeList: [],
      parentMenuList: [],
      formData: {
        menuTypeModel: null,
        parentMenuModel: null,
        name: "",
        path: "",
        icon: "",
        systemAppId: 20250318, //系统ID
      },
      rules: {
        menuTypeModel: [
          { required: true, message: "菜单类型不能为空", trigger: "blur" },
        ],
        parentMenuModel: [
          { required: true, message: "父级菜单不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        icon: [
          { required: true, message: "菜单图标不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "访问路径不能为空", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    // let iconList = iconSvgList()
    let iconArr = [];
    // if (iconList) {
    //   for (let i in iconList) {
    //     if (iconList[i].default.id.includes('mh-menu')) {
    //       iconArr.push(iconList[i].default.id.slice(5))
    //     }
    //   }
    // }
    this.iconArr = iconArr;
  },
  methods: {
    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const {
            menuTypeModel,
            parentMenuModel,
            name,
            path,
            sort,
            icon,
            permission,
          } = this.formData;
          const params = {
            menuType: menuTypeModel.menuType,
            parentId: parentMenuModel.id,
            name: name,
            path: path,
            sort: sort,
            icon: icon,
            permission: permission,
            delFlag: 0, //是否删除：0=否，1=是
            systemAppId: 20250318, //系统ID
          };
          if (this.styleType === 1) {
            const res = await $api.systemApi.createMenu(params);
            const { code, error } = res.data;
            if (code === 0) {
              this.$message.success("新增成功");
              this.$emit("ok");
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          } else {
            params.id = this.model.id;
            const res = await $api.systemApi.updateMenu(params);
            const { code, error } = res.data;
            if (code === 0) {
              this.$message.success("修改成功");
              this.$emit("ok");
              this.close();
            } else {
              this.$message.error(error);
            }
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.reset();
      this.$emit("ok", 1);
    },
    reset() {
      this.formData = {
        menuTypeModel: null,
        parentMenuModel: null,
        name: "",
        path: "",
        sort: "",
        icon: "",
      };
      this.model = {};
      this.menuTypeList = [];
      this.parentMenuList = [];
    },
    addFormFn() {
      this.reset();
      this.styleType = 1;
      this.getMenuTypeList();
    },
    edit(data) {
      this.styleType = 2;
      this.getMenuTypeList();
      this.model = Object.assign({}, data);
      const model = this.model;
      this.formData = {
        menuTypeModel: { menuType: model.menuType, name: model.name },
        parentMenuModel: {
          id: model.parentId,
          name: model.menuType === 2 ? "无" : model.name,
        },
        name: model.name,
        path: model.path,
        sort: model.sort,
        icon: model.icon,
        permission: model.permission,
      };
      this.changeMenuType({ menuType: model.menuType, name: model.name });
    },
    iconClick() {
      this.iconShow = true;
    },
    iconSelect(svgName) {
      console.log(svgName);
      this.formData.icon = svgName;
      this.iconShow = false;
    },
    async getMenuTypeList() {
      this.menuTypeList = [
        { name: "模块", menuType: 2 },
        { name: "菜单", menuType: 0 },
        { name: "按钮", menuType: 1 },
      ];
    },
    handlePhoneInput(value) {
      // 只允许输入数字 使用正则表达式移除非数字字符
      this.formData.sort = value.replace(/\D/g, "");
    },
    changeMenuType(v) {
      // this.addTitle = `新增${v.name}`;
      // 菜单类型：0=菜单，1=按钮， 2=模块
      if (v.menuType === 1) {
        // 如果选择的是按钮，则获取所有的父级菜单列表
        this.getParentMenuList(0);
      } else if (v.menuType === 2) {
        // 如果选择的是模块，则没有父级菜单，不能选择
        // this.parentMenuList = [];
        this.parentMenuList = [{ name: "无", id: 0 }];
        this.formData.parentMenuModel = { id: 0, name: "无" };
      } else {
        // 如果选择的是菜单，则获取所有的父级模块列表
        this.getParentMenuList(2);
      }
    },
    async getParentMenuList(menuType) {
      const params = {
        menuType,
      };
      const res = await $api.systemApi.getMenu(params);
      const { code, data, error } = res.data;
      if (code === 0) {
        this.parentMenuList = data;
      } else {
        this.$message.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
