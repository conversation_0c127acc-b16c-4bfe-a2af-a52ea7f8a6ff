import { auth } from "@/utils";

import request from "@/utils/request";
import { userApi, systemManagementApi } from "@/api";
import routeService from "@/services/routeService";
import crypto from "@/utils/crypto";

// 从菜单数据中递归提取按钮数据
function extractButtonsFromMenuData(menuData, parentPath = "") {
  const buttons = [];

  if (!Array.isArray(menuData)) {
    console.warn("菜单数据不是数组:", menuData);
    return buttons;
  }

  try {
    menuData.forEach((menu) => {
      if (!menu || typeof menu !== "object") {
        console.warn("无效的菜单项:", menu);
        return;
      }

      // 如果是按钮类型（menuType为3），添加到按钮数组
      if (menu.menuType === 3) {
        buttons.push({
          ...menu,
          parentPath: parentPath || menu.parentPath,
        });
      }

      // 递归处理子菜单
      if (
        menu.children &&
        Array.isArray(menu.children) &&
        menu.children.length > 0
      ) {
        try {
          const childButtons = extractButtonsFromMenuData(
            menu.children,
            menu.path || parentPath
          );
          buttons.push(...childButtons);
        } catch (childError) {
          console.error("处理子菜单按钮失败:", childError, menu);
        }
      }
    });
  } catch (error) {
    console.error("提取按钮数据时发生错误:", error);
  }

  return buttons;
}

const state = {
  token: auth.getToken(),
  userInfo: auth.getUserInfo(),
  permissions: auth.getPermissions(),
  roles: [],
};

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token;
  },

  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
    state.roles = userInfo?.roles || [];
  },

  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions;
  },

  CLEAR_AUTH(state) {
    state.token = null;
    state.userInfo = null;
    state.permissions = [];
    state.roles = [];
  },
};

const actions = {
  // 登录
  async login({ commit }, loginData) {
    try {
      const response = await request.post("/ds/doLogin", {
        loginName: loginData.username,
        password: crypto.encryptData(loginData.password),
      });

      if (response.code === 0 && response.data) {
        const { token, refreshToken, tokenType } = response.data;

        const remember = loginData.remember || false;

        auth.setToken(token, remember);
        auth.setRefreshToken(refreshToken, remember);
        if (tokenType) {
          auth.setTokenType(tokenType, remember);
        }

        commit("SET_TOKEN", token);

        // 账号密码登录成功，token已设置，用户信息将通过getUserInfo接口获取

        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      throw new Error(error.message || "登录失败");
    }
  },

  // IP免密登录
  async ipLogin({ commit }, { ipAddress, remember = true }) {
    try {
      const response = await systemManagementApi.doIpLogin({
        ipAddress: ipAddress,
      });

      if (response.code === 0 && response.data) {
        const { token, refreshToken, tokenType } = response.data;

        auth.setToken(token, remember);
        auth.setRefreshToken(refreshToken, remember);
        if (tokenType) {
          auth.setTokenType(tokenType, remember);
        }

        commit("SET_TOKEN", token);

        return response.data;
      } else {
        throw new Error(response.message || "IP登录失败");
      }
    } catch (error) {
      throw new Error(error.message || "IP登录失败");
    }
  },

  // 发送短信验证码
  async sendSmsCode({ commit }, phone) {
    try {
      const response = await systemManagementApi.doSmsMessage({ phone });

      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "发送验证码失败");
      }
    } catch (error) {
      throw new Error(error.message || "发送验证码失败");
    }
  },

  // 短信验证码登录
  async smsLogin({ commit }, { phone, code, remember = true }) {
    try {
      const response = await systemManagementApi.doSmsLogin({ phone, code });

      if (response.code === 0 && response.data) {
        const { token, refreshToken, tokenType } = response.data;

        auth.setToken(token, remember);
        auth.setRefreshToken(refreshToken, remember);
        if (tokenType) {
          auth.setTokenType(tokenType, remember);
        }

        commit("SET_TOKEN", token);

        return response.data;
      } else {
        throw new Error(response.message || "短信登录失败");
      }
    } catch (error) {
      throw new Error(error.message || "短信登录失败");
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await userApi.getUserInfo();

      if (response.code === 0 && response.data) {
        const userInfo = {
          id: response.data.id,
          username: response.data.loginName,
          name: response.data.userName,
          phone: response.data.phone,
          phoneTel: response.data.phoneTel,
          positions: response.data.positions,
          userClass: response.data.userClass,
          orgId: response.data.orgId,
          orgName: response.data.orgName,
          userRole: response.data.userRole || [],
          roles: response.data.userRole?.map((r) => r.roleName) || [],
          fileBaseUrl: response.data.fileBaseUrl,
        };

        commit("SET_USER_INFO", userInfo);
        auth.setUserInfo(userInfo, true);

        // 使用getUserInfo中的菜单和权限数据
        const menuData =
          response.data.userMenu ||
          response.data.menus ||
          response.data.menuList ||
          [];

        // 检查菜单数据是否为空
        if (!Array.isArray(menuData) || menuData.length === 0) {
          throw new Error("用户没有分配任何菜单权限，请联系管理员");
        }

        // 从菜单数据中提取按钮数据（menuType为3的项目）
        let buttonData = [];
        try {
          buttonData = extractButtonsFromMenuData(menuData);
        } catch (error) {
          console.error("提取按钮数据失败:", error);
          // 如果提取按钮数据失败，继续使用空数组，不影响菜单加载
          buttonData = [];
        }

        try {
          // getUserInfo中的菜单数据加载路由
          await routeService.loadUserRoutesFromData(menuData, buttonData);
        } catch (error) {
          console.error("路由加载失败:", error);
          routeService.clearAll();
          throw new Error(`菜单加载失败: ${error.message}`);
        }

        return userInfo;
      } else {
        throw new Error(response.message || "获取用户信息失败");
      }
    } catch (error) {
      throw error;
    }
  },

  // 获取菜单权限和按钮权限
  async getMenuDataList({ dispatch }) {
    return await dispatch("getUserInfo");
  },

  // 登出
  async logout({ commit }) {
    try {
      await request.post("/ds/logout");
    } catch (error) {
      // 静默处理登出接口错误
    } finally {
      commit("CLEAR_AUTH");
      auth.clearAuth();
      // 清除路由服务中的动态路由和权限数据
      routeService.clearAll();
    }
  },
};

const getters = {
  hasPermission: (state) => (permission) => {
    return state.permissions.includes(permission);
  },
  hasRole: (state) => (role) => {
    return state.userInfo.roles && state.userInfo.roles.includes(role);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
