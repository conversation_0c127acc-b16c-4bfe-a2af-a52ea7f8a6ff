<template>
  <div class="assistant-Ai-index">
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <div class="assistant-ai-content">
        <div class="assistant-Ai-chat">
          <div
            class="chat-message"
            v-for="(message, index) in messagesList"
            :key="index"
          >
            <div
              class="message-content"
              :class="message.type === 1 ? 'opposite-message' : 'self-message'"
            >
              <div v-if="message.type === 1" class="avatar">
                <el-avatar
                  :src="require('@/assets/images/icons/aiAvatar.svg')"
                ></el-avatar>
              </div>
              <div v-if="message.type === 0" class="avatar">
                <el-avatar
                  :src="require('@/assets/images/icons/userAvatar.svg')"
                ></el-avatar>
              </div>
              <div class="text">
                {{ message.text }}
              </div>
            </div>
          </div>
        </div>
        <div class="assistant-Ai-information">
          <el-descriptions
            :column="descriptionsColumn"
            :colon="descriptionsColon"
          >
            <div class="title" slot="title">
              <span class="line"></span>
              <span>基本信息</span>
            </div>
            <el-descriptions-item label="事件标题：">
              <el-input
                v-model="from.infoTitle"
                placeholder="请输入"
                clearable
              />
            </el-descriptions-item>
            <el-descriptions-item label="事发时间：">
              <el-input
                v-model="from.infoTime"
                placeholder="请输入"
                clearable
              />
            </el-descriptions-item>
            <el-descriptions-item label="事发地点：">
              <el-input
                v-model="from.infoLocationDetail"
                placeholder="请输入"
                clearable
              />
            </el-descriptions-item>
            <el-descriptions-item label="经纬度：">
              <el-input
                v-model="from.latitudeAndLongitude"
                placeholder="请输入"
                clearable
              />
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions
            :column="descriptionsColumn"
            :colon="descriptionsColon"
          >
            <div class="title" slot="title">
              <span class="line"></span>
              <span>事件详情</span>
            </div>
            <el-descriptions-item label="事件详情：">
              <el-input
                v-model="from.eventInfo"
                placeholder="请输入"
                clearable
              />
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions
            :column="descriptionsColumn"
            :colon="descriptionsColon"
          >
            <div class="title" slot="title">
              <span class="line"></span>
              <span>其他信息</span>
            </div>
            <el-descriptions-item label="上报时间：">
              <el-date-picker
                v-model="from.createTime"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-descriptions-item>
            <el-descriptions-item label="上报单位：">
              <el-input v-model="from.unit" placeholder="请输入" clearable />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </general-dialog>
  </div>
</template>
<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { assistantAIApi, meetingManagementApi } from "@/api";

export default {
  name: "AssistantAiDialog",
  components: { GeneralDialog },
  data() {
    return {
      timer: null,
      dialogVisible: false,
      dialogWidth: "1100px",
      generalDialogTitle: "AI助手",
      descriptionsColumn: 2,
      descriptionsColon: false,
      messagesList: [],
      messagesList1: [
        {
          type: 1, //0 表示自己 1 表示对方
          text: "市应急办您好，这里是市卫生和健康委员会。接市人民医保上报，京东幼儿园2名幼儿出现腹泻，在人民医院治疗。",
        },
        {
          type: 0,
          text: "市卫生和健康委员会，您好。请上报事情发生时间。",
        },
        {
          type: 1,
          text: "发生时间是5月30日早晨9点27分，目前2名幼儿已在人民医院治疗，病情稳定，目前市公安、市市场监督管理局、市卫生部门已安排专人到幼儿园进行调查，事件已处置结束，原因为幼儿家里食用过量西瓜，幼儿园卫生未查出异常。",
        },
        {
          type: 0,
          text: "收到，请随时上报事件进展。",
        },
      ],
      messagesList2: [
        {
          type: 1, //0 表示自己 1 表示对方
          text: "市应急办您好，这里是市公安局公安交通管理局。在京承高速出京方向K67+750，一辆车辆侧翻、高速断路，造成交通拥堵。",
        },
        {
          type: 0,
          text: "市公安局公安交通管理局，您好。请上报侧翻发生时间。",
        },
        {
          type: 1,
          text: "侧翻发生时间是13时27分，目前交通、交管、消防部门正在现场处置。",
        },
        {
          type: 0,
          text: "收到，请随时上报事件进展。",
        },
      ],

      from: {
        infoTitle: "", //事件标题
        infoTime: "", //事发时间
        infoLocationDetail: "", //事发地点
        latitudeAndLongitude: "", //经纬度
        eventInfo: "", //事件详情
        createTime: "", //上报时间
        unit: "", //上报单位
      },
    };
  },
  mounted() {
    this.startPolling();
  },
  methods: {
    startPolling() {
      this.timer = setInterval(async () => {
        await this.fetchData();
      }, 10000);
    },

    clearPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    async fetchData() {
      const res = await assistantAIApi.linShiCreateReportInfo();
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);

      if (data) {
        this.from = {
          ...data,
        };
        this.from.latitudeAndLongitude =
          data.infoLongitude + "," + data.infoLatitude; //经纬度

        this.from.unit = "市公安局公安交通管理局"; //上报单位

        // 测试数据
        if (data.infoTitle === '京东幼儿园腹泻事件调查') {
          this.messagesList = this.messagesList1;
        } else {
          this.messagesList = this.messagesList2;
        }

        this.dialogVisible = true;
        this.clearPolling();
      }
    },

    //提交
    async handleSubmit() {
      const res = await assistantAIApi.createReportInfo(this.from);
      const { code, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.$message.success("信息上报成功");
      this.handleCancel();
      this.startPolling();
    },

    handleCancel() {
      this.dialogVisible = false;
      this.clearPolling(); // 关闭弹窗时确保停止轮询
    },
  },
};
</script>
<style scoped lang="scss">
.assistant-ai-content {
  padding: 30px 60px 5px 60px;

  .assistant-Ai-chat {
    position: relative;
    height: calc(100vh - 190px - 15px - 20px - 356px - 80px);
    overflow: auto;
    padding-right: 15px;
    margin-bottom: 15px;

    .chat-message {
      margin-bottom: 10px;

      .message-content {
        display: flex;
        align-items: baseline;
      }

      .opposite-message {
        .avatar {
          margin-right: 10px;
        }

        .text {
          width: auto;
          border-radius: 0 25px 25px 25px;
          background: rgba(255, 255, 255, 0.7);
          box-shadow: 0 6px 12px 0 rgba(63, 140, 255, 0.1);
        }
      }

      .self-message {
        flex-direction: row-reverse;
        .avatar {
          margin-left: 10px;
        }
        .text {
          width: auto;
          background: #d1e3ff;
          border-radius: 25px 0 25px 25px;
          box-shadow: 0 6px 12px 0 rgba(63, 140, 255, 0.1);
        }
      }

      .text {
        padding: 20px 20px;
        font-family: Microsoft YaHei, sans-serif;
        font-weight: 400;
        font-size: 18px;
        color: #0a1629;
        line-height: 24px;
        text-align: left;
        letter-spacing: 1px;
        font-style: normal;
        text-transform: none;
      }

      ::v-deep {
        .el-avatar {
          width: 45px;
          height: 45px;
          background: transparent;
        }
      }
    }
  }

  .assistant-Ai-information {
    border-top: 1px solid var(--themeColor);
    padding-top: 20px;

    .title {
      .line {
        border: 2px solid var(--themeColor);
        margin-right: 20px;
      }
    }

    ::v-deep {
      .el-descriptions-item__container {
        display: flex;
        align-items: center;
      }

      .el-descriptions-item__content {
        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          width: 100%;
        }
      }

      .el-descriptions-item__cell {
        padding-right: 20px;
      }
    }
  }
}
</style>
