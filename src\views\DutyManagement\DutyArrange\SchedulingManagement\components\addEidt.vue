<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >

        <el-form-item label="当前岗位" prop="departmentPath">
          <el-input
            :disabled="true"
            v-model="departmentPath"
            autocomplete="off"
            placeholder="请输入"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType === 4" label="值班人员" prop="beforeName">
          <el-input
            :disabled="true"
            v-model="beforeName"
            autocomplete="off"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType == 0 || styleType == 1 || styleType == 3" label="选择人员" prop="selectedUserId" class="select-width">
          <!-- <el-cascader
            v-model="form.orgList"
            :options="orgTree"
            :props="props"
            collapse-tags
            clearable
            @change="handleChange">
          </el-cascader> -->

          <el-cascader
            v-model="formData.selectedUserId"
            :props="cascaderProps"
            :options="options"
            :show-all-levels="false"
            clearable
            placeholder="请选择用户"
            @change="handleChange"
          ></el-cascader>
          <!-- <el-input
            v-model="formData.userName"
            autocomplete="off"
            placeholder="请输入"
            type="text"
          >
          </el-input> -->
        </el-form-item>

        <el-form-item v-if="styleType === 2" label="替换前的人员" prop="beforeName">
          <el-input
            :disabled="true"
            v-model="beforeName"
            autocomplete="off"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType === 2" label="替换后的人员" prop="afterName">
          <el-input
            :disabled="true"
            v-model="afterName"
            autocomplete="off"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="styleType === 1 || styleType === 2" label="替换原因" prop="substituteContent">
          <el-input
            v-model="formData.substituteContent"
            autocomplete="off"
            type="text"
          >
          </el-input>
        </el-form-item>

        <!-- <el-form-item v-if="styleType != 1" label="人员" prop="currentUser">
          <el-select
            v-model="formData.userName"
            placeholder="请选择" 
            @change="changeUserType">
            <el-option
              v-for="data in userList"
              :key="data.userId"
              :label="data.userName"
              :value="data.userId"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        
        <!-- <el-form-item :label="handleUserType" prop="newUser">
          <el-cascader
            v-model="formData.newUser"
            placeholder="请选择"
            :show-all-levels="false"
            :options="contactList"
            filterable>
          </el-cascader>
        </el-form-item> -->

        <!-- <el-form-item v-if="styleType === 2" label="换班日期" prop="dateTime">
          <el-date-picker
            v-model="formData.dateTime"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 184px;"
            type="date"
            @change="handleChangeMonth">
          </el-date-picker>
        </el-form-item> -->

        <!-- <el-form-item label="值班人数" prop="num">
          <el-input
            v-model="formData.num"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入"
            type="tel"
            @input="handlePhoneInput"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="formData.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="tel"
            @input="handleSortInput"
          >
          </el-input>
        </el-form-item> -->

        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>

import { dutyManagementApi, systemManagementApi, orgApi } from '@/api/index'

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 0新增; 1替班；2换班；3修改；4：删除
      beforeName: "", // 换班前的人员
      afterName: "", // 换班后的人员
      clickData: null,
      departmentPath: '',

      selectedUserId: '', // 选中的用户
      nodeMap: new Map(), // 用于存储id到对象的映射
      options: [],      // 初始加载的顶级部门
      cascaderProps: {
        value: 'id',
        label: 'orgName',
        children: 'children',
        checkStrictly: true, // 允许选择任意级
        lazy: true,          // 启用懒加载
        emitPath: false, 
        lazyLoad: this.loadChildren,
        // 自定义节点显示内容
        renderLabel: ({ data }) => {
          return data.type === 1 ? `${data.userName} (${data.phone})` : data.orgName;
        }
      },

      // eventTypeList: [
      //   {
      //     value:1,
      //     name:"新增"
      //   },
      //   {
      //     value:2,
      //     name:"替班"
      //   },
      //   {
      //     value:3,
      //     name:"换班"
      //   }
      // ],
      formData: {
        selectedUserId: '',
        substituteContent: ''
      },
      rules: {
        selectedUserId: [
          {required: true, message: '值班人员不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
    // this.loadTopLevel();
  },
  methods: {
    // handlePhoneInput(value) {
    //   // 只允许输入数字 使用正则表达式移除非数字字符
    //   this.formData.num = value.replace(/\D/g, '')
    // },
    // handleSortInput(value) {
    //   this.formData.sort = value.replace(/\D/g, '')
    // },
    // changeUserType(levelId) {
    //   if (levelId === 1) {
    //     this.formData.parentId = '0';
    //     this.parentList = [{positionName: '无', id: '0'}];
    //     // this.parentList = [{positionName: '无', id: 0}];
    //   } else {
    //     this.getParentList(levelId);
    //   }
    // },

    // 加载顶级部门
    async loadTopLevel() {
      try {
        const response = await systemManagementApi.queryOrgAddressBook({orgId: '0'});
        this.options = response.data.map(item => ({
          ...item,
          disabled: item.type === 1 // 用户类型在顶级禁用（根据你的数据结构调整）
        }));
      } catch (error) {
        console.error('加载顶级部门失败:', error);
      }
    },

    // 懒加载子节点
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map(item => ({
            ...item,
            disabled: item.type === 0 && item.children && item.children.length === 0 // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({orgId: data?.id || '0'});
        const nodes = response.data.map(item => {
           this.nodeMap.set(item.id, item); // 存储映射关系
          // 用户节点
          if (item.type === 1) {
            return {
              ...item,
              orgName: item.userName || '未命名用户',
              isLeaf: true
            };
          }
          // 部门节点
          return {
            ...item,
            disabled: item.type === 0 && (!item.children || item.children.length === 0) // 空部门禁用
          };
          
        });
        resolve(nodes);
      } catch (error) {
        console.error('加载子节点失败:', error);
        resolve([]);
      }
    },

    // 处理选择变化
    handleChange(value) {
      if (value && value.length > 0) {
        // const lastId = value[value.length - 1];
        this.selectedUserId = value;
      }
    },

    addFormFn(paramsData) {
      this.reset();
      this.styleType = paramsData.type;
      if (this.styleType === 2 || this.styleType === 4) {
        this.beforeName = paramsData.arrangement?.userName;
      }
      if (this.styleType === 2) {
        this.afterName = paramsData.arrangementReplace?.userName;
      }
      this.clickData = paramsData;
      this.departmentPath = paramsData.path;
    },
    // edit(data) {
    //   this.styleType = 2
    //   this.getPositionById(data.id);
    //   this.getParentList(data.level);
    // },
    
    // async getPositionById(id) {
    //   const res = await dutyManagementApi.queryPositionById({id});
    //   const {code, data, error} = res;
    //   if (code === 0) {
    //     this.formData = data;
    //     if (data.level === 1) {
    //       this.formData.parentId = '0';
    //       this.parentList = [{positionName: '无', id: '0'}];
    //     }
    //   } else {
    //     this.$message.error(error);
    //   }
    // },
    // async getParentList(level) {
    //   const res = await dutyManagementApi.queryPositionByLevel({level});
    //   const {code, data, error} = res;
    //   if (code === 0) {
    //     this.parentList = data;
    //   } else {
    //     this.$message.error(error);
    //   }
    // },

    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType > 0) {
            let successTip = '';
            let params = {
              substituteType: this.styleType,
              arrangementId: this.clickData.arrangement.id
            };
            if (this.styleType === 1) {
              const selectedUserModel = this.nodeMap.get(this.selectedUserId);
              // console.log('选中的用户ID:', value, selectedUserModel);
              params.userName = selectedUserModel.userName;
              params.userId = selectedUserModel.id;
              params.phoneNum = selectedUserModel.phone;
              params.substituteContent = this.formData.substituteContent;
              successTip = "替班成功";
            } else if (this.styleType === 2) {
              params.substituteContent = this.formData.substituteContent;
              params.arrangementReplaceId = this.clickData.arrangementReplace.id;
              successTip = "换班成功";
            } else if (this.styleType === 3) {
              const selectedUserModel = this.nodeMap.get(this.selectedUserId);
              // console.log('选中的用户ID:', value, selectedUserModel);
              params.userName = selectedUserModel.userName;
              params.userId = selectedUserModel.id;
              params.phoneNum = selectedUserModel.phone;
              successTip = "修改成功";
            } else if (this.styleType === 4) {
              successTip = "删除成功";
            }
            const res = await dutyManagementApi.createSubstitute(params);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success(successTip);
              this.$emit('ok');
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          } else {
            // 组装请求参数
            const dateTime = this.clickData.rowData.dateTime[0];
            // this.formData.dat = dateTime?.dat;
            // this.formData.positionId = this.clickData.colData.id;
            const params = {};
            params.dat = dateTime?.dat;
            params.positionId = this.clickData.colData.id;
            const selectedUserModel = this.nodeMap.get(this.selectedUserId);
            // console.log('选中的用户ID:', value, selectedUserModel);
            params.userName = selectedUserModel.userName;
            params.userId = selectedUserModel.id;
            params.phoneNum = selectedUserModel.phone;
            const res = await dutyManagementApi.createArrangement(params);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功');
              this.$emit('ok');
              this.reset();
              this.close();
            } else {
              this.$message.error(error);
            }
          }
        } else {
          return false;
        }
      })
    },
    close() {
      this.reset();
      this.$emit('ok', 1);
    },
    reset() {
      this.formData = {
        selectedUserId: '',
        substituteContent: ''
      }
      this.beforeName = "";
      this.afterName = "";
      this.departmentPath = "";
      this.clickData = null;
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
