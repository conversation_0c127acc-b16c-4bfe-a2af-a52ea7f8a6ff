<template>
  <div id="app" class="app-wrapper">
    <!-- 主布局组件 - Component -->
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <div class="logo">
          <!-- <div class="logo-icon">综</div> -->
          <!-- <div class="logo-img"> -->
          <img
            style="width: 45px; height: 45px"
            src="@/assets/images/logo.png"
            alt=""
          />
          <!-- </div> -->
          <span class="logo-text">应急值守业务系统</span>
        </div>
      </div>
      <div class="header-nav" @wheel="handleNavWheel">
        <div
          class="nav-content"
          :class="{ 'nav-loading-state': isMenuLoading }"
        >
          <!-- 菜单加载状态 -->
          <div v-if="isMenuLoading" class="nav-loading">
            <i class="el-icon-loading"></i>
            <span>加载菜单中...</span>
          </div>
          <!-- 菜单项 -->
          <template v-else>
            <div
              v-for="(navItem, index) in processedRouters"
              :key="navItem.id"
              class="nav-item"
              :class="{ active: navItem.isActive }"
              @click="handleNavClick(index, navItem, $event)"
            >
              {{ navItem.name }}
            </div>
          </template>
        </div>
      </div>
      <div class="header-right">
        <div class="user-info">
          <i class="el-icon-user-solid"></i>
          <span class="user-name">{{ userInfo?.name || "admin" }}</span>
          <span v-if="userInfo?.positions" class="user-position"
            >（{{ userInfo.positions }}）</span
          >
        </div>
        <div class="phone-info" v-if="userInfo?.phone">
          <i class="el-icon-phone"></i>
          <span class="phone-number">{{ userInfo.phone }}</span>
        </div>
        <div class="notification">
          <i class="el-icon-bell"></i>
        </div>
        <div class="logout-btn" @click="handleLogout">
          <i class="el-icon-switch-button"></i>
        </div>
      </div>
    </div>

    <!-- 主体容器 -->
    <div class="main-container">
      <!-- 左侧菜单 - 显示当前激活导航的下级菜单 -->
      <div
        v-show="!isHome"
        class="sidebar"
        :class="{ collapsed: !sidebar.opened }"
      >
        <!-- 折叠按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <i
            :class="sidebar.opened ? 'el-icon-s-fold' : 'el-icon-s-unfold'"
          ></i>
        </div>

        <!-- 菜单加载状态 -->
        <div v-if="isMenuLoading" class="nav-loading nav-submenu-item">
          <i class="el-icon-loading"></i>
          <span>加载菜单中...</span>
        </div>
        <!-- 修改为el-menu实现多级菜单 -->
        <template v-else>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :collapse="!sidebar.opened"
            :collapse-transition="false"
            background-color="#e0e9f7"
            text-color="#333"
            active-text-color="#409EFF"
            @select="handleMenuSelect"
          >
            <menu-item
              v-for="item in currentMenuItems"
              :key="item.path"
              :item="item"
              :base-path="item.path"
            />
          </el-menu>
        </template>
      </div>

      <!-- 右侧内容区域 -->
      <div
        class="content-wrapper"
        :class="{ 'full-width': activeTopNavIndex === 0 }"
      >
        <!-- 面包屑导航 - 有下级菜单时显示 -->
        <div v-if="currentMenuItems.length > 0" class="breadcrumb">
          <div class="breadcrumb-content">
            <i class="el-icon-location breadcrumb-icon"></i>
            <el-breadcrumb separator="/">
              <!-- 如果面包屑需要跳转 -->
              <!-- :to="index < breadcrumbList.length - 1 ? item.path : null" -->
              <el-breadcrumb-item
                v-for="(item, index) in breadcrumbList"
                :key="index"
              >
                {{ item.name }}
              </el-breadcrumb-item>
            </el-breadcrumb>

            <!-- 获取当前页面的按钮 -->
            <div class="breadcrumb-actions">
              <el-button
                v-for="button in currentButtons"
                v-if="hasPagePermission(button.permission || button.path)"
                :key="button.id"
                type="primary"
                size="small"
                :icon="button.icon"
                @click="handleButtonClick(button)"
              >
                {{ button.name }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 页面内容 -->
        <div class="page-content">
          <div class="content-container">
            <transition name="slide-right" mode="out-in">
              <router-view :key="$route.fullPath" />
            </transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from "vuex";
// import auth from "../utils/auth";
import MenuItem from "../components/MenuItem.vue";
import permissionMixin from "@/mixins/permission";

export default {
  name: "Layout",
  components: {
    MenuItem,
  },
  mixins: [permissionMixin],
  data() {
    return {
      buttonIconMap: {
        import_top: "el-icon-upload2",
        export_top: "el-icon-right",
        download_top: "el-icon-download",
        sync_top: "el-icon-refresh",
        add_top: "el-icon-plus",
        edit_top: "el-icon-edit",
        deleteBatch_top: "el-icon-delete",
        task_sync_top: "el-icon-refresh-right",
        task_dispatch_top: "el-icon-s-promotion",
      },
      routers: [],
      buttonData: [],
      // 菜单加载状态
      isMenuLoading: false,
      // 侧边栏状态
      sidebar: {
        opened: true,
      },
      // 当前激活的一级导航
      activeTopNavIndex: 0,
      activeMenu: "", // 当前激活的菜单
    };
  },
  async mounted() {
    // 监听菜单数据加载完成事件
    window.addEventListener("menuDataLoaded", this.handleMenuDataLoaded);

    // 页面刷新时重新获取用户信息和菜单数据
    this.isMenuLoading = true;
    try {
      await this.getUserInfo();
    } catch (error) {
      console.error("获取用户信息失败:", error);

      // 检查是否是菜单权限相关错误
      if (
        error.message &&
        (error.message.includes("菜单加载失败") ||
          error.message.includes("用户没有分配任何菜单权限"))
      ) {
        // 菜单权限错误，清除所有数据并跳转到登录页
        console.error("菜单权限错误:", error.message);

        // 清除本地存储的菜单数据，防止刷新后使用旧数据
        const { logoutUtils } = require("@/utils");
        logoutUtils.clearAllUserData();

        this.$message.error("用户没有分配任何菜单权限，请联系管理员");
        this.$router.replace({
          path: "/login",
          query: { redirect: this.$route.fullPath, error: "menu_load_failed" },
        });
        return;
      }

      // 如果是认证相关错误，才跳转到登录页
      if (
        error.message &&
        (error.message.includes("401") ||
          error.message.includes("未授权") ||
          error.message.includes("token"))
      ) {
        this.$message.error("登录状态已过期，请重新登录");
        this.$router.push("/login");
        return;
      }

      // 其他错误，显示错误信息但不跳转
      this.$message.warning(`获取用户信息失败: ${error.message}`);
      // 注意：不再尝试使用本地数据，因为可能存在权限问题
    } finally {
      this.isMenuLoading = false;
    }

    setTimeout(() => {
      this.loadMenuData();
      this.setActiveMenuFromRoute();
    }, 100);
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener("menuDataLoaded", this.handleMenuDataLoaded);
  },
  watch: {
    $route() {
      this.setActiveMenuFromRoute();
    },
  },

  computed: {
    ...mapState("user", ["userInfo"]),

    //判断是否为首页
    isHome() {
      return (
        this.$route.path === "/" ||
        this.$route.path === "/home" ||
        this.$route.path === "/thirdHome"
      );
    },

    processedRouters() {
      if (!this.routers || !Array.isArray(this.routers)) {
        return [];
      }
      const result = this.routers.map((route, index) => ({
        ...route,
        isActive: this.checkActive(route, index),
      }));

      return result;
    },

    currentMenuItems() {
      if (
        !this.routers ||
        !Array.isArray(this.routers) ||
        !this.routers[this.activeTopNavIndex]
      ) {
        return [];
      }
      return this.routers[this.activeTopNavIndex].children || [];
    },

    breadcrumbList() {
      const matched = this.getMatchedRoutes(this.$route.path, this.routers);
      // 如果没有匹配到任何路由，显示首页
      return matched && matched.length
        ? matched
        : [{ name: "首页", path: "/" }];
    },

    currentTopNavTitle() {
      return this.routers[this.activeTopNavIndex].name || "";
    },

    // 获取当前路由对应的按钮数据
    currentButtons() {
      const currentPath = this.$route.path;
      const matchedPath = Object.keys(this.buttonData).find(
        (path) => currentPath === path || currentPath.startsWith(path + "/")
      );
      const allButton = this.buttonData[matchedPath] || [];

      let topButtonData = [];
      allButton.forEach((btnItem) => {
        // 顶部按钮优先找path
        const btnIcon = this.buttonIconMap[btnItem.path];
        if (btnIcon) {
          btnItem.icon = btnIcon;
          topButtonData.push(btnItem);
        } else {
          const perBtnIcon = this.buttonIconMap[btnItem.permission];
          if (perBtnIcon) {
            btnItem.icon = perBtnIcon;
            topButtonData.push(btnItem);
          }
        }
      });
      return topButtonData;
    },
  },
  methods: {
    ...mapActions("user", ["logout", "getUserInfo"]),

    ...mapMutations({
      setIframeSrc: "publicStore/setIframeSrc",
    }),

    loadMenuData() {
      const filteredMenus = localStorage.getItem("userMenu");
      const buttonMap = localStorage.getItem("userPermissions");

      try {
        const parsedRouters = filteredMenus ? JSON.parse(filteredMenus) : [];
        const parsedButtonData = buttonMap ? JSON.parse(buttonMap) : [];

        this.$set(this, "routers", parsedRouters);
        this.$set(this, "buttonData", parsedButtonData);

        //初始化处理嵌套页面获取 模块 菜单权限标识/列表按钮标识（嵌套模块的为动态地址）
        parsedRouters.forEach((item) => {
          if (this.isHome) {
            if (/^(https?:|)\/\/./.test(item.permission)) {
              this.setIframeSrc(item.permission);
            }
          }
        });

        this.$nextTick(() => {
          this.$forceUpdate();
        });

        // 如果有菜单且当前路由是根路径，设置默认选中
        if (
          this.routers.length > 0 &&
          (this.$route.path === "/" || this.$route.path === "/home")
        ) {
          this.setDefaultActiveMenu();
        }
      } catch (error) {
        console.error("解析菜单数据失败:", error);
        this.$set(this, "routers", []);
        this.$set(this, "buttonData", []);
      }
    },

    handleMenuDataLoaded() {
      // 重新加载菜单数据
      this.loadMenuData();
      this.setActiveMenuFromRoute();
      // 关闭loading状态
      this.isMenuLoading = false;
    },

    setDefaultActiveMenu() {
      // 设置顶部第一个菜单为激活状态
      this.activeTopNavIndex = 0;

      // 获取第一个顶部菜单的第一个子菜单
      const firstTopMenu = this.routers[0];
      if (
        firstTopMenu &&
        firstTopMenu.children &&
        firstTopMenu.children.length > 0
      ) {
        const firstSubMenu = firstTopMenu.children[0];

        // 如果第一个子菜单还有子菜单，选择最深层的第一个
        let targetMenu = firstSubMenu;
        while (targetMenu.children && targetMenu.children.length > 0) {
          targetMenu = targetMenu.children[0];
        }

        // 设置激活菜单并跳转
        this.activeMenu = targetMenu.path;
        this.$router.push(targetMenu.path);
      } else if (firstTopMenu && firstTopMenu.path) {
        // 如果顶部菜单没有子菜单，直接跳转到顶部菜单
        this.activeMenu = firstTopMenu.path;
        this.$router.push(firstTopMenu.path);
      }
    },

    handleButtonClick(event) {
      this.$store.dispatch("generalEvent/triggerEvent", event.path);
    },

    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened;
    },

    getMatchedRoutes(path, routes, breadcrumbs = []) {
      for (const route of routes) {
        // 检查路径是否匹配（精确匹配或前缀匹配）
        if (path === route.path || path.startsWith(route.path + "/")) {
          const newBreadcrumbs = [
            ...breadcrumbs,
            {
              name: route.name,
              path: route.path,
            },
          ];

          // 如果有子路由，继续查找
          if (route.children) {
            const childMatch = this.getMatchedRoutes(
              path,
              route.children,
              newBreadcrumbs
            );
            if (childMatch) return childMatch;
          }

          return newBreadcrumbs;
        }
      }
      return null;
    },

    checkActive(route, index) {
      // 精确匹配当前路由
      if (this.$route.path === route.path) {
        this.activeTopNavIndex = index;
        return true;
      }
      // 匹配子路由
      if (route.children) {
        const isActive = route.children.some(
          (child) =>
            this.$route.path === child.path ||
            this.$route.path.startsWith(child.path + "/")
        );
        if (isActive) {
          this.activeTopNavIndex = index;
        }
        return isActive;
      }
      // 匹配路由前缀
      const isActive = this.$route.path.startsWith(route.path + "/");
      if (isActive) {
        this.activeTopNavIndex = index;
      }
      return isActive;
    },

    handleNavWheel(event) {
      // 阻止默认的页面滚动行为
      event.preventDefault();

      // 获取导航容器
      const navContainer = event.currentTarget;

      // 根据滚轮方向进行水平滚动
      const scrollAmount = event.deltaY > 0 ? 100 : -100;
      navContainer.scrollLeft += scrollAmount;
    },

    handleNavClick(index, navItem, event) {
      // 设置当前激活的一级导航
      this.activeTopNavIndex = index;

      // 点击动画效果
      if (event?.target) {
        const el = event.target.closest(".nav-item");
        el.style.transform = "scale(0.96)";
        setTimeout(() => (el.style.transform = ""), 100);
      }

      // 处理路由跳转
      if (navItem.children?.length) {
        if (!this.checkActive(navItem, index)) {
          this.$router.push(navItem.children[0].path);
        }
      } else if (this.$route.path !== navItem.path) {
        this.$router.push(navItem.path);
      }

      //处理嵌套页面获取 模块 菜单权限标识/列表按钮标识（嵌套模块的为动态地址）
      if (/^(https?:|)\/\/./.test(navItem.permission)) {
        this.setIframeSrc(navItem.permission);
      }
    },

    handleLogout() {
      this.$confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.logout().then(() => {
          this.$message.success("退出成功");
          this.$router.push("/login");
        });
      });
    },

    // 根据路由设置激活菜单
    setActiveMenuFromRoute() {
      const matched = this.$route.matched;
      if (matched.length) {
        this.activeMenu = matched[matched.length - 1].path;
      }
    },

    // 修改菜单选择处理
    handleMenuSelect(index) {
      this.activeMenu = index;
      // 如果点击的是有子菜单的项，不跳转
      const menuItem = this.findMenuItem(this.currentMenuItems, index);
      if (!menuItem || !menuItem.children || menuItem.children.length === 0) {
        this.$router.push(index);
      }
    },

    // 查找菜单项
    findMenuItem(menuList, path) {
      for (const item of menuList) {
        if (item.path === path) return item;
        if (item.children) {
          const found = this.findMenuItem(item.children, path);
          if (found) return found;
        }
      }
      return null;
    },
  },
};
</script>

<style scoped>
.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
  min-height: 800px;

  overflow-x: hidden;
  overflow-y: scroll;
}

/* 调整菜单项间距 */
.el-menu-item,
.el-submenu__title {
  height: 46px;
  line-height: 46px;
}

/* 激活菜单项样式 */
.el-menu-item.is-active {
  background-color: #ecf5ff !important;
  color: #409eff !important;
  font-weight: 500;
}

/* 折叠状态下调整图标位置 */
.el-menu--collapse .el-submenu__title span,
.el-menu--collapse .el-menu-item span {
  display: none;
}

.el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.app-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

/* 顶部导航栏 */
.header {
  height: 50px;
  background: var(--nav-header-bg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  color: #ffffff;
  box-shadow: var(--content-shadow);
  z-index: 1000;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-width: 0;
  max-width: 400px;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-icon {
  width: 28px;
  height: 28px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 8px;
  color: var(--icon-primary);
  border: 2px solid #ffffff;
}

.logo-text {
  min-width: 160px;
  margin-left: 5px;
  font-style: italic;
  font-family: Microsoft YaHei UI, Microsoft YaHei UI;
  font-weight: 700;
  font-size: 18px;
  color: #ffffff;
  line-height: 40px;
  letter-spacing: 2px;
  text-align: left;
  text-transform: none;
}

.header-nav {
  flex: 1;
  margin: 0 20px;
  overflow-x: auto;
  overflow-y: hidden;
  margin-bottom: -13px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.header-nav:hover {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.header-nav::-webkit-scrollbar {
  height: 4px;
  background: transparent;
}

.header-nav::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.header-nav::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 2px;
  transition: background 0.3s ease;
}

.header-nav:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.header-nav:hover::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.nav-content {
  display: flex;
  gap: 12px;
  align-items: center;
  min-width: max-content;
  animation: fadeInNav 0.6s ease-out;
  height: 100%;
  justify-content: center;
}

.nav-content.nav-loading-state {
  justify-content: center;
  align-items: center;
  height: 50px; /* 与header高度一致 */
  margin-bottom: 13px; /* 抵消header-nav的负边距 */
}

@keyframes fadeInNav {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: fadeInNav 0.6s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-loading i {
  font-size: 16px;
  animation: rotate 1s linear infinite;
}

.nav-loading span {
  font-size: 13px;
  white-space: nowrap;
}

.nav-submenu-item {
  border-width: 0;
  color: var(--themeColor) !important;
  animation: none !important;
  background: transparent !important;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  i {
    margin-top: 2px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.nav-item {
  color: #ffffff;
  text-decoration: none;
  padding: 8px 18px;
  border-radius: 6px;
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 14px;
  font-weight: 400;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
  user-select: none;
}

.nav-item:active {
  transform: scale(0.96);
  transition: transform 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-item:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-item::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: #ffffff;
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-item:hover::before {
  width: 60%;
}

.nav-item.active {
  background: var(--nav-menu-active-bg);
  font-weight: 500;
  color: var(--nav-menu-active-text);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(54, 86, 154, 0.3);
}

.nav-item.active::before {
  width: 80%;
  background: #ffffff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-shrink: 0;
  min-width: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ffffff;
  font-size: 13px;
}

.user-info i {
  font-size: 15px;
}

.user-position {
  font-size: 12px;
  opacity: 0.8;
}

.phone-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ffffff;
  font-size: 13px;
}

.phone-info i {
  font-size: 15px;
}

.notification {
  cursor: pointer;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.notification:hover {
  color: #e6f7ff;
}

.logout-btn {
  cursor: pointer;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  color: #e6f7ff;
}

/* 主体容器 */
.main-container {
  height: calc(100% - 50px); /* 减去顶部高度 */
  display: flex;
  overflow: hidden;
  background: #eff3f7;
}

/* 左侧菜单 */
.sidebar {
  height: 100%;
  flex-shrink: 0;
  background: #e0e9f7;
  border-right: 1px solid var(--table-border);
  position: relative;
  transition: width 0.3s;
  width: 200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 64px;
}

.menu-list {
  flex: 1;
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  background: transparent;
  border-right: 2px solid transparent;
  font-size: 14px;
  margin: 0;
  border-radius: 0;
  text-align: center;
}

.menu-item:hover {
  background: var(--tree-node-hover-bg);
  border-right-color: #a1c7ff;
  color: #314dc3;
  transform: translateX(-2px);
}

.menu-item.active {
  background: var(--tree-node-active-bg);
  color: var(--tree-node-active-text);
  border-right: var(--tree-node-active-border);
  box-shadow: var(--content-shadow);
  transform: translateX(-3px);
  font-weight: 500;
}

.menu-item i {
  margin-right: 10px;
  font-size: 16px;
  width: 16px;
  text-align: center;
}

.menu-item span {
  font-size: 14px;
  white-space: nowrap;
}

.sidebar-toggle {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--icon-primary);
  color: var(--content-bg);
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 1px solid var(--table-border);
  font-size: 14px;
}

.sidebar-toggle:hover {
  background: #36569a;
  transform: translateX(2px);
  box-shadow: var(--content-shadow);
}

/* 右侧内容区域 */
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; /* 确保flex子项可以缩小到比内容宽度更小 */
}

.content-wrapper.full-width {
  width: 100%;
}

.breadcrumb {
  height: 40px;
  padding: 0 16px;
  background-color: var(--breadcrumb-bg);
  border-bottom: 1px solid var(--table-border);
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.breadcrumb-icon {
  color: #409eff;
  font-size: 14px;
  margin-right: 4px;
}

.breadcrumb-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

/* 面包屑区域按钮样式优化 */
.breadcrumb-actions .el-button--small {
  padding: 6px 12px;
  font-size: 12px;
  height: 28px;
  line-height: 1;
}

.breadcrumb-actions .el-button--small [class*="el-icon-"] {
  font-size: 12px;
}

/* 面包屑样式优化 */
.breadcrumb >>> .el-breadcrumb__inner {
  color: #909399;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.breadcrumb >>> .el-breadcrumb__inner.is-link {
  color: #606266;
  font-weight: 500;
}

.breadcrumb >>> .el-breadcrumb__inner.is-link:hover {
  color: var(--icon-primary);
  text-decoration: underline;
}

/* 当前页面（最后一个面包屑项）*/
.breadcrumb >>> .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #000000;
  font-weight: 500;
}

/* 面包屑分隔符 */
.breadcrumb >>> .el-breadcrumb__separator {
  color: #c0c4cc;
  font-weight: 400;
}

.page-content {
  flex: 1;
  overflow: hidden;
  background-color: #eff3f7;
  padding: 12px;
}

.content-container {
  background: #ffffff;
  border-radius: 4px;
  height: 100%;
  overflow: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 页面切换过渡动画 - 优化版从右到左甩入效果 */
.slide-right-enter-active,
.slide-right-leave-active {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: transform, opacity;
}

.slide-right-enter-active {
  transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 2;
}

.slide-right-leave-active {
  transition: all 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  z-index: 1;
}

.slide-right-enter {
  opacity: 0;
  transform: translateX(100%) scale(0.98);
}

.slide-right-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.slide-right-leave {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-25%) scale(1.02);
}

/* 移动端优化 - 更快更流畅的动画 */
@media (max-width: 768px) {
  .slide-right-enter-active {
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .slide-right-leave-active {
    transition: all 0.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }

  .slide-right-enter {
    opacity: 0.8;
    transform: translateX(100%);
  }

  .slide-right-enter-to {
    opacity: 1;
    transform: translateX(0);
  }

  .slide-right-leave-to {
    opacity: 0.8;
    transform: translateX(-30%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-nav {
    margin: 0 10px;
  }

  .nav-content {
    gap: 8px;
  }

  .nav-item {
    padding: 6px 10px;
    font-size: 12px;
  }

  .sidebar {
    width: 64px;
  }

  .sidebar .menu-item span {
    display: none;
  }

  .sidebar-toggle {
    display: none;
  }

  .page-content {
    padding: 12px;
  }

  .content-container {
    border-radius: 4px;
  }
}
</style>
