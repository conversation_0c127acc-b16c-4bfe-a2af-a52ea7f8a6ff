<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="detailTitle"
    width="75%"
    :before-close="handleClose"
  >
    <div class="dialog-content" v-if="dialogVisible">
      <el-row class="el-row-first" :gutter="20" v-if="dialogType === 'add'">
        <el-col :span="24">
          <div class="textarea-with-btn">
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              v-model="basicInfoInput"
            />
            <el-button
              type="primary"
              size="small"
              class="submit-btn"
              @click="handleExtract"
              >提取</el-button
            >
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="120">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-col :span="12">
            <div class="title">基本信息</div>
            <el-form-item label="事件标题" prop="infoTitle">
              <el-input
                v-model="form.infoTitle"
                placeholder="请输入事件标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="事发时间" prop="infoTime">
              <el-date-picker
                type="datetime"
                placeholder="请选择事发时间"
                v-model="form.infoTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="事发地点" required>
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    prop="infoDistrict"
                    style="width: 100%; padding-right: 10px"
                  >
                    <el-select
                      v-model="form.infoDistrict"
                      placeholder="请选择区县"
                      @change="geocodingInterfaceHandle"
                    >
                      <el-option
                        v-for="item in townsBeijingList"
                        :key="item.code"
                        :label="item.townName"
                        :value="item.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" style="margin-bottom: 20px">
                  <el-form-item prop="infoTownshipStreet">
                    <el-select
                      v-model="form.infoTownshipStreet"
                      placeholder="请选择乡镇/街道"
                      @change="geocodingInterfaceHandle"
                    >
                      <el-option
                        v-for="item in subdistrictList"
                        :key="item.code"
                        :label="item.townName"
                        :value="item.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    prop="infoLocationDetail"
                    style="width: 100%; margin-bottom: 20px"
                  >
                    <el-input
                      v-model="form.infoLocationDetail"
                      placeholder="请输入详细地址"
                      @blur="geocodingInterfaceHandle"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <div class="map">
                  <tian-di-tu ref="mapRef" />
                </div>
              </el-row>
            </el-form-item>
            <el-form-item label="经纬度" required>
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="infoLongitude">
                    <el-input
                      v-model="form.infoLongitude"
                      placeholder="请输入经度"
                      style="width: 100%; padding-right: 10px"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="infoLatitude">
                    <el-input
                      v-model="form.infoLatitude"
                      placeholder="请输入纬度"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="事件类型" prop="eventType" required>
              <el-cascader
                v-model="form.eventType"
                :options="emergencyTypeList"
                :props="cascaderProps"
                style="width: 100%"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="title">伤亡信息</div>
            <el-row>
              <el-col :span="12">
                <el-form-item label="死亡人数" prop="deathNum">
                  <el-input
                    v-model.number="form.deathNum"
                    placeholder="请输入死亡人数"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="失联人数" prop="missingNum">
                  <el-input
                    v-model.number="form.missingNum"
                    placeholder="请输入失联人数"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="重伤人数" prop="severeInjuryNum">
                  <el-input
                    v-model.number="form.severeInjuryNum"
                    placeholder="请输入重伤人数"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="轻伤人数" prop="lightInjuryNum">
                  <el-input
                    v-model.number="form.lightInjuryNum"
                    placeholder="请输入轻伤人数"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="title">事件详情</div>
            <el-form-item label="事件详情" prop="eventInfo">
              <el-input
                type="textarea"
                :rows="6"
                placeholder="请输入内容"
                v-model="form.eventInfo"
              ></el-input>
            </el-form-item>
            <div class="title">其他信息</div>
            <el-form-item
              v-if="dialogType === 'add'"
              label="上报方式"
              prop="infoReportingMethod"
              required
            >
              <el-select
                v-model="form.infoReportingMethod"
                placeholder="请选择上报方式"
                style="width: 100%"
              >
                <el-option
                  v-for="item in reportMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="上报单位" prop="infoReportingUnit">
              <el-input
                v-model="form.infoReportingUnit"
                placeholder="请输入上报单位"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="上报人" prop="infoReportingUser">
              <el-input
                v-model="form.infoReportingUser"
                placeholder="请输入上报人"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-row type="flex" justify="end">
        <el-col :span="24" style="text-align: right">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script>
import TianDiTu from "@/components/TianDiTu.vue";
import { geocodingInterface } from "@/utils/tianditu";
import {
  getTownsBeijingList,
  getItemList,
  eventType,
} from "@/utils/dictionary";
import { receiveInformationApi } from "@/api";

export default {
  name: "AddDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailTitle: {
      type: String,
      default: "详情",
    },
    reportMethodOptions: {
      type: Array,
      default: () => [],
    },
    emergencyTypeList: {
      type: Array,
      default: () => [],
    },
    userInfoList: {
      type: Object,
      default: () => ({}),
    },
    dialogType: {
      type: String,
      default: "",
    },
  },
  components: {
    TianDiTu,
  },
  data() {
    return {
      basicInfoInput: "",
      townsBeijingList: [],
      eventTypeList: [],
      form: {
        infoTitle: "",
        infoTime: "",
        infoDistrict: "",
        infoTownshipStreet: "",
        infoLocationDetail: "",
        infoLongitude: "",
        infoLatitude: "",
        eventType: [],
        infoTypeValue: "",
        infoChildType: "",
        infoThirdType: "",
        deathNum: 0,
        missingNum: 0,
        severeInjuryNum: 0,
        lightInjuryNum: 0,
        eventInfo: "",
        infoReportingMethod: "",
        infoReportingUnit: "",
        infoReportingUser: "",
      },
      rules: {
        infoTitle: [
          { required: true, message: "请输入事件标题", trigger: "blur" },
        ],
        infoTime: [
          { required: true, message: "请选择事发时间", trigger: "blur" },
        ],
        infoDistrict: [
          { required: true, message: "请选择区县", trigger: "change" },
        ],
        infoTownshipStreet: [
          { required: true, message: "请选择街道", trigger: "change" },
        ],
        infoLocationDetail: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        infoLongitude: [
          { required: true, message: "请输入经度", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (isNaN(parseFloat(value)) || !isFinite(value)) {
                callback(new Error("请输入有效的数字"));
              } else {
                const numValue = parseFloat(value);
                if (numValue < -180 || numValue > 180) {
                  callback(new Error("经度必须在-180到180之间"));
                } else {
                  callback();
                }
              }
            },
            trigger: "blur",
          },
        ],
        infoLatitude: [
          { required: true, message: "请输入纬度", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (isNaN(parseFloat(value)) || !isFinite(value)) {
                callback(new Error("请输入有效的数字"));
              } else {
                const numValue = parseFloat(value);
                if (numValue < -90 || numValue > 90) {
                  callback(new Error("纬度必须在-90到90之间"));
                } else {
                  callback();
                }
              }
            },
            trigger: "blur",
          },
        ],
        eventType: [
          { required: true, message: "请选择事件类型", trigger: "change" },
        ],
        deathNum: [{ type: "number", message: "死亡人数必须为数字值" }],
        missingNum: [{ type: "number", message: "失联人数必须为数字值" }],
        severeInjuryNum: [{ type: "number", message: "重伤人数必须为数字值" }],
        lightInjuryNum: [{ type: "number", message: "轻伤人数必须为数字值" }],
        eventInfo: [
          { required: true, message: "请输入事件详情", trigger: "blur" },
        ],
        infoReportingMethod: [
          { required: true, message: "请选择上报方式", trigger: "change" },
        ],
      },
      cascaderProps: {
        value: "id", // 指定选项的值为 id 字段
        label: "name", // 指定选项标签为 name 字段
        children: "children", // 指定子选项为 children 字段
      },
    };
  },
  watch: {
    "form.infoLongitude": {
      handler(newVal) {
        this.checkCoordinates();
      },
      immediate: true,
    },
    "form.infoLatitude": {
      handler(newVal) {
        this.checkCoordinates();
      },
      immediate: true,
    },
    userInfoList: {
      immediate: true,
      handler(newVal) {
        this.form.infoReportingUnit = newVal?.orgName || "";
        this.form.infoReportingUser = newVal?.username || "";
      },
    },
  },
  computed: {
    subdistrictList() {
      if (this.form.infoDistrict) {
        if (this.dialogType === "add") {
          this.form.infoTownshipStreet = "";
        }
        return this.townsBeijingList.find(
          (item) => item.code === this.form.infoDistrict
        ).child;
      }
      return [];
    },
  },
  methods: {
    // 查询续报详情
    queryReportInfo(id) {
      try {
        receiveInformationApi.queryReportInfo({ id }).then((res) => {
          this.form = res?.data || {};
          this.form.eventType = [
            this.form.infoTypeValue,
            this.form.infoChildType,
            this.form.infoThirdType,
          ];
          this.form.infoReportingUnit = this.userInfoList.orgName || "";
          this.form.infoReportingUser = this.userInfoList.username || "";
        });
      } catch (error) {}
    },
    getTownsBeijingList() {
      getTownsBeijingList().then((data) => {
        this.townsBeijingList = data || [];
      });
    },
    getItemList() {
      getItemList(eventType).then((data) => {
        this.eventTypeList = data || [];
      });
    },
    handleExtract() {},
    resetForm() {
      this.form = {
        infoTitle: "",
        infoTime: "",
        infoDistrict: "",
        infoTownshipStreet: "",
        infoLocationDetail: "",
        infoLongitude: "",
        infoLatitude: "",
        eventType: [],
        infoTypeValue: "",
        infoChildType: "",
        infoThirdType: "",
        deathNum: 0,
        missingNum: 0,
        severeInjuryNum: 0,
        lightInjuryNum: 0,
        eventInfo: "",
        infoReportingMethod: "",
        infoReportingUnit: this.userInfoList.orgName || "",
        infoReportingUser: this.userInfoList.username || "",
      };
      this.basicInfoInput = "";
    },
    handleClose() {
      this.resetForm();
      this.$emit("close");
    },
    handleSubmit() {
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType === "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          [
            this.form.infoTypeValue,
            this.form.infoChildType,
            this.form.infoThirdType,
          ] = this.form.eventType;
          this.form.infoReportingUnit = this.userInfoList.orgId || "";
          this.form.infoReportingUser = this.userInfoList.id || "";
          try {
            receiveInformationApi.createReportInfo(this.form).then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                });
                this.$emit("submitSuccess");
                this.handleClose();
              }
            });
          } catch (error) {}
        } else {
          console.log("表单验证失败");
        }
      });
    },
    editSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          [
            this.form.infoTypeValue,
            this.form.infoChildType,
            this.form.infoThirdType,
          ] = this.form.eventType;
          this.form.infoReportingUnit = this.userInfoList.orgId || "";
          this.form.infoReportingUser = this.userInfoList.id || "";
          try {
            receiveInformationApi.updateReportInfo(this.form).then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                });
                this.$emit("submitSuccess");
                this.handleClose();
              }
            });
          } catch (error) {}
        } else {
          console.log("表单验证失败");
        }
      });
    },
    checkCoordinates() {
      if (this.form.infoLongitude && this.form.infoLatitude) {
        this.$refs.mapRef.addMarker(
          this.form.infoLongitude,
          this.form.infoLatitude
        );
      }
    },
    geocodingInterfaceHandle() {
      if (
        this.form.infoDistrict &&
        this.form.infoTownshipStreet &&
        this.form.infoLocationDetail
      ) {
        const district = this.townsBeijingList.find(
          (item) => item.code === this.form.infoDistrict
        )?.townName;
        const street = this.subdistrictList.find(
          (item) => item.code === this.form.infoTownshipStreet
        )?.townName;
        if (!district || !street) return;
        geocodingInterface(
          `${district}${street}${this.form.infoLocationDetail}`
        ).then((res) => {
          this.form.infoLongitude = res?.location?.lon;
          this.form.infoLatitude = res?.location?.lat;
        });
      }
    },
  },
  mounted() {
    this.getTownsBeijingList();
  },
};
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: 80vh;
  overflow-y: auto;
  padding-right: 10px;
  padding-bottom: 20px;
  overflow-x: hidden;
}
.textarea-with-btn {
  position: relative;
}

.submit-btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 1;
}
.title {
  position: relative;
  padding-left: 20px;
  margin: 0 0 20px 0;
  font-size: 16px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -2px;
    height: 120%;
    width: 2px;
    background-color: #409eff;
  }
}
.el-row-first {
  margin-bottom: 20px;
}
.map {
  width: 100%;
  height: 200px;
  border: 1px solid #409eff;
  border-radius: 4px;
}
::v-deep .el-dialog {
  margin: 5vh auto 0 !important;
}
</style>