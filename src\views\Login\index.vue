<template>
  <div class="login-container">
    <!-- 登录 Page -->
    <div class="login-wrapper">
      <!-- 左侧背景区域 -->
      <div class="login-bg">
        <div class="bg-content">
          <h2 class="system-title">{{ systemInfo.title }}</h2>
          <div class="feature-list">
            <div class="feature-item">
              <i class="el-icon-lightning"></i>
              <span>快速响应</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-connection"></i>
              <span>智能调度</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-wrapper">
        <div class="login-form">
          <div class="form-header">
            <h2>用户登录</h2>
            <p>请选择登录方式</p>
          </div>

          <!-- 登录方式切换标签 -->
          <el-tabs
            v-model="loginMode"
            class="login-tabs"
            @tab-click="handleTabChange"
          >
            <el-tab-pane label="账号密码登录" name="password">
              <el-form
                ref="loginForm"
                :model="loginForm"
                :rules="loginRules"
                class="login-form-content"
                @keyup.enter.native="handleLogin"
              >
                <el-form-item prop="username">
                  <el-input
                    v-model="loginForm.username"
                    placeholder="请输入用户名"
                    prefix-icon="el-icon-user"
                    size="large"
                    clearable
                  />
                </el-form-item>

                <el-form-item prop="password">
                  <el-input
                    v-model="loginForm.password"
                    type="password"
                    placeholder="请输入密码"
                    prefix-icon="el-icon-lock"
                    size="large"
                    show-password
                    clearable
                  />
                </el-form-item>

                <el-form-item prop="captcha" v-if="showCaptcha">
                  <div class="captcha-wrapper">
                    <el-input
                      v-model="loginForm.captcha"
                      placeholder="请输入验证码"
                      prefix-icon="el-icon-key"
                      size="large"
                      clearable
                    />
                    <div class="captcha-image" @click="refreshCaptcha">
                      <img :src="captchaImage" alt="验证码" />
                      <span class="refresh-tip">点击刷新</span>
                    </div>
                  </div>
                </el-form-item>

                <el-form-item>
                  <div class="form-options">
                    <div class="remember-option">
                      <el-checkbox v-model="loginForm.remember">
                        记住密码
                      </el-checkbox>
                      <span class="remember-tip"
                        >（关闭浏览器后仍保持登录）</span
                      >
                    </div>
                    <el-link
                      type="primary"
                      :underline="false"
                      @click="handleForgotPassword"
                    >
                      忘记密码？
                    </el-link>
                  </div>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    size="large"
                    :loading="loading"
                    @click="handleLogin"
                    class="login-btn"
                  >
                    {{ loading ? "登录中..." : "登录" }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- IP免密登录标签页 -->
            <el-tab-pane label="IP免密登录" name="ip">
              <div class="ip-login-content">
                <!-- IP检测区域 -->
                <div class="ip-detection-area">
                  <div class="ip-status">
                    <div v-if="ipDetecting" class="ip-detecting">
                      <i class="el-icon-loading"></i>
                      <span>正在检测IP地址...</span>
                    </div>
                    <div v-else-if="currentIP" class="ip-detected">
                      <div class="ip-display">
                        <i class="el-icon-location-outline"></i>
                        <span class="ip-text">{{ currentIP }}</span>
                      </div>
                      <div class="ip-info" v-if="ipResult && ipResult.details">
                        {{ formatIPLocation(ipResult.details) }}
                      </div>
                    </div>
                    <div v-else-if="ipDetectError" class="ip-error">
                      <i class="el-icon-warning-outline"></i>
                      <span>{{ ipDetectError }}</span>
                    </div>
                  </div>

                  <!-- IP操作按钮 -->
                  <div class="ip-actions">
                    <el-button
                      v-if="!currentIP || ipDetectError"
                      @click="detectIP"
                      :loading="ipDetecting"
                      size="medium"
                      icon="el-icon-refresh"
                    >
                      {{ ipDetectError ? "重新检测" : "检测IP地址" }}
                    </el-button>
                    <el-button
                      v-if="currentIP && !ipDetectError"
                      @click="detectIP"
                      :loading="ipDetecting"
                      size="medium"
                      icon="el-icon-refresh"
                      plain
                    >
                      重新检测
                    </el-button>
                    <el-button
                      v-if="ipDetectError"
                      @click="switchToManualIP"
                      size="medium"
                      type="text"
                    >
                      手动输入IP
                    </el-button>
                  </div>

                  <!-- 手动输入IP -->
                  <div v-if="manualIPInput" class="manual-ip-input">
                    <el-input
                      v-model="manualIP"
                      placeholder="请输入IP地址，如：*************"
                      size="large"
                      clearable
                      @input="validateManualIP"
                    >
                      <template slot="prepend">IP地址</template>
                    </el-input>
                    <div v-if="manualIPError" class="manual-ip-error">
                      {{ manualIPError }}
                    </div>
                  </div>
                </div>

                <!-- IP登录选项 -->
                <div class="ip-login-options">
                  <div class="remember-option">
                    <el-checkbox v-model="ipRemember">记住登录状态</el-checkbox>
                    <span class="remember-tip">（关闭浏览器后仍保持登录）</span>
                  </div>
                </div>

                <!-- IP登录按钮 -->
                <div class="ip-login-actions">
                  <el-button
                    type="primary"
                    size="large"
                    :loading="loading"
                    :disabled="!getLoginIP()"
                    @click="handleIPLogin"
                    class="login-btn"
                  >
                    {{ loading ? "登录中..." : "IP免密登录" }}
                  </el-button>
                  <div class="ip-login-tip">
                    <i class="el-icon-info"></i>
                    <span>使用IP免密登录需要管理员预先配置IP白名单</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 短信验证码登录标签页 -->
            <el-tab-pane label="短信验证码登录" name="sms">
              <el-form
                ref="smsForm"
                :model="smsForm"
                :rules="smsRules"
                class="login-form-content"
                @keyup.enter.native="handleSmsLogin"
              >
                <el-form-item prop="phone">
                  <el-input
                    v-model="smsForm.phone"
                    placeholder="请输入手机号"
                    prefix-icon="el-icon-phone"
                    size="large"
                    clearable
                    maxlength="11"
                  />
                </el-form-item>

                <el-form-item prop="code">
                  <div class="sms-code-wrapper">
                    <el-input
                      v-model="smsForm.code"
                      placeholder="请输入验证码"
                      prefix-icon="el-icon-key"
                      size="large"
                      clearable
                      maxlength="6"
                    />
                    <el-button
                      :disabled="!canSendSms || smsCountdown > 0"
                      :loading="sendingSms"
                      @click="handleSendSmsCode"
                      size="large"
                      class="sms-btn"
                    >
                      {{ getSmsButtonText }}
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item>
                  <div class="remember-option">
                    <el-checkbox v-model="smsForm.remember">
                      记住登录状态
                    </el-checkbox>
                    <span class="remember-tip">（关闭浏览器后仍保持登录）</span>
                  </div>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    size="large"
                    :loading="loading"
                    @click="handleSmsLogin"
                    class="login-btn"
                  >
                    {{ loading ? "登录中..." : "短信登录" }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>

          <div class="form-footer">
            <p class="copyright">{{ systemInfo.copyright }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { SYSTEM_INFO } from "@/constants";
import { auth } from "@/utils";
import { getIPWithFallback, validateIPAddress } from "@/utils/ipUtils";

export default {
  name: "Login",
  data() {
    // 用户名验证
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入用户名"));
      } else if (value.length < 2) {
        callback(new Error("用户名长度不能少于2位"));
      } else {
        callback();
      }
    };

    // 密码验证
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入密码"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能少于6位"));
      } else {
        callback();
      }
    };

    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入手机号"));
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error("请输入正确的手机号"));
      } else {
        callback();
      }
    };

    // 验证码验证
    const validateSmsCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入验证码"));
      } else if (!/^\d{6}$/.test(value)) {
        callback(new Error("验证码为6位数字"));
      } else {
        callback();
      }
    };

    return {
      systemInfo: {
        title: SYSTEM_INFO.TITLE,
        copyright: SYSTEM_INFO.COPYRIGHT,
      },
      loading: false,
      showCaptcha: false,
      captchaImage: "",
      loginAttempts: 0,

      // 登录模式相关
      loginMode: "password", // 'password' | 'sms' | 'ip'
      currentIP: "", // 当前检测到的IP地址
      ipDetecting: false, // IP检测中状态
      ipDetectError: "", // IP检测错误信息
      ipResult: null, // IP检测结果对象
      manualIPInput: false, // 是否显示手动输入IP
      manualIP: "", // 手动输入的IP地址
      manualIPError: "", // 手动输入IP的错误信息

      loginForm: {
        username: "",
        password: "",
        captcha: "",
        remember: true,
      },

      // 短信登录表单
      smsForm: {
        phone: "",
        code: "",
        remember: true, // 默认记住登录状态
      },

      // 短信相关状态
      sendingSms: false, // 发送验证码中
      smsCountdown: 0, // 验证码倒计时
      smsTimer: null, // 倒计时定时器

      // IP登录状态
      ipRemember: true, // IP登录记住状态，默认为true

      loginRules: {
        username: [{ validator: validateUsername, trigger: "blur" }],
        password: [{ validator: validatePassword, trigger: "blur" }],
        captcha: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { min: 4, max: 4, message: "验证码长度为4位", trigger: "blur" },
        ],
      },

      smsRules: {
        phone: [{ validator: validatePhone, trigger: "blur" }],
        code: [{ validator: validateSmsCode, trigger: "blur" }],
      },
    };
  },

  computed: {
    // 是否可以发送短信验证码
    canSendSms() {
      return /^1[3-9]\d{9}$/.test(this.smsForm.phone);
    },

    // 短信按钮文字
    getSmsButtonText() {
      if (this.sendingSms) {
        return "发送中...";
      }
      if (this.smsCountdown > 0) {
        return `${this.smsCountdown}s后重发`;
      }
      return "获取验证码";
    },
  },

  mounted() {
    this.initLogin();
  },

  beforeDestroy() {
    // 清理短信倒计时定时器
    this.clearSmsCountdown();
  },

  methods: {
    ...mapActions("user", [
      "login",
      "getUserInfo",
      "ipLogin",
      "sendSmsCode",
      "smsLogin",
    ]),

    initLogin() {
      // 检查URL参数中的错误信息
      const errorType = this.$route.query.error;
      if (errorType === "menu_load_failed") {
        this.$message.error({
          message: "用户权限配置异常，请联系管理员检查菜单权限配置后重新登录",
          duration: 8000,
          showClose: true,
        });
        // 清除错误参数
        this.$router.replace({ path: "/login", query: {} });
      }

      // 检查是否已登录
      if (auth.isLoggedIn()) {
        // 避免重复导航，只在当前不是根路径时才跳转
        if (this.$route.path !== "/") {
          this.$router.replace("/");
        }
        return;
      }

      // 始终生成验证码
      this.generateCaptcha();

      // 检查是否需要显示验证码
      this.checkCaptchaRequired();

      // 加载记住的用户名
      this.loadRememberedCredentials();
    },

    checkCaptchaRequired() {
      // 登录失败次数超过3次显示验证码
      const attempts = localStorage.getItem("loginAttempts") || 0;
      if (parseInt(attempts) >= 3) {
        this.showCaptcha = true;
        this.generateCaptcha();
      }
    },

    loadRememberedCredentials() {
      const remembered = localStorage.getItem("rememberedCredentials");
      if (remembered) {
        try {
          const credentials = JSON.parse(remembered);
          this.loginForm.username = credentials.username || "";
          this.loginForm.remember = true;
        } catch (error) {
          // 静默处理错误
        }
      }
    },

    generateCaptcha() {
      const canvas = document.createElement("canvas");
      canvas.width = 120;
      canvas.height = 48;
      const ctx = canvas.getContext("2d");

      // 生成随机验证码
      const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      let captchaText = "";
      for (let i = 0; i < 4; i++) {
        captchaText += chars.charAt(Math.floor(Math.random() * chars.length));
      }

      // 创建渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 120, 48);
      gradient.addColorStop(0, "#f8f9fa");
      gradient.addColorStop(1, "#e9ecef");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 120, 48);

      // 绘制验证码文字
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      // 为每个字符设置不同的颜色和角度
      for (let i = 0; i < captchaText.length; i++) {
        const char = captchaText[i];
        const x = 20 + i * 20;
        const y = 24;

        // 随机颜色（深色系）
        const colors = [
          "#2c3e50",
          "#34495e",
          "#7f8c8d",
          "#95a5a6",
          "#16a085",
          "#27ae60",
        ];
        ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];

        // 随机字体大小和样式
        const fontSize = 16 + Math.random() * 4;
        ctx.font = `bold ${fontSize}px Arial, sans-serif`;

        // 保存当前状态
        ctx.save();

        // 移动到字符位置并旋转
        ctx.translate(x, y);
        ctx.rotate((Math.random() - 0.5) * 0.4); // 随机旋转角度

        // 绘制字符
        ctx.fillText(char, 0, 0);

        // 恢复状态
        ctx.restore();
      }

      for (let i = 0; i < 3; i++) {
        ctx.strokeStyle = `rgba(${100 + Math.random() * 100}, ${
          100 + Math.random() * 100
        }, ${100 + Math.random() * 100}, 0.3)`;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(Math.random() * 120, Math.random() * 48);
        ctx.bezierCurveTo(
          Math.random() * 120,
          Math.random() * 48,
          Math.random() * 120,
          Math.random() * 48,
          Math.random() * 120,
          Math.random() * 48
        );
        ctx.stroke();
      }

      // 添加干扰点
      for (let i = 0; i < 20; i++) {
        ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${
          Math.random() * 255
        }, 0.3)`;
        ctx.beginPath();
        ctx.arc(Math.random() * 120, Math.random() * 48, 1, 0, 2 * Math.PI);
        ctx.fill();
      }

      this.captchaImage = canvas.toDataURL();
      this.correctCaptcha = captchaText;
    },

    refreshCaptcha() {
      if (this.showCaptcha) {
        this.generateCaptcha();
        this.loginForm.captcha = "";
      }
    },

    async handleLogin() {
      try {
        // 表单验证
        await this.$refs.loginForm.validate();

        // 验证码验证
        if (this.showCaptcha) {
          if (!this.loginForm.captcha) {
            this.$message.error("请输入验证码");
            return;
          }
          if (this.loginForm.captcha.toUpperCase() !== this.correctCaptcha) {
            this.$message.error("验证码错误");
            this.refreshCaptcha();
            return;
          }
        }

        this.loading = true;

        // 调用登录接口
        const loginData = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          captcha: this.loginForm.captcha,
          remember: this.loginForm.remember, // 传递记住我参数
        };

        await this.login(loginData);

        // 用户信息将由路由守卫自动获取

        // 处理记住密码
        if (this.loginForm.remember) {
          localStorage.setItem(
            "rememberedCredentials",
            JSON.stringify({
              username: this.loginForm.username,
            })
          );
        } else {
          localStorage.removeItem("rememberedCredentials");
        }

        // 清除登录失败次数
        localStorage.removeItem("loginAttempts");

        this.$message.success("登录成功");

        // 跳转到首页或之前访问的页面
        const redirect = this.$route.query.redirect || "/";
        // 避免重复导航，只在目标路径与当前路径不同时才跳转
        if (this.$route.path !== redirect) {
          this.$router.push(redirect);
        }
      } catch (error) {
        this.handleLoginError(error);
      } finally {
        this.loading = false;
      }
    },

    handleLoginError(error) {
      console.error("登录错误:", error);

      // 检查是否是菜单权限相关错误
      if (error.message && error.message.includes("用户没有分配任何菜单权限")) {
        this.$message.error({
          message: "用户没有分配任何菜单权限，请联系管理员配置权限",
          duration: 5000,
          showClose: true,
        });
        return;
      }

      // 显示具体的错误信息
      this.$message.error(error.message || "登录失败，请重试");

      // 增加登录失败次数
      this.loginAttempts++;
      localStorage.setItem("loginAttempts", this.loginAttempts.toString());

      // 失败3次后显示验证码
      if (this.loginAttempts >= 3 && !this.showCaptcha) {
        this.showCaptcha = true;
        this.generateCaptcha();
      }

      // 刷新验证码
      if (this.showCaptcha) {
        this.refreshCaptcha();
      }
    },

    handleForgotPassword() {
      this.$message.info("请联系系统管理员重置密码");
    },

    // IP登录相关方法
    handleTabChange(tab) {
      // 切换到IP登录时自动检测IP
      if (tab.name === "ip" && !this.currentIP && !this.ipDetecting) {
        this.detectIP();
      }
    },

    async detectIP() {
      this.ipDetecting = true;
      this.ipDetectError = "";
      this.currentIP = "";
      this.ipResult = null;
      this.manualIPInput = false;
      this.manualIP = "";
      this.manualIPError = "";

      try {
        // console.log("开始检测IP地址");
        const result = await getIPWithFallback();
        // console.log("IP检测成功:", result);

        this.currentIP = result.ip;
        this.ipResult = result;
        // this.$message.success(`IP检测成功: ${result.ip}`);
      } catch (error) {
        console.error("IP检测失败:", error);
        this.ipDetectError = error.message || "IP检测失败，请重试或手动输入";
        this.$message.error(this.ipDetectError);
      } finally {
        this.ipDetecting = false;
      }
    },

    switchToManualIP() {
      this.manualIPInput = true;
      this.manualIP = "";
      this.manualIPError = "";
    },

    validateManualIP() {
      if (!this.manualIP) {
        this.manualIPError = "";
        return;
      }

      if (!validateIPAddress(this.manualIP)) {
        this.manualIPError = "请输入有效的IPv4地址";
      } else {
        this.manualIPError = "";
      }
    },

    getLoginIP() {
      if (this.manualIPInput && this.manualIP && !this.manualIPError) {
        return this.manualIP;
      }
      return this.currentIP;
    },

    formatIPLocation(details) {
      if (!details) return "";
      const { country, prov, city, district, isp } = details;
      let location = "";
      if (country) location += country;
      if (prov) location += prov;
      if (city) location += city;
      if (district) location += district;
      if (isp) location += ` [${isp}]`;
      return location;
    },

    async handleIPLogin() {
      try {
        const ipAddress = this.getLoginIP();
        if (!ipAddress) {
          this.$message.error("请先检测或输入IP地址");
          return;
        }

        if (!validateIPAddress(ipAddress)) {
          this.$message.error("IP地址格式不正确");
          return;
        }

        this.loading = true;

        // 调用IP登录接口
        await this.ipLogin({
          ipAddress: ipAddress,
          remember: this.ipRemember,
        });

        // 用户信息将由路由守卫自动获取

        // 清除登录失败次数
        localStorage.removeItem("loginAttempts");

        this.$message.success("IP免密登录成功");

        // 跳转到首页或之前访问的页面
        const redirect = this.$route.query.redirect || "/";
        if (this.$route.path !== redirect) {
          this.$router.push(redirect);
        }
      } catch (error) {
        this.handleIPLoginError(error);
      } finally {
        this.loading = false;
      }
    },

    // 发送短信验证码
    async handleSendSmsCode() {
      try {
        // 验证手机号
        if (!this.canSendSms) {
          this.$message.error("请输入正确的手机号");
          return;
        }

        this.sendingSms = true;

        // 调用发送验证码接口
        await this.sendSmsCode(this.smsForm.phone);

        this.$message.success("验证码发送成功，请注意查收");

        // 开始倒计时
        this.startSmsCountdown();
      } catch (error) {
        console.error("发送验证码失败:", error);
        this.$message.error(error.message || "发送验证码失败，请稍后重试");
      } finally {
        this.sendingSms = false;
      }
    },

    // 开始短信倒计时
    startSmsCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          this.clearSmsCountdown();
        }
      }, 1000);
    },

    // 清除短信倒计时
    clearSmsCountdown() {
      if (this.smsTimer) {
        clearInterval(this.smsTimer);
        this.smsTimer = null;
      }
      this.smsCountdown = 0;
    },

    // 短信验证码登录
    async handleSmsLogin() {
      try {
        // 表单验证
        await this.$refs.smsForm.validate();

        this.loading = true;

        // 调用短信登录接口
        await this.smsLogin({
          phone: this.smsForm.phone,
          code: this.smsForm.code,
          remember: this.smsForm.remember,
        });

        // 用户信息将由路由守卫自动获取

        // 清除登录失败次数
        localStorage.removeItem("loginAttempts");

        this.$message.success("短信登录成功");

        // 跳转到首页或之前访问的页面
        const redirect = this.$route.query.redirect || "/";
        if (this.$route.path !== redirect) {
          this.$router.push(redirect);
        }
      } catch (error) {
        this.handleSmsLoginError(error);
      } finally {
        this.loading = false;
      }
    },

    // 处理短信登录错误
    handleSmsLoginError(error) {
      console.error("短信登录错误:", error);

      let errorMessage = error.message || "短信登录失败";

      // 根据错误信息提供更友好的提示
      if (
        errorMessage.includes("验证码错误") ||
        errorMessage.includes("验证码不正确")
      ) {
        errorMessage = "验证码错误，请重新输入";
      } else if (errorMessage.includes("验证码过期")) {
        errorMessage = "验证码已过期，请重新获取";
      } else if (errorMessage.includes("手机号未注册")) {
        errorMessage = "该手机号未注册，请联系管理员";
      }

      this.$message.error(errorMessage);

      // 如果是验证码相关错误，清空验证码输入框
      if (errorMessage.includes("验证码")) {
        this.smsForm.code = "";
      }
    },

    handleIPLoginError(error) {
      console.error("IP登录错误:", error);

      let errorMessage = error.message || "IP登录失败";

      // 根据错误信息提供更友好的提示
      if (
        errorMessage.includes("IP地址未授权") ||
        errorMessage.includes("IP不在白名单")
      ) {
        errorMessage = "当前IP地址未授权，请联系管理员添加IP白名单";
      } else if (errorMessage.includes("IP登录已禁用")) {
        errorMessage = "IP免密登录功能已禁用，请使用账号密码登录";
      }

      this.$message.error(errorMessage);

      // 建议切换到账号密码登录
      if (errorMessage.includes("未授权") || errorMessage.includes("禁用")) {
        this.$confirm("是否切换到账号密码登录？", "提示", {
          confirmButtonText: "切换",
          cancelButtonText: "取消",
          type: "info",
        })
          .then(() => {
            this.loginMode = "password";
          })
          .catch(() => {
            // 用户取消
          });
      }
    },
  },
};
</script>

<style scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-wrapper {
  width: 100%;
  max-width: 1000px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
}

/* 左侧背景区域 */
.login-bg {
  flex: 1;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
}

.login-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.bg-content {
  text-align: center;
  z-index: 1;
  position: relative;
}

.system-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-desc {
  font-size: 16px;
  margin-bottom: 40px;
  opacity: 0.9;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 16px;
}

.feature-item i {
  font-size: 24px;
  opacity: 0.8;
}

/* 右侧登录表单 */
.login-form-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-header h2 {
  font-size: 28px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

.login-form-content {
  margin-bottom: 20px;
}

.login-form-content .el-form-item {
  margin-bottom: 24px;
}

.login-form-content .el-input__inner {
  height: 48px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 14px;
}

.login-form-content .el-input__inner:focus {
  border-color: #4a90e2;
  box-shadow: none;
}

.captcha-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-wrapper .el-input {
  flex: 1;
}

/* 短信验证码输入框样式 */
.sms-code-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sms-code-wrapper .el-input {
  flex: 1;
}

.sms-btn {
  width: 120px;
  white-space: nowrap;
}

/* 记住登录状态选项样式 */
.remember-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.remember-tip {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

/* IP登录选项样式 */
.ip-login-options {
  margin-bottom: 20px;
}

.ip-login-options .el-checkbox {
  color: #606266;
}

.captcha-image {
  width: 120px;
  height: 48px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: #f8f9fa;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-image:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
  transform: translateY(-1px);
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  transition: all 0.3s ease;
}

/* 半透明遮罩层 */
.captcha-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(74, 144, 226, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 6px;
  z-index: 1;
}

.captcha-image:hover::before {
  opacity: 1;
}

.captcha-image:hover img {
  filter: brightness(0.9);
}

.captcha-image .refresh-tip {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 11px;
  text-align: center;
  padding: 4px 2px;
  opacity: 0;
  transition: all 0.3s ease;
  font-weight: 500;
  letter-spacing: 0.5px;
  z-index: 2;
  backdrop-filter: blur(2px);
}

.captcha-image:hover .refresh-tip {
  opacity: 1;
  transform: translateY(0);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border: none;
}

.login-btn:hover {
  background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
}

.form-footer {
  text-align: center;
  margin-top: 30px;
}

.copyright {
  color: #999;
  font-size: 12px;
}

/* IP登录相关样式 */
.login-tabs {
  margin-bottom: 20px;
}

.login-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.login-tabs .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #e4e7ed;
}

.login-tabs .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.login-tabs .el-tabs__item.is-active {
  color: #4a90e2;
}

.login-tabs .el-tabs__active-bar {
  background-color: #4a90e2;
}

.ip-login-content {
  padding: 10px 0;
}

.ip-detection-area {
  margin-bottom: 30px;
}

.ip-status {
  margin-bottom: 20px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ip-detecting {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a90e2;
  font-size: 14px;
}

.ip-detecting i {
  font-size: 16px;
  animation: rotating 1s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ip-detected {
  text-align: center;
}

.ip-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.ip-display i {
  color: #4a90e2;
  font-size: 18px;
}

.ip-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "Courier New", monospace;
}

.ip-info {
  font-size: 12px;
  color: #666;
}

.ip-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #f56c6c;
  font-size: 14px;
}

.ip-error i {
  font-size: 16px;
}

.ip-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.manual-ip-input {
  margin-top: 20px;
}

.manual-ip-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
  text-align: center;
}

.ip-login-actions {
  text-align: center;
}

.ip-login-tip {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #909399;
  font-size: 12px;
}

.ip-login-tip i {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    height: auto;
    max-width: 400px;
  }

  .login-bg {
    height: 200px;
    flex: none;
  }

  .system-title {
    font-size: 24px;
  }

  .feature-list {
    flex-direction: row;
    justify-content: center;
    gap: 30px;
  }

  .feature-item {
    flex-direction: column;
    gap: 8px;
  }

  .login-form-wrapper {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .captcha-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .captcha-image {
    width: 100%;
  }

  .ip-actions {
    flex-direction: column;
    align-items: center;
  }

  .ip-actions .el-button {
    width: 100%;
    max-width: 200px;
  }

  .manual-ip-input .el-input-group__prepend {
    width: 80px;
  }
}
</style>
