<template>
  <div class="full-report-editor">
    <!-- 全文报告编辑组件 - Component -->

    <!-- 标题编辑区 -->
    <div class="title-section">
      <div class="section-label">标题</div>
      <el-input
        v-if="isEditMode"
        v-model="localData.title"
        placeholder="请输入标题"
        class="title-input"
        @input="handleDataChange"
      />
      <div v-else class="title-display">
        {{ localData.title }}
      </div>
    </div>

    <!-- 事件内容编辑区 -->
    <div class="events-section">
      <div class="section-label">事件内容</div>
      <div
        v-for="(event, index) in localData.events"
        :key="index"
        class="event-item"
      >
        <div class="event-line">
          <span class="event-number">{{ event.number }}</span>
          <el-input
            v-if="isEditMode"
            v-model="event.content"
            type="textarea"
            :rows="2"
            placeholder="请输入事件内容"
            @input="handleDataChange"
            class="event-content-input"
          />
          <span v-else class="event-content">{{ event.content }}</span>
          <span class="location-tag">{{ event.location }}</span>
        </div>
      </div>
    </div>

    <!-- 数据表格区 -->
    <div class="table-section">
      <div class="section-label">相关数据</div>
      <editable-table
        :data="localData.tableData"
        :columns="tableColumns"
        @cell-change="handleTableCellChange"
        @data-change="handleTableDataChange"
      />
    </div>

    <!-- 附件管理区 -->
    <div class="attachment-section">
      <div class="section-label">附件管理</div>
      <editable-table
        :data="localData.attachments"
        :columns="attachmentColumns"
        @cell-change="handleAttachmentCellChange"
        @data-change="handleAttachmentDataChange"
      />
    </div>
  </div>
</template>

<script>
import EditableTable from "@/components/EditableTable/index.vue";

export default {
  name: "FullReportEditor",
  components: {
    EditableTable,
  },
  props: {
    // 编辑数据
    editData: {
      type: Object,
      required: true,
    },
    // 是否编辑模式
    isEditMode: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      localData: {},

      // 数据表格列配置
      tableColumns: [
        {
          prop: "name",
          label: "项目名称",
          editable: true,
          minWidth: 120,
        },
        {
          prop: "value",
          label: "数值",
          editable: true,
          minWidth: 80,
        },
        {
          prop: "unit",
          label: "单位",
          editable: true,
          minWidth: 60,
        },
        {
          prop: "date",
          label: "统计日期",
          editable: true,
          minWidth: 100,
        },
        {
          prop: "status",
          label: "状态",
          editable: true,
          minWidth: 80,
        },
      ],

      // 附件表格列配置
      attachmentColumns: [
        {
          prop: "name",
          label: "文件名称",
          editable: true,
          minWidth: 150,
        },
        {
          prop: "type",
          label: "文件类型",
          editable: true,
          minWidth: 80,
        },
        {
          prop: "size",
          label: "文件大小",
          editable: false,
          minWidth: 80,
        },
        {
          prop: "uploadTime",
          label: "上传时间",
          editable: false,
          minWidth: 120,
        },
      ],
    };
  },
  watch: {
    editData: {
      handler(newData) {
        this.localData = JSON.parse(JSON.stringify(newData));
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 数据变更处理
    handleDataChange() {
      this.$emit("data-change", this.localData);
    },

    // 表格单元格变更
    handleTableCellChange(rowIndex, prop, newValue, oldValue) {
      console.log("表格数据变更:", { rowIndex, prop, newValue, oldValue });
      this.handleDataChange();
    },

    // 表格数据变更
    handleTableDataChange(newData) {
      this.localData.tableData = newData;
      this.handleDataChange();
    },

    // 附件单元格变更
    handleAttachmentCellChange(rowIndex, prop, newValue, oldValue) {
      console.log("附件数据变更:", { rowIndex, prop, newValue, oldValue });
      this.handleDataChange();
    },

    // 附件数据变更
    handleAttachmentDataChange(newData) {
      this.localData.attachments = newData;
      this.handleDataChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.full-report-editor {
  width: 100%;
}

.section-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-left: 8px;
  border-left: 3px solid #409eff;
}

.title-section,
.events-section,
.table-section,
.attachment-section {
  margin-bottom: 24px;
}

.title-input {
  font-size: 18px;
  font-weight: bold;
}

.title-display {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  padding: 8px 0;
}

.event-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.event-line {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.event-number {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  min-width: 24px;
  text-align: center;
  flex-shrink: 0;
  margin-top: 8px;
}

.event-content-input {
  flex: 1;
}

.event-content {
  flex: 1;
  line-height: 1.6;
  color: #606266;
  padding: 8px 0;
}

.location-tag {
  background: #f0f9ff;
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  margin-top: 8px;
}

// 表格样式
.table-section,
.attachment-section {
  ::v-deep .el-table {
    width: 100%;
  }

  ::v-deep .el-table__body-wrapper {
    width: 100%;
  }
}
</style>
