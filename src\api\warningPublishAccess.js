/**
 * 预警接入相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";

export default {
    // 获取预警接入分页列表
    async getWarningAccessList(params = {}) {
        return request({
            url: '/ds/smsTemplate/queryList',
            method: 'get',
            params: params
        })
    },
    // 获取预警接入不分页列表
    async getWarningAccessListAll(params = {}) {
        return request({
            url: '/ds/smsTemplate/queryAll',
            method: 'get',
            params: params
        })
    },
    // 新增预警接入
    async createWarningAccess(params = {}) {
        return request({
            url: '/ds/smsTemplate/create',
            method: 'post',
            data: params
        })
    },
    // 更新预警接入
    async updateWarningAccess(params = {}) {
        return request({
            url: '/ds/smsTemplate/update',
            method: 'post',
            data: params
        })
    },
    // 获取预警接入详情
    async queryWarningAccessById(params = {}) {
        return request({
            url: '/ds/smsTemplate/queryById',
            method: 'post',
            data: params
        })
    },
    // 删除预警接入
    async removeWarningAccess(params = {}) {
        return request({
            url: '/ds/smsTemplate/deleteById',
            method: 'post',
            data: params
        })
    },
};
