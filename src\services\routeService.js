/**
 * 路由服务 - 统一管理动态路由的初始化和更新
 */

import RouteManager from "@/utils/routeManager";
import permissionManager from "@/utils/permissionManager";
import { systemManagementApi } from "@/api";

class RouteService {
  constructor() {
    this.routeManager = null;
    this.isInitialized = false;
    this.initPromise = null;
  }

  /**
   * 初始化路由服务
   * @param {VueRouter} router - Vue Router实例
   */
  init(router) {
    if (this.isInitialized) {
      return;
    }

    this.routeManager = new RouteManager(router);
    this.isInitialized = true;
  }

  /**
   * 加载用户路由
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否加载成功
   */
  async loadUserRoutes(userId) {
    if (!this.isInitialized) {
      throw new Error("路由服务未初始化");
    }

    // 避免重复加载
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._loadUserRoutesInternal(userId);
    return this.initPromise;
  }

  /**
   * 直接使用菜单数据加载用户路由
   * @param {Array} menuData - 菜单数据
   * @param {Array} buttonData - 按钮权限数据
   * @returns {Promise<boolean>} 是否加载成功
   */
  async loadUserRoutesFromData(menuData, buttonData = []) {
    if (!this.isInitialized) {
      throw new Error("路由服务未初始化");
    }

    // 避免重复加载
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._loadUserRoutesFromDataInternal(
      menuData,
      buttonData
    );
    return this.initPromise;
  }

  /**
   * 内部路由加载逻辑
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否加载成功
   */
  async _loadUserRoutesInternal(userId) {
    try {
      // 获取用户菜单数据
      const menuResponse = await systemManagementApi.getUserMenu(userId);

      if (menuResponse.code !== 0) {
        throw new Error(
          `获取用户菜单失败: ${menuResponse.message || "未知错误"}`
        );
      }

      const menuData = menuResponse.data?.userMenu || [];
      const buttonData = menuResponse.data?.userPermissions || [];

      if (menuData.length === 0) {
        throw new Error("用户没有分配任何菜单权限，请联系管理员");
      }

      // 初始化权限管理器
      permissionManager.initPermissions(menuData, buttonData);

      // 生成动态路由
      const routes = this.routeManager.generateRoutes(menuData);

      // 添加路由到路由器
      this.routeManager.addDynamicRoutes(routes);

      // 存储到本地存储（兼容现有逻辑）
      this._saveToLocalStorage(menuData, buttonData);

      // 发射事件通知布局组件更新
      window.dispatchEvent(
        new CustomEvent("menuDataLoaded", {
          detail: { menuData, buttonData },
        })
      );

      return true;
    } catch (error) {
      this.initPromise = null; // 重置Promise，允许重试
      throw error;
    }
  }

  /**
   * 直接使用菜单数据的内部路由加载逻辑
   * @param {Array} menuData - 菜单数据
   * @param {Array} buttonData - 按钮权限数据
   * @returns {Promise<boolean>} 是否加载成功
   */
  async _loadUserRoutesFromDataInternal(menuData, buttonData = []) {
    try {
      // 验证菜单数据
      if (!Array.isArray(menuData) || menuData.length === 0) {
        throw new Error("用户没有分配任何菜单权限，请联系管理员");
      }

      // 初始化权限管理器
      permissionManager.initPermissions(menuData, buttonData);

      // 生成动态路由
      const routes = this.routeManager.generateRoutes(menuData);

      // 添加路由到路由器
      this.routeManager.addDynamicRoutes(routes);

      // 存储到本地存储（兼容现有逻辑）
      this._saveToLocalStorage(menuData, buttonData);

      // 发射事件通知布局组件更新
      window.dispatchEvent(
        new CustomEvent("menuDataLoaded", {
          detail: { menuData, buttonData },
        })
      );

      return true;
    } catch (error) {
      this.initPromise = null; // 重置Promise，允许重试
      throw error;
    }
  }

  /**
   * 保存数据到本地存储（兼容现有代码）
   * @param {Array} menuData - 菜单数据
   * @param {Array} buttonData - 按钮权限数据
   */
  _saveToLocalStorage(menuData, buttonData) {
    try {
      // 过滤并处理菜单数据
      const filteredMenus = this._processMenuForStorage(menuData);

      // 处理按钮权限数据
      const buttonMap = this._processButtonsForStorage(buttonData);

      // 存储到localStorage
      localStorage.setItem("userMenu", JSON.stringify(filteredMenus));
      localStorage.setItem("userPermissions", JSON.stringify(buttonMap));

      // console.log("数据已保存到本地存储");
    } catch (error) {
      console.error("保存到本地存储失败:", error);
    }
  }

  /**
   * 处理菜单数据用于存储
   * @param {Array} menuData - 原始菜单数据
   * @returns {Array} 处理后的菜单数据
   */
  _processMenuForStorage(menuData) {
    return menuData
      .filter((menu) => menu.menuType !== 3) // 3 表示按钮
      .map((menu) => ({
        ...menu,
        children: menu.children
          ? this._processMenuForStorage(menu.children)
          : [],
      }));
  }

  /**
   * 处理按钮权限数据用于存储
   * @param {Array} buttonData - 原始按钮数据
   * @returns {Object} 处理后的按钮权限映射
   */
  _processButtonsForStorage(buttonData) {
    const buttonMap = {};

    buttonData.forEach((button) => {
      const key = button.parentPath || "global";
      if (!buttonMap[key]) {
        buttonMap[key] = [];
      }
      buttonMap[key].push({
        name: button.name,
        path: button.path,
        permission: button.permission,
        id: button.id,
        menuType: button.menuType,
      });
    });

    return buttonMap;
  }

  /**
   * 重新加载路由（用户权限变更时）
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否重新加载成功
   */
  async reloadRoutes(userId) {
    if (!this.isInitialized) {
      throw new Error("路由服务未初始化");
    }

    try {
      // 清除现有路由和权限
      this.routeManager.clearDynamicRoutes();
      permissionManager.clearPermissions();

      // 重置初始化状态
      this.initPromise = null;

      // 重新加载
      return await this.loadUserRoutes(userId);
    } catch (error) {
      console.error("重新加载路由失败:", error);
      throw error;
    }
  }

  /**
   * 清除所有动态路由和权限
   */
  clearAll() {
    if (this.routeManager) {
      this.routeManager.clearDynamicRoutes();
    }

    permissionManager.clearPermissions();

    // 清除本地存储
    localStorage.removeItem("userMenu");
    localStorage.removeItem("userPermissions");

    // 重置状态
    this.initPromise = null;
  }

  /**
   * 获取路由服务状态
   * @returns {Object} 服务状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasRouteManager: !!this.routeManager,
      dynamicRoutes: this.routeManager
        ? this.routeManager.getDynamicRoutesInfo()
        : [],
      permissions: permissionManager.getPermissionStats(),
    };
  }

  /**
   * 检查路由是否已加载
   * @returns {boolean} 路由是否已加载
   */
  isRoutesLoaded() {
    return (
      !!this.initPromise &&
      this.routeManager &&
      this.routeManager.getDynamicRoutesInfo().length > 0
    );
  }

  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  getDebugInfo() {
    return {
      service: this.getStatus(),
      permissions: permissionManager.exportPermissions(),
      localStorage: {
        userMenu: localStorage.getItem("userMenu"),
        userPermissions: localStorage.getItem("userPermissions"),
      },
    };
  }
}

// 创建全局路由服务实例
const routeService = new RouteService();

export default routeService;
