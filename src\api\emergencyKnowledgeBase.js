import request from "@/utils/request";
export default {
    //根据字典ID查询字典项(不分页)
    queryItemListByDictionaryId(params) {
        return request({
            url: '/ds/systemDictionary/queryItemListByDictionaryId',
            method: 'post',
            data: params
        })
    },
    //知识库分页查询 	
    // type :1 法律法规 2 标准规范 3 应急手册 4 典型案例
    queryKnowledgeBasePage(params) {
        return request({
            url: '/ds/knowledgeBase/queryKnowledgeBasePage',
            method: 'post',
            data: params
        })
    },
    // 查询详情
    queryKnowledgeBaseInfo(params) {
        return request({
            url: '/ds/knowledgeBase/queryKnowledgeBaseInfo',
            method: 'post',
            data: params
        })
    },
    // 知识库编辑
    updateKnowledgeBase(params) {
        return request({
            url: '/ds/knowledgeBase/updateKnowledgeBase',
            method: 'post',
            data: params
        })
    },
    // 知识库附件上传
    uploadFile(params) {
        return request({
            url: '/ds/upload/uploadFile',
            method: 'post',
            data: params,
            headers: {
                "Content-Type": "multipart/form-data",
            },
        })
    },
    // 删除知识库附件
    deleteKnowledgeBaseFile(params) {
        return request({
            url: '/ds/knowledgeBase/deleteKnowledgeBaseFile',
            method: 'post',
            data: params
        })
    },
    // 添加知识库
    createKnowledgeBase(params) {
        return request({
            url: '/ds/knowledgeBase/createKnowledgeBase',
            method: 'post',
            data: params
        })
    },
    // 知识库编辑
    updateKnowledgeBase(params) {
        return request({
            url: '/ds/knowledgeBase/updateKnowledgeBase',
            method: 'post',
            data: params
        })
    },
    // 删除知识库
    deleteKnowledgeBase(params) {
        return request({
            url: '/ds/knowledgeBase/deleteKnowledgeBase',
            method: 'post',
            data: params
        })
    },


    /**
   * --------------------------应急资源-------------------------------
   */

    // 应急资源---新增应急资源
    createEquipmentInfo(params) {
        return request({
            url: '/ds/equipmentInfo/createEquipmentInfo',
            method: 'post',
            data: params
        })
    },

    // 应急资源---删除应急资源
    delEquipmentInfo(params) {
        return request({
            url: '/ds/equipmentInfo/delEquipmentInfo',
            method: 'post',
            data: params
        })
    },

    // 应急资源---修改应急资源
    updEquipmentInfo(params) {
        return request({
            url: '/ds/equipmentInfo/updEquipmentInfo',
            method: 'post',
            data: params
        })
    },

    // 应急资源---查询应急资源列表
    getEquipmentInfoPage(params) {
        return request({
            url: '/ds/equipmentInfo/getEquipmentInfoPage',
            method: 'post',
            data: params
        })
    },

    // 应急资源---查询应急资源详情
    getEquipmentInfo(params) {
        return request({
            url: '/ds/equipmentInfo/getEquipmentInfo',
            method: 'post',
            data: params
        })
    },


    // 避难场所---新增避难场所
    createRefugeInfo(params) {
        return request({
            url: '/ds/refuge/createRefugeInfo',
            method: 'post',
            data: params
        })
    },

    // 避难场所---删除避难场所
    deleteRefugeInfo(params) {
        return request({
            url: '/ds/refuge/deleteRefugeInfo',
            method: 'post',
            data: params
        })
    },

    // 避难场所---修改避难场所
    updateRefugeInfo(params) {
        return request({
            url: '/ds/refuge/updateRefugeInfo',
            method: 'post',
            data: params
        })
    },

    // 避难场所---查询避难场所列表
    queryRefugeInfoPage(params) {
        return request({
            url: '/ds/refuge/queryRefugeInfoPage',
            method: 'post',
            data: params
        })
    },

    // 避难场所---查询避难场所详情
    queryRefugeInfo(params) {
        return request({
            url: '/ds/refuge/queryRefugeInfo',
            method: 'post',
            data: params
        })
    },
}