<template>
    <general-dialog
        :dialog-visible="dialogVisible"
        :dialog-width="dialogWidth"
        :general-dialog-title="generalDialogTitle"
        :set-component-name="$store.getters.componentName"
        :showFooter="formType != 3"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      >
        <el-form
          ref="addForm"
          :model="form"
          :rules="rules"
          class="add-form demo-form-inline"
          label-position="right"
          label-width="150px"
        >
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="账号名称" prop="loginName">
                        <el-input :disabled="formType == 3" v-model="form.loginName" placeholder="请输入账号名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="用户姓名" prop="userName">
                        <el-input :disabled="formType == 3" v-model="form.userName" placeholder="请输入用户姓名" />
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系电话（手机）" prop="phone">
                        <el-input :disabled="formType == 3" v-model="form.phone" placeholder="请输入联系电话（手机）" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系电话（座机）" prop="phoneTel">
                        <el-input :disabled="formType == 3" v-model="form.phoneTel" placeholder="请输入联系电话（座机）" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="用户分级" prop="userClass">
                        <el-select
                            v-model="form.userClass"
                            :disabled="formType == 3"
                            placeholder="请选择用户分级">
                            <el-option
                                v-for="data in option"
                                :key="data.id"
                                :label="data.itemName"
                                :value="data.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <!-- <el-form-item label="账号状态" prop="lockFlag">
                        <el-select
                            v-model="form.lockFlag"
                            placeholder="请选择账号状态">
                            <el-option
                                v-for="data in option2"
                                :key="data.id"
                                :label="data.itemName"
                                :value="data.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="用户职务" prop="positions">
                        <el-select
                            v-model="form.positions"
                            :disabled="formType == 3"
                            placeholder="请选择用户职务">
                            <el-option
                                v-for="data in option1"
                                :key="data.id"
                                :label="data.itemName"
                                :value="data.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="所属机构" prop="orgId" class="select-width">
                        <el-cascader
                            v-model="form.orgId"
                            :options="orgTree"
                            :props="props"
                            :disabled="formType == 3"
                            popper-class="popper-select"
                            collapse-tags
                            clearable
                            :show-all-levels="false"
                            @change="handleChange">
                        </el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="备注" prop="remark">
                       <el-input
                            type="textarea"
                            :rows="2"
                            :disabled="formType == 3"
                            placeholder="请输入内容"
                            v-model="form.remark">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { orgApi } from '@/api/index'
import { getItemList, userClassDictionaryId, userPositionDictionaryId } from "@/utils/dictionary";


export default {
    // props:{
    //     orgTree: {
    //         type: Array,
    //         default: () => []
    //     }
    // },
    components: { GeneralDialog },
    data() {
        // 手机号验证规则
        const validatePhone = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('请输入手机号码'));
            }
            const reg = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
            if (!reg.test(value)) {
                return callback(new Error('请输入正确的手机号码'));
            }
            callback();
        };
        
        // 座机号验证规则（可选）
        const validateTel = (rule, value, callback) => {
            if (value && !/^(0\d{2,3}-?)?\d{7,8}$/.test(value)) {
                return callback(new Error('请输入正确的座机号码'));
            }
            callback();
        };
        return {
            title: '',
            formType:1,
            rowData:{},
            dialogVisible: false,
            dialogWidth: "68%",
            generalDialogTitle: "用户新增",
            form: {
                loginName: '',
                userName: '',
                phone: '',
                phoneTel: '',
                userClass: '',
                orgId: '',
                positions: '',
                remark:''
                // lockFlag: ''
            },
            rules: {
                loginName: [
                    { required: true, message: '请输入账号名称', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                userName: [
                    { required: true, message: '请输入用户姓名', trigger: 'blur' },
                    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
                ],
                phone: [
                    { required: true, validator: validatePhone, trigger: 'blur' }
                ],
                phoneTel: [
                    { validator: validateTel, trigger: 'blur' }
                ],
                userClass: [
                    { required: true, message: '请选择用户分级', trigger: 'change' }
                ],
                orgId: [
                    { required: true, message: '请输入所属机构', trigger: 'change' }
                ],
                positions: [
                    { required: true, message: '请选择用户职务', trigger: 'change' }
                ],
                // lockFlag: [
                //     { required: true, message: '请选择账号状态', trigger: 'change' }
                // ]
            },
            option: [],
            option1: [],
            option2: [
                {id:1,itemName:'正常'},
                {id:2,itemName:'注销'}
            ],
            orgTree:[],
            props: {
                multiple: false,
                label: 'orgName',
                value: 'id',
                children: 'children',
                emitPath: false 
            },
        }
    },
    created(){
        this.dictionaryFn(userClassDictionaryId,1); //用户等级
        this.dictionaryFn(userPositionDictionaryId,2); //用户职务
    },
    mounted(){
        
    },
    methods:{
        handleChange(value){
        },
        addEditShowFn(data){
            var _this = this;
            this.formType = data.formType; //1 添加 2 编辑 3查看
            this.generalDialogTitle = data.formType == 1?"用户新增":data.formType == 2?"用户编辑":"用户详情";
            this.orgTree = this.processEmptyChildren(data.orgTree); //组织树结构
            if(data.formType != 1){
                this.rowData = data.row?data.row:[];
                this.getUserInfoDetailFn(this.rowData);
            }
            this.dialogVisible = true;
        },

        async getUserInfoDetailFn(dataObj){
            const res = await orgApi.getUserInfoDetail({id:dataObj.id});
            const {code, data, message, error} = res;
            if (code === 0) {
                this.rowData = data;
                this.form = {
                    loginName: data.loginName,
                    userName: data.userName,
                    phone: data.phone,
                    phoneTel: data.phoneTel,
                    userClass: data.userClass,
                    orgId: data.orgList[0]?.id,
                    positions: data.positions,
                    remark:data.remark,
                    // lockFlag: this.rowData.lockFlag == 1?1:9
                };
            } else {
              this.$message.error(message || error);
            }
        },

        //取消
        handleCancel() {
            this.dialogVisible = false;
            this.form = {
                loginName: '',
                userName: '',
                phone: '',
                phoneTel: '',
                userClass: '',
                sourceTypeItemId: '',
                positions: '',
                remark:''
                // lockFlag: ''
            };
        },
        handleSubmit() {
            this.$refs.addForm.validate(valid => {
                if (valid) {
                    var dataObj = JSON.parse(JSON.stringify(this.form)); // 深拷贝用于请求
                    if(this.formType == 1){ //添加
                        this.addFn(dataObj);
                    }else{ //编辑
                        dataObj.id = this.rowData.id;
                        this.eidtFn(dataObj);
                    }
                } else {
                    return false;
                }
            });
        },
        async addFn(dataObj){
            const res = await orgApi.createSysUser(dataObj);
            const {code, error} = res;
            if (code === 0) {
              this.dialogVisible = false;
              this.$message.success('新增成功');
              this.$emit('confirm',dataObj.orgId);
            } else {
              this.$message.error(error);
            }
        },
        async eidtFn(dataObj){
            const res = await orgApi.updateSysUser(dataObj);
            const {code, error} = res;
            if (code === 0) {
              this.dialogVisible = false;
              this.$message.success('修改成功');
              this.$emit('confirm',this.rowData.orgId);
            } else {
              this.$message.error(error);
            }
        },
        async dictionaryFn(dataObj,type){
            const data = await getItemList(dataObj);
            if(type == 1){ // 等级
                this.option = data;
            }else if(type == 2){ //职务
                this.option1 = data;
            }
        },
        processEmptyChildren(treeData) {
            return treeData.map(item => {
                // 深拷贝当前节点（避免修改原对象）
                const newNode = {...item};
                
                // 如果 children 存在且是数组
                if (Array.isArray(newNode.children)) {
                    if (newNode.children.length === 0) {
                        // 空数组设置为 null
                        newNode.children = null;
                    } else {
                        // 递归处理子节点
                        newNode.children = this.processEmptyChildren(newNode.children);
                    }
                }
                return newNode;
            });
        },

    }
}
</script>

<style scoped lang="scss">
.add-form  .select-width .el-cascader { width: 100%; }
::v-deep .popper-select {
        li[aria-haspopup="true"] {
                .el-checkbox {
                        display: none;
                }
        }
}
</style>