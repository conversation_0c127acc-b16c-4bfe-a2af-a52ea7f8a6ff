<template>
  <div class="position-form">
    <!-- 职务编辑表单 - Component -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
      class="position-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职务名称" prop="itemName">
            <el-input
              v-model="form.itemName"
              placeholder="请输入职务名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务描述" prop="itemDesc">
            <el-input
              v-model="form.itemDesc"
              placeholder="请输入职务描述"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="位次" prop="itemSort">
            <el-input-number
              v-model="form.itemSort"
              :min="1"
              :max="999"
              placeholder="请输入位次"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务值" prop="itemValue">
            <el-input
              v-model="form.itemValue"
              placeholder="请输入职务值（英文标识）"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? "更新" : "保存" }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { positionApi } from "@/api/index.js";

export default {
  name: "PositionForm",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      submitting: false,
      form: {
        id: "",
        systemDictionaryId: "202506231532",
        itemName: "",
        itemDesc: "",
        itemValue: "",
        itemSort: 1,
      },
      rules: {
        itemName: [
          { required: true, message: "请输入职务名称", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        itemDesc: [
          { required: true, message: "请输入职务描述", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur",
          },
        ],
        itemValue: [
          { required: true, message: "请输入职务值", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        itemSort: [
          { required: true, message: "请输入位次", trigger: "blur" },
          {
            type: "number",
            min: 1,
            max: 999,
            message: "位次范围 1-999",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    isEdit() {
      return this.editData && this.editData.id;
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm();
      }
    },
    editData: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.form = { ...newVal };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit) {
        this.form = { ...this.editData };
      } else {
        this.form = {
          id: "",
          systemDictionaryId: "202506231532",
          itemName: "",
          itemDesc: "",
          itemValue: "",
          itemSort: 1,
        };
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate();
        if (!valid) return;

        this.submitting = true;

        let response;
        if (this.isEdit) {
          // 编辑模式 - 调用更新API
          const apiData = {
            ...this.form,
            createTime:
              this.form.createTime ||
              new Date().toISOString().slice(0, 19).replace("T", " "),
            isDel: 0,
          };
          response = await positionApi.updatePositions(apiData);
        } else {
          // 新增模式 - 调用新增API
          const createData = {
            createTime: new Date().toISOString().slice(0, 19).replace("T", " "),
            id: "",
            isDel: 0,
            itemDesc: this.form.itemDesc,
            itemName: this.form.itemName,
            itemSort: this.form.itemSort,
            itemValue: this.form.itemValue || this.form.itemName,
            systemDictionaryId: "202506231532",
          };
          response = await positionApi.createPositions(createData);
        }

        if (response && response.code === 0) {
          this.$message.success(this.isEdit ? "更新成功" : "新增成功");
          this.$emit("success");
          this.handleCancel();
        } else {
          this.$message.error(response?.message || "操作失败");
        }
      } catch (error) {
        this.$message.error("操作失败，请重试");
      } finally {
        this.submitting = false;
      }
    },

    // 取消操作
    handleCancel() {
      this.$emit("cancel");
    },

    // 重置表单
    resetForm() {
      this.initForm();
    },
  },
};
</script>

<style lang="scss" scoped>
.position-form {
  padding: 50px 20px 20px 20px;
}

.position-edit-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.form-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}
</style>
