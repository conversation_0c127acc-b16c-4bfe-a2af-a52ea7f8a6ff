<!-- 工作内容管理---WorkContentManagement -->
<template>
    <div class="user-index-container">
        <portal-table style="padding: 20px" :showAddButton="false" :showSelection="false" :columns="columns"
            :pagination="pagination" :search-items="searchItems" :table-data="tableData" row-key="name"
            @search="handleSearch" @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange" />
        <ContactForm :visible.sync="formVisible" :form-data="currentContact" :isOperation="isOperation"
            @submit="handleFormSubmit" @update:isOperation="isOperation = $event" />
    </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import ContactForm from './components/ContactForm.vue';

export default {
    name: "WorkContentManagement",
    components: {
        PortalTable,
        ContactForm
    },
    data() {
        return {
            searchItems: [
                {
                    prop: "aa",
                    label: "预警事项",
                    type: "input",
                    placeholder: "输入预警名称",
                    width: "170",
                },
                {
                    prop: "bb",
                    label: "发布单位",
                    type: "select",
                    placeholder: "请选择",
                    width: "170",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "cc",
                    label: "审核状态",
                    type: "select",
                    placeholder: "请选择",
                    width: "170",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "dd",
                    label: "发布状态",
                    type: "select",
                    placeholder: "请选择",
                    width: "170",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "ff",
                    label: "事件类型",
                    type: "cascader",
                    placeholder: "输选择事件类型",
                    width: "170",
                    options: [
                        {
                            value: 'zhinan',
                            label: '指南',
                            children: [
                                {
                                    value: 'shejiyuanze',
                                    label: '设计原则',
                                }
                            ]
                        }
                    ],
                    props: {
                        checkStrictly: true
                    }
                },
                {
                    prop: "ee",
                    label: "发布时间",
                    type: "startEndPicker",
                    placeholder: "输选择发布时间",
                },
                
            ],
            columns: [
                { prop: "aa", label: "发布时间", text: true },
                { prop: "bb", label: "发布单位", text: true },
                { prop: "cc", label: "预警事项", text: true },
                { prop: "dd", label: "预警等级", text: true, width: "160px" },
                { prop: "ee", label: "起始时间", text: true },
                { prop: "gg", label: "发布状态", text: true, width: "160px" },
                {
                    action: true, //是否显示操作
                    label: '操作',
                    width: '200px',
                    operationList: [
                        {
                            label: '智能辅助',
                            permission: '',
                            buttonClick: this.handleView,
                            isShow: (row, $index) => {
                                return true
                            }
                        },
                        
                    ]
                }
            ],
            tableData: [
                {
                    aa: '123'
                }
            ],
            styleType: 1, // 1：新增，2：编辑，3：查看
            // 页码
            pagination: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            formVisible: false,
            currentContact: {},
            isOperation: 1 // 1添加、2 编辑 3查看
        }
    },
    mounted() {

    },
    methods: {
        handleView() {
            this.formVisible = true
            this.isOperation = 3
        },
        handleEdit() {
            this.formVisible = true
            this.isOperation = 2
        },
        handleRemove() {
            this.$confirm("确定删除", "删除", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {

            }).catch(() => {
            });
        },
        handleApply() {
            this.$confirm("确定提交申请", "申请", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {

            }).catch(() => {
            });
        },
        handleRelease() {
            this.$confirm("确定提交发布", "发布", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {

            }).catch(() => {
            });
        },

        handleFormSubmit() {

        },
        handleSearch() {

        },
        handleSizeChange() {

        },
        handleCurrentChange() {

        }
    },
};
</script>

<style lang="scss" scoped></style>
