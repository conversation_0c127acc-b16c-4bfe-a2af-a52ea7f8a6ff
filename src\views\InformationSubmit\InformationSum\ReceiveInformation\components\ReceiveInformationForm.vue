<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :general-dialog-title="dialogTitle"
    dialog-width="1200px"
    @cancel="handleCancel"
    @confirm="handleSubmit"
  >
    <!-- 接报信息表单弹窗 - Component -->
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      label-width="auto"
      class="add-form"
    >
      <!-- 基本信息 -->
      <basic-info-form
        v-model="form"
        :location-options="locationOptions"
        :event-type-options="eventTypeOptions"
        :readonly="isContinueMode"
      />

      <!-- 事件详情 -->
      <event-detail-form v-model="form.eventDetail" />

      <!-- 伤亡信息 -->
      <casualty-info-form v-model="casualtyInfo" />

      <!-- 其他信息 -->
      <other-info-form
        v-model="otherInfo"
        :report-method-options="reportMethodOptions"
      />
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import BasicInfoForm from "./BasicInfoForm.vue";
import EventDetailForm from "./EventDetailForm.vue";
import CasualtyInfoForm from "./CasualtyInfoForm.vue";
import OtherInfoForm from "./OtherInfoForm.vue";
import {
  formRules,
  getInitialFormData,
  formatSubmitData,
  fillFormWithRowData,
  fillContinueFormWithRowData,
  formatContinueSubmitData,
} from "../utils/receiveInfoUtils";

export default {
  name: "ReceiveInformationForm",
  components: {
    GeneralDialog,
    BasicInfoForm,
    EventDetailForm,
    CasualtyInfoForm,
    OtherInfoForm,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String,
      default: "新增接报信息",
    },
    isEditMode: {
      type: Boolean,
      default: false,
    },
    isContinueMode: {
      type: Boolean,
      default: false,
    },
    editingRow: {
      type: Object,
      default: null,
    },
    locationOptions: {
      type: Array,
      default: () => [],
    },
    eventTypeOptions: {
      type: Array,
      default: () => [],
    },
    reportMethodOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: getInitialFormData(),
      rules: formRules,
    };
  },
  computed: {
    casualtyInfo: {
      get() {
        return {
          deathCount: this.form.deathCount,
          missingCount: this.form.missingCount,
          seriousInjuryCount: this.form.seriousInjuryCount,
          minorInjuryCount: this.form.minorInjuryCount,
        };
      },
      set(value) {
        Object.assign(this.form, value);
      },
    },
    otherInfo: {
      get() {
        return {
          reportMethod: this.form.reportMethod,
          reportUnit: this.form.reportUnit,
          reportPerson: this.form.reportPerson,
        };
      },
      set(value) {
        Object.assign(this.form, value);
      },
    },
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        // 延迟初始化，确保地点选项数据已加载
        this.$nextTick(() => {
          this.initForm();
        });
      }
    },
    // 监听地点选项变化，重新处理地点数据
    locationOptions: {
      handler(newOptions) {
        if (
          this.isContinueMode &&
          this.editingRow &&
          newOptions.length > 0 &&
          this.form.locationPath &&
          this.form.locationPath.length > 0
        ) {
          this.$nextTick(() => {
            this.processLocationData();
          });
        }
      },
      deep: true,
      immediate: false,
    },
  },
  methods: {
    initForm() {
      this.resetForm();
      if (this.isEditMode && this.editingRow) {
        const formData = fillFormWithRowData(this.editingRow);
        Object.assign(this.form, formData);
      } else if (this.isContinueMode && this.editingRow) {
        // 续报模式：基本信息回显，其他信息清空
        const formData = fillContinueFormWithRowData(this.editingRow);
        Object.assign(this.form, formData);

        // 简化处理流程
        this.$nextTick(() => {
          // 处理事件类型转换
          this.convertEventTypeTextToId();

          // 填充用户信息
          this.fillUserInfo();
          this.fillReportingUnit();

          // 直接处理地点数据
          this.processLocationData();

          // 如果地点数据处理后仍然为空，尝试设置测试数据
          if (!this.form.locationPath || this.form.locationPath.length === 0) {
            this.setTestLocationData();
          }
        });
      } else {
        this.fillUserInfo();
        this.fillReportingUnit();
      }
    },

    resetForm() {
      this.form = getInitialFormData();
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields();
      }
    },

    fillUserInfo() {
      if (this.isEditMode && this.editingRow && !this.isContinueMode) {
        this.form.reportPerson = this.editingRow.infoReportingUser || "";
      } else {
        const userInfo = this.$store.state.user.userInfo;
        if (userInfo) {
          this.form.reportPerson = userInfo.name || userInfo.username || "";
        }
      }
    },

    fillReportingUnit() {
      if (this.isEditMode && this.editingRow && !this.isContinueMode) {
        this.form.reportUnit = this.editingRow.infoReportingUnit || "";
      } else {
        const userInfo = this.$store.state.user.userInfo;
        if (userInfo && userInfo.orgName) {
          this.form.reportUnit = userInfo.orgName;
        }
      }
    },

    convertEventTypeTextToId() {
      // 将事件类型的显示文本转换为对应的ID值
      if (this.form.infoChildType && this.eventTypeOptions.length > 0) {
        const eventTypeText = this.form.infoChildType;

        // 查找匹配的选项
        const matchedOption = this.eventTypeOptions.find(
          (option) => option.label === eventTypeText
        );

        if (matchedOption) {
          this.form.infoChildType = matchedOption.value;
        } else {
          // 如果找不到匹配项，清空该字段让用户重新选择
          this.form.infoChildType = "";
        }
      }
    },

    // 简化的地点数据处理方法
    processLocationData() {
      // 如果无地点选项，则无法处理
      if (this.locationOptions.length === 0) {
        return;
      }

      // 如果已有有效路径且选项已加载，则无需处理
      if (
        this.form.locationPath &&
        this.form.locationPath.length > 0 &&
        this.validateLocationPath(this.form.locationPath)
      ) {
        return;
      }

      // 清空之前的无效路径
      this.form.locationPath = [];

      // 如果有显示名称，尝试基于名称匹配
      if (this.form.locationDisplayName) {
        const matchedLocationPath = this.matchLocationByDisplayName(
          this.form.locationDisplayName
        );

        if (matchedLocationPath.length > 0) {
          this.form.locationPath = matchedLocationPath;
          return;
        }
      }

      // 如果编辑行中有明确的区县和街道信息，尝试匹配
      if (this.editingRow) {
        // 提取所有可能的地点信息
        const districtValue =
          this.editingRow.infoDistrict || this.editingRow.district || "";
        const streetValue =
          this.editingRow.infoTownshipStreet || this.editingRow.street || "";

        // 查找匹配的区县
        if (districtValue) {
          const matchedDistrict = this.locationOptions.find(
            (district) =>
              district.id === districtValue || district.name === districtValue
          );

          if (matchedDistrict) {
            // 如果有街道值，查找匹配的街道
            if (streetValue && matchedDistrict.children) {
              const matchedStreet = matchedDistrict.children.find(
                (street) =>
                  street.id === streetValue || street.name === streetValue
              );

              if (matchedStreet) {
                this.form.locationPath = [matchedDistrict.id, matchedStreet.id];
                return;
              }
            }

            // 只有区县匹配
            this.form.locationPath = [matchedDistrict.id];
            return;
          }
        }
      }
    },

    // 验证地点路径是否有效 (与地点选项匹配)
    validateLocationPath(path) {
      if (!path || path.length === 0) return false;

      // 验证区县
      const districtId = path[0];
      const matchedDistrict = this.locationOptions.find(
        (district) => district.id === districtId
      );

      if (!matchedDistrict) return false;

      // 如果有街道，验证街道
      if (path.length > 1) {
        const streetId = path[1];
        if (!matchedDistrict.children) return false;

        const matchedStreet = matchedDistrict.children.find(
          (street) => street.id === streetId
        );

        if (!matchedStreet) return false;
      }

      return true;
    },

    // 根据显示名称匹配地点
    matchLocationByDisplayName(displayName) {
      if (!displayName || this.locationOptions.length === 0) {
        return [];
      }

      // 遍历所有区县和街道，查找名称包含关系
      for (const district of this.locationOptions) {
        // 如果区县名称包含在显示名称中
        if (displayName.includes(district.name)) {
          // 如果有街道数据，检查街道
          if (district.children && district.children.length > 0) {
            for (const street of district.children) {
              if (displayName.includes(street.name)) {
                return [district.id, street.id];
              }
            }
          }

          // 只有区县匹配
          return [district.id];
        }
      }

      return [];
    },

    // 设置测试地点数据 - 仅在无法通过其他方式获取地点时使用
    setTestLocationData() {
      if (this.locationOptions.length === 0) {
        return;
      }

      // 获取第一个区县选项
      const firstDistrict = this.locationOptions[0];
      if (firstDistrict) {
        if (firstDistrict.children && firstDistrict.children.length > 0) {
          // 有街道数据，设置区县+街道
          this.form.locationPath = [
            firstDistrict.id,
            firstDistrict.children[0].id,
          ];
        } else {
          // 只有区县数据
          this.form.locationPath = [firstDistrict.id];
        }
      }
    },

    handleCancel() {
      this.$emit("cancel");
    },

    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let submitData;

          if (this.isContinueMode) {
            // 续保模式：使用续报数据格式
            submitData = formatContinueSubmitData(
              this.form,
              this.editingRow.id
            );
          } else {
            // 新增或编辑模式：使用原有数据格式
            submitData = formatSubmitData(this.form);
            if (this.isEditMode) {
              submitData.id = this.editingRow.id;
            }
          }

          this.$emit("confirm", submitData);
        } else {
          this.$message.error("请完善必填信息");
        }
      });
    },
  },
};
</script>

<style scoped>
.add-form {
  padding: 30px 60px 5px 60px;
  max-height: 70vh;
  overflow-y: auto;
  background: #ffffff;
}

.add-form::-webkit-scrollbar {
  width: 6px;
}

.add-form::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.add-form::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.add-form::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

.form-section {
  margin-bottom: 30px;
  padding: 0;
  background: #ffffff;
  border: none;
  position: relative;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0;
  color: #0a1629;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.section-title i {
  color: #4569af;
  font-size: 16px;
}

@media (max-width: 768px) {
  .add-form {
    padding: 20px 30px 5px 30px;
    max-height: 60vh;
  }
}
</style>
