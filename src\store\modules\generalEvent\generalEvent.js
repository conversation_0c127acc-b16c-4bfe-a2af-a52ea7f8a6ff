const state = {
  eventHandlers: {
    add_top: null, //  新增事件
    edit_top: null, // 编辑事件
    deleteBatch_top: null, // 删除事件
    download_top: null, // 下载模板
    import_top: null, // 导入事件
    export_top: null, // 导出事件
    sync_top: null, // 数据同步
    task_sync_top: null, // 任务同步
    task_dispatch_top: null, // 任务下发
  },
};

const mutations = {
  registerEventHandler(state, { type, handler }) {
    if (
      [
        "add_top",
        "import_top",
        "export_top",
        "sync_top",
        "edit_top",
        "download_top",
        "deleteBatch_top",
        "task_sync_top",
        "task_dispatch_top",
      ].includes(type)
    ) {
      state.eventHandlers[type] =
        typeof handler === "function" ? handler : null;
    }
  },
};

const getters = {};

const actions = {
  triggerEvent({ state }, type) {
    const handler = state.eventHandlers[type];
    if (handler) handler();
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
