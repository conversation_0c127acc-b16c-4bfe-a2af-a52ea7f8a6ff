<template>
  <div class="data-table" :style="containerStyle">
    <!-- 通用数据表格组件 - Component -->

    <div class="table-container">
      <el-table
        v-show="tableData && tebShow"
        :data="tableData"
        :stripe="stripe"
        :border="border"
        :size="size"
        :height="computedTableHeight"
        :max-height="computedTableHeight"
        style="width: 100%"
        v-loading="loading"
        :row-key="rowKey"
        ref="tableRef"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.9)"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        @sort-change="handleSortChange"
        @select-all="selectAll"
        @select="checkboxSelect"
        class="data-table-el"
      >
        <el-table-column
          v-if="showSelection"
          :reserve-selection="true"
          type="selection"
          width="55"
          align="center"
          fixed="left"
        ></el-table-column>

        <el-table-column
          v-if="showIndex"
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>

        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable ? 'custom' : false"
          :align="column.align || 'center'"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template slot-scope="scope">
            <span v-if="!column.render">{{ scope.row[column.prop] }}</span>
            <span
              v-else
              v-html="column.render(scope.row, scope.column, scope.$index)"
            ></span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="rowActions && rowActions.length > 0"
          label="操作"
          :width="actionColumnWidth"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              v-for="action in getRowActions(scope.row)"
              :key="action.key"
              :type="action.type || 'text'"
              :size="action.size || 'mini'"
              :icon="action.icon"
              @click.stop="handleRowAction(action, scope.row, scope.$index)"
            >
              {{ action.label }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div v-if="showPagination" class="pagination-wrapper">
      <div class="pagination-info">
        <span class="total-info">共 {{ total }} 条</span>
        <el-select
          v-model="currentPageSize"
          size="small"
          style="width: 100px; margin-left: 8px"
          @change="handleSizeChange"
        >
          <el-option
            v-for="size in pageSizes"
            :key="size"
            :label="`${size}条/页`"
            :value="size"
          ></el-option>
        </el-select>
      </div>

      <div class="pagination-controls">
        <el-pagination
          :current-page="currentPage"
          :page-size="currentPageSize"
          :total="total"
          :page-sizes="pageSizes"
          layout="prev, pager, next"
          @current-change="handleCurrentChange"
          small
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getTableAllData } from "@/utils/portalUtils";
export default {
  name: "DataTable",
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },
    // 列配置
    columns: {
      type: Array,
      default: () => [],
    },
    // 分页相关
    total: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100],
    },
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 表格配置
    stripe: {
      type: Boolean,
      default: true,
    },
    border: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: "small",
    },
    tableHeight: {
      type: [String, Number],
      default: null,
    },
    // 表格最大高度（用于控制整体高度）
    maxHeight: {
      type: [String, Number],
      default: null,
    },
    // 是否固定表头
    fixedHeader: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // 选择相关
    showSelection: {
      type: Boolean,
      default: false,
    },
    showIndex: {
      type: Boolean,
      default: false,
    },
    // 操作列
    rowActions: {
      type: Array,
      default: () => [],
    },
    actionColumnWidth: {
      type: Number,
      default: 120,
    },
    //表格row-key
    rowKey: {
      type: String,
      default: "",
    },
    //单选分页勾选记录开启
    typeSelectonOne: {
      type: Boolean,
      default: false,
    },
    //多选分页勾选记录
    typeSelecton: {
      type: Boolean,
      default: false,
    },
    tebShow: {
      // 表格是否显示
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      currentPageSize: this.pageSize,
      ColumnData2: [],
      checkedKeys: false,
    };
  },
  computed: {
    // 计算表格高度，用于固定表头
    computedTableHeight() {
      if (this.tableHeight) {
        return this.tableHeight;
      }
      // 如果设置了maxHeight，需要为分页组件预留空间
      if (this.maxHeight && this.showPagination) {
        const maxHeightNum =
          typeof this.maxHeight === "number"
            ? this.maxHeight
            : parseInt(this.maxHeight);
        return Math.max(200, maxHeightNum - 60); // 为分页组件预留60px
      }
      // 如果启用固定表头且没有指定高度，使用默认高度
      if (this.fixedHeader) {
        return this.showPagination ? 350 : 400;
      }
      return null;
    },
    // 计算容器样式
    containerStyle() {
      const style = {};
      if (this.maxHeight) {
        style.maxHeight =
          typeof this.maxHeight === "number"
            ? `${this.maxHeight}px`
            : this.maxHeight;
        style.display = "flex";
        style.flexDirection = "column";
      }
      return style;
    },
  },
  watch: {
    pageSize(newVal) {
      this.currentPageSize = newVal;
    },
    tableData: {
      immediate: true,
      handler(newVal) {
        if (newVal.length > 0 && (this.typeSelectonOne || this.typeSelecton)) {
          //回显
          newVal.forEach((item) => {
            if (item.isSelected) {
              this.$refs.tableRef.toggleRowSelection(item, true);
            }
          });
        }
      },
    },
  },
  mounted() {},
  methods: {
    // 分页事件
    handleSizeChange(size) {
      this.currentPageSize = size;
      this.$emit("size-change", size);
    },
    handleCurrentChange(page) {
      this.$emit("current-change", page);
    },
    // 表格事件
    handleSelectionChange(selection) {
      if (this.typeSelecton) {
        //多选分页勾选记录
        if (this.typeSelectonOne) {
          //单选
          if (selection.length > 1) {
            this.$refs.tableRef.clearSelection();
            this.$refs.tableRef.toggleRowSelection(
              selection[selection.length - 1]
            );
            return;
          }
          this.$emit("selection-change", selection);
          return;
        }
        this.$emit("selection-change", selection);
        return;
      }
      this.$emit("selection-change", this.getCheckList());
    },
    getCheckList() {
      return getTableAllData(this.ColumnData2).filter(
        (item) => item.isSelected
      );
    },
    checkboxSelect(val, row) {
      if (this.typeSelecton) {
        //多选分页勾选记录
        return;
      }
      const thisSelect = !row.isSelected;
      row.isSelected = thisSelect;
      if (!this.isAllSelect) {
        // 取消其他所有行的选中状态
        const allRows = getTableAllData(this.ColumnData2);
        allRows.forEach((item) => {
          if (item !== row) {
            item.isSelected = false;
            this.$refs.tableRef.toggleRowSelection(item, false);
          }
        });
      } else {
        //this.$set(row, 'isSelected', thisSelect)
        this.$refs.tableRef.toggleRowSelection(row, thisSelect);
        row.children && this.splite(row.children, thisSelect); // 点击父节点，则子勾选
      }
    },
    selectAll() {
      // 全选/非全选
      this.checkedKeys = !this.checkedKeys;
      this.splite(this.ColumnData2, this.checkedKeys);
    },
    splite(data, flag) {
      data.forEach((row) => {
        row.isSelected = flag;
        this.$set(row, "isSelected", flag);
        this.$refs.tableRef.toggleRowSelection(row, flag);
        row.children && this.splite(row.children, flag);
      });
    },
    handleRowClick(row, column, event) {
      this.$emit("row-click", row, column, event);
    },
    handleSortChange({ column, prop, order }) {
      this.$emit("sort-change", { column, prop, order });
    },
    // 行操作
    handleRowAction(action, row, index) {
      this.$emit("row-action", action, row, index);
    },
    getRowActions(row) {
      return this.rowActions.filter((action) => {
        return !action.show || action.show(row);
      });
    },
  },
};
</script>

<style scoped>
.data-table {
  background: var(--content-bg);
  border-radius: var(--content-border-radius);
  display: flex;
  flex-direction: column;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-container {
  border: 1px solid #c4d5f6;
  border-radius: 4px;
  margin-bottom: 4px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--content-bg);
  border-top: 1px solid var(--table-border);
  padding: 4px 20px 4px 20px;
  min-height: 40px;
}

.pagination-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}

.total-info {
  margin-right: 8px;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

/* 表格样式覆盖 */
.data-table-el {
  border: none;
}

.data-table-el >>> .el-table__header th {
  background: #c4d5f6;
  color: #5a6c8a;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 0;
  height: 40px;
  text-align: center;
  border-bottom: 1px solid #c4d5f6;
  border-right: 1px solid #c4d5f6;
  line-height: 24px;
}

.data-table-el >>> .el-table__header th .cell {
  color: #5a6c8a;
  font-weight: 500;
  font-size: 13px;
  text-align: center;
  line-height: 24px;
}

.data-table-el >>> .el-table__row:hover > td {
  background-color: var(--table-row-hover);
}

.data-table-el >>> .el-table td {
  border-bottom: 1px solid #c4d5f6;
  border-right: 1px solid #c4d5f6;
  font-size: 13px;
  padding: 10px 0;
  color: var(--text-secondary);
}

.data-table-el >>> .el-table {
  border: none;
  border-radius: 0;
}

.data-table-el >>> .el-table::before {
  display: none;
}

.data-table-el >>> .el-table--border::after,
.data-table-el >>> .el-table--group::after {
  display: none;
}

.data-table-el >>> .el-table__body-wrapper {
  border: none;
}

.data-table-el >>> .el-table__fixed {
  border: none;
}

.data-table-el >>> .el-table__fixed-right {
  border: none;
}

/* 分页组件样式 */
.pagination-wrapper >>> .el-pagination {
  font-size: 13px;
}

.pagination-wrapper >>> .el-pagination .el-pager li {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid #dcdfe6;
  margin: 0 2px;
  border-radius: 4px;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

.pagination-wrapper >>> .el-pagination .el-pager li.active {
  background: var(--pagination-active-bg);
  color: var(--pagination-active-text);
  border-color: var(--pagination-active-bg);
}

.pagination-wrapper >>> .el-pagination .el-pager li:hover {
  color: var(--pagination-active-bg);
  background: transparent;
  border-color: var(--pagination-active-bg);
}

.pagination-wrapper >>> .el-pagination .btn-prev,
.pagination-wrapper >>> .el-pagination .btn-next {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin: 0 2px;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

.pagination-wrapper >>> .el-pagination .btn-prev:hover,
.pagination-wrapper >>> .el-pagination .btn-next:hover {
  color: var(--pagination-active-bg);
  background: transparent;
  border-color: var(--pagination-active-bg);
}

/* 操作列按钮样式 */
.data-table >>> .el-button--text.el-button--mini {
  margin-right: 2px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
  min-width: auto !important;
  white-space: nowrap !important;
  line-height: 1.4 !important;
}

/* 最后一个按钮不需要右边距 */
.data-table >>> .el-button--text.el-button--mini:last-child {
  margin-right: 0 !important;
}
</style>
