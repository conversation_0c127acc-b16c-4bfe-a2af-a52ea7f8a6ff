<!--
  接报信息管理-查看-顶部详情展示组件 - Component

  主要功能：
  - 展示事件的核心信息（标题、类型、时间、伤亡情况、地点）
  - 提供格式化的信息展示
  - 支持伤亡情况的特殊显示样式
-->

<template>
  <div class="core-info-compact">
    <div class="info-row single-line">
      <div class="info-item time-item" v-if="detailData.infoTime">
        <span class="label">时间</span>
        <span class="value">{{ formatDate(detailData.infoTime) }}</span>
      </div>
      <div
        class="info-item casualty-item"
        v-if="getCasualtyInfo() && getCasualtyInfo() !== '0死0伤'"
      >
        <span class="label">伤亡</span>
        <span class="value">{{ getCasualtyInfo() }}</span>
      </div>

      <div class="info-item location-item" v-if="detailData.infoLocationDetail">
        <span class="label">地点</span>
        <span class="value">{{ detailData.infoLocationDetail }}</span>
      </div>
      <div class="info-item" v-if="detailData.infoType">
        <span class="label">类型</span>
        <span class="value">{{ detailData.infoType }}</span>
      </div>
      <div class="info-item" v-if="detailData.infoType">
        <span class="label">类型</span>
        <span class="value">{{ detailData.infoType }}</span>
      </div>
      <div class="info-item status-item" v-if="getStatusText()">
        <span class="label">事件状态</span>
        <span class="value">{{ getStatusText() }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { infoStatusMap } from "../utils/receiveInfoUtils";

export default {
  name: "CoreInfoSection",
  props: {
    detailData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    // 获取状态文字
    getStatusText() {
      const statusValue = this.detailData.infoStatus;
      if (statusValue !== undefined && statusValue !== null) {
        return infoStatusMap[statusValue] || "";
      }
      return "";
    },

    // 获取事件标题
    getEventTitle() {
      return (
        this.detailData.infoTitle ||
        this.detailData.eventTitle ||
        this.detailData.title ||
        ""
      );
    },

    // 获取伤亡情况信息
    getCasualtyInfo() {
      const death = this.detailData.deathNum || 0;
      const missing = this.detailData.missingNum || 0;
      const severe = this.detailData.severeInjuryNum || 0;
      const light = this.detailData.lightInjuryNum || 0;

      const total = death + missing + severe + light;
      if (total === 0) {
        return "0死0伤";
      }

      return `${death}死${missing}失联${severe}重伤${light}轻伤`;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.core-info-compact {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 16px;
  min-width: 580px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 0;

    &.single-line {
      flex-wrap: wrap;
      gap: 15px;
      overflow: visible;

      @media (max-width: 1200px) {
        gap: 10px;
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #4a5568;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 0.8);
      padding: 6px 12px;
      border-radius: 6px;
      border: 1px solid rgba(226, 232, 240, 0.6);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.95);
        border-color: #cbd5e0;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      @media (max-width: 1200px) {
        font-size: 13px;
        padding: 5px 10px;
      }

      .label {
        font-weight: 600;
        color: #2d3748;
        margin-right: 8px;
        flex-shrink: 0;
        position: relative;

        &::after {
          content: ":";
          margin-left: 2px;
          color: #a0aec0;
        }

        @media (max-width: 1200px) {
          margin-right: 6px;
        }
      }

      .value {
        color: #4a5568;
        font-weight: 500;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.4;
      }

      &.casualty-item {
        background: rgba(254, 242, 242, 0.9);
        border-color: rgba(252, 165, 165, 0.4);

        .label {
          color: #c53030;
        }

        .value {
          color: #e53e3e;
          font-weight: 600;
        }

        &:hover {
          background: rgba(254, 242, 242, 1);
          border-color: rgba(252, 165, 165, 0.6);
        }
      }

      &.status-item {
        background: rgba(240, 253, 244, 0.9);
        border-color: rgba(72, 187, 120, 0.4);

        .label {
          color: #2f855a;
        }

        .value {
          color: #38a169;
          font-weight: 600;
        }

        &:hover {
          background: rgba(240, 253, 244, 1);
          border-color: rgba(72, 187, 120, 0.6);
        }
      }

      &.location-item {
        flex-shrink: 1;
        min-width: 0;
        max-width: none;

        .value {
          max-width: none;
        }
      }

      &.time-item {
        .value {
          font-family: "Consolas", "Monaco", monospace;
          font-size: 13px;

          @media (max-width: 1200px) {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
