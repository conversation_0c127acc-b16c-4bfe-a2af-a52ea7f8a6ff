<template>
  <div class="user-index-container">
    <portal-table
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :show-pagination="false"
      :showSelection="false"
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
    />
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :showFooter="false"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <addEidt ref="addEditClient" @ok="loadData" />
    </general-dialog>
  </div>
</template>

<script>
import { systemConfigApi } from "@/api";
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import { conversionDate } from "@/utils/publicMethod";

import addEidt from "./components/addEidt.vue";

export default {
  name: "EmergencyType",
  components: { PortalTable, GeneralDialog, addEidt },
  data() {
    return {
      columns: [
        { text: true, align: "left", prop: "name", label: "事件名称" },
        { text: true, prop: "code", label: "类型" },
        { text: true, prop: "sortOrder", label: "类型排序" },
        {
          action: true, //是否显示操作
          label: "操作",
          width: '350px',
          operationList: [
            {
              label: "查看",
              permission: "menu:view",
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "编辑",
              permission: "menu:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "menu:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "周边情况",
              permission: "menu:bindRes",
              buttonClick: this.handleBindRes,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],

      searchItems: [
        {
          prop: "name",
          label: "事件类型名称",
          type: "input",
          placeholder: "请输入",
        },
      ],
      searchParams: null,
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增事件类型",
    };
  },
  created() {},
  mounted() {
    this.registerHandlers();
    this.getTableDataList();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    async getTableDataList() {
      //获取所有的事件类型
      const res = await systemConfigApi.queryEmergencyTypeList(this.searchParams);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      this.tableData = data;
    },

    // 搜索
    handleSearch(row) {
      this.searchParams = row;
      this.getTableDataList();
    },

    loadData(e) {
      this.dialogVisible = false;
      if (e === 1) {
        return;
      }
      this.getTableDataList();
    },

    //新增
    handleAdd() {
      this.generalDialogTitle = "新增事件类型";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.addFormFn();
      });
    },
    //编辑
    handleEdit(row) {
      this.generalDialogTitle = "修改事件类型";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.edit(row);
      });
    },
    // 查看事件类型
    handleReview(row) {
      this.generalDialogTitle = "查看事件类型";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.review(row);
      });
    },
    // 周边情况
    handleBindRes(row) {
      this.generalDialogTitle = "绑定事件周边资源情况";
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.bindRes(row);
      });
    },

    //提交
    handleSubmit() {},

    handleCancel() {
      this.dialogVisible = false;
    },

    //删除
    handleDelete(row) {
      let txt = "此操作将永久删除该条数据, 是否继续?";
      if (row.children.length > 0) {
        txt = "此操作将永久删除该条数据和子级数据，是否继续？";
      }
      this.$confirm(txt, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        systemConfigApi.deleteEmergencyType({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success("删除成功");
          this.getTableDataList();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-index-container {
  padding: 20px;
}
</style>
