<template>
    <!-- 右侧内容区域 -->
    <div class="contact-book-container">
        <div class="contact-book-main">
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <SearchForm :form-config="searchConfig" :tips="searchTips" :show-tips="false" @search="handleSearch"
                    @reset="handleReset" ref="searchFormRef" />
            </div>
            <!-- 表格区域 -->
            <div class="table-wrapper">
                <DataTable :table-data="contactList || []" :columns="responsiveTableColumns || []"
                    :tableHeight="600"
                    :total="pagination?.total || 0" :current-page="pagination?.current || 1"
                    :page-size="pagination?.pageSize || 10" :row-actions="tableActions" :action-column-width="200"
                    :loading="loading" :stripe="true" :border="true" :show-pagination="true" :show-index="true"
                    :key="tableKey" 
                    @row-action="handleTableRowAction" 
                    @current-change="handleCurrentChange" 
                    @size-change="handleSizeChange"
                    />
            </div>
        </div>
    </div>
</template>

<script name="TextMessageRecord">
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import mixinTable from './mixinTable.js'
export default {
    mixins: [mixinTable],
    components: {
        SearchForm,
        DataTable,
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "content",
                    label: "短信内容",
                    type: "input",
                    placeholder: "输入短信内容",
                    width: "200px",
                },
                {
                    prop: "name",
                    label: "接收人",
                    type: "input",
                    placeholder: "输入接收人",
                    width: "200px",
                },

            ],
            searchTips: [

            ],
            // 表格列配置
            tableColumns: [
                { prop: "content", label: "短信内容", minWidth: 320, sortable: false },
                { prop: "name", label: "接收人", width: 150, sortable: false },
                { prop: "mobile", label: "接收号码", width: 150, sortable: false },
                {
                    prop: "result", label: "消息状态", width: 150, sortable: false, render: (row, index) => {
                        return ['发送失败', '发送成功'][row.result]
                    }
                },
                { prop: "createTime", label: "发送时间", width: 150, sortable: false },

            ],
            tableActions: [
                // {
                //     key: "view",
                //     label: '查看',
                //     type: "text",
                //     size: "mini",

                // },
                {
                    key: "remove",
                    label: '删除',
                    type: "text",
                    size: "mini",
                }
            ],
            // 表格刷新key
            tableKey: 0,
            // 表单相关状态
            formVisible: false,
            currentContact: {},
            isEdit: false,
        };
    },
    computed: {

        // 响应式表格列配置
        responsiveTableColumns() {
            const screenWidth = window.innerWidth || 1200;
            return this.tableColumns;

        },
    },
    methods: {

        // 搜索相关方法
        async handleSearch(formData) {
            const searchParams = { ...formData };
            await this.seachContactList(searchParams);
            await this.getContactList();
            this.$message.success("搜索完成");
        },
        async initData() {
            try {
                await this.getContactList();
            } catch (error) {
                console.error("短信模版页面初始化失败:", error);
                this.$message.error("页面初始化失败，请刷新重试");
            }
        },
        async handleReset() {
            await this.seachContactList({});
            await this.setCurrentChange({ current: 1 })
            await this.getContactList();
        },
        async handleCurrentChange() {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ current: page })
            await this.getContactList();
        },
        async handleSizeChange(size) {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ pageSize: size })
            await this.getContactList();
        },
        handleTableRowAction(action, row, index) {
            let key = action.key
            console.log(key)
            switch (key) {
                case 'remove':
                    this.$confirm("确定要删除吗", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        this.removeTextMessage(row)
                        this.initData()
                    }).catch(() => {
                    })
                    break;
                   
                default:
                    break;
            }
        }
    },
    mounted() {
        this.initData();
    }
}
</script>

<style scoped>
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}

.search-wrapper {
    margin-bottom: 8px;
}

.table-wrapper {
    flex: 1;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-wrapper>>>.el-button--text:nth-child(1) {
    color: #409eff !important;
}

.table-wrapper>>>.el-button--text:nth-child(2) {
    color: #e6a23c !important;
}
</style>