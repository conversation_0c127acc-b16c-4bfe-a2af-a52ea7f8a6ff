<template>
    <div class="piece-top">
        <div class="item">
                    
            <div class="piece-item">
                <div>
                    <p class="color">评价周期</p>
                    <p class="title">2025年度</p>
                </div>
                <div class="y-bei">
                    <el-button icon="el-icon-date" size="small" circle></el-button>
                </div>
            </div>

            <div class="piece-item">
                <div>
                    <p class="color">自评截至日期：<span class="color">2025-06-30</span></p>
                </div>
                <div class="but-bei">
                    <el-button round type="primary" size="mini">剩余3天</el-button>
                </div>
            </div>

        </div>

        <div class="item">
            
            <div class="piece-item">
                <div>
                    <p class="color">评价状态</p>
                    <p class="title">进行中</p>
                </div>
                <div class="b-color">
                    <el-button type="success" icon="el-icon-success" size="small" circle></el-button>
                </div>
            </div>

            <div class="progress-css">
                <span>自评进度：</span>
                <el-progress :percentage="50" :color="'#67C23A'"></el-progress>
            </div>


        </div>

        <div class="item">
            
            <div class="piece-item">
                <div>
                    <p class="color">系统评价</p>
                    <p class="title">待审核</p>
                </div>
                <div class="but-bei">
                    <el-button icon="el-icon-time" size="small" circle></el-button>
                </div>
            </div>

            <div class="piece-item">
                <div>
                    <p class="color">自动评分<span class="color1">85分</span></p>
                </div>
                <div class="y-bei">
                    <el-button round type="primary" size="mini">优秀</el-button>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    props: {
        dataArr: {
            type: Array,
            default: () => []
        }
    }
}
</script>

<style lang="scss" scoped>
.piece-top{
    p {
        margin: 0;
    }
    display: flex;
    justify-content: space-between;
    margin: 20px 2% 0;
    .item {
        box-shadow: 0 1px 6px 0px rgb(215 215 215 / 75%);
        width: 32%;
        padding: 5px 2% 5px 2%;
        border-radius: 12px;
        .progress-css {
            display: flex;
            align-items: baseline;
            > span {
                width: 25%;
            }
            > div {
                width: 80%;
            }
            ::v-deep .el-progress__text {
                color: var(--icon-success) !important;
            }
        }
    }
    .piece-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;
        .but-bei {
            .el-button {
                background: #ffa7606e;
                border: solid 1px #ffa7606e;
                color: #ffa760;
            }
        }
        .b-color button {
            color: var(--icon-success);
            background: #67c23a5c;
        }
        .y-bei  {
            .el-button {
                background: #4569af4d;
                border: solid 1px #4569af4d;
                color: #4569af;
            }
        }
        .title {
            font-size: 18px;
            margin-top: 5px;
        }
        .color {
            color: var(--text-auxiliary);
            .color {
                color: #ffa760;
            }
            .color1 {
                color: var(--light-blue);
            }
        }
    }
}
</style>