<template>
    <!-- 右侧内容区域 -->
    <div class="contact-book-container">
        <div class="contact-book-main">
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <SearchForm :form-config="searchConfig" :tips="searchTips" :show-tips="false" @search="handleSearch"
                    @reset="handleReset" ref="searchFormRef" />
            </div>
            <!-- 表格区域 -->
            <div class="table-wrapper">
                <DataTable :table-data="contactList || []" :columns="responsiveTableColumns || []"
                    :tableHeight="600"
                    :total="pagination?.total || 0" :current-page="pagination?.current || 1"
                    :page-size="pagination?.pageSize || 10" :row-actions="tableActions" :action-column-width="200"
                    :loading="loading" :stripe="true" :border="true" :show-pagination="true" :show-index="true"
                    :key="tableKey" @row-action="handleTableRowAction" @current-change="handleCurrentChange" @size-change="handleSizeChange"/>
            </div>
        </div>
        <!--查看 -->
        <ViewForm :visible.sync="viewVisible" :form-data="currentContact"  />
        <!-- 拟办 -->
        <ProposedForm :visible.sync="proposedVisible" :form-data="currentContact" @submit="handleFormSubmit" />
        <!-- 审批 -->
        <ReviewForm :visible.sync="reviewVisible" :form-data="currentContact" @submit="handleFormSubmit" />
        <!-- 提交报批 -->
        <ReportForm :visible.sync="reportVisible" :form-data="currentContact" @submit="handleFormSubmit"></ReportForm>
    </div>
</template>

<script name="TextMessageTemplate">
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import ViewForm from "./components/ViewForm.vue";
import ProposedForm from "./components/ProposedForm.vue";
import ReviewForm from "./components/ReviewForm.vue";
import ReportForm from "./components/ReportForm.vue";
import { getItemList, textMessageTemplateType } from "@/utils/dictionary";
import mixinTable from './mixinTable.js'
export default {
    mixins: [mixinTable],
    components: {
        SearchForm,
        DataTable,
        ViewForm,
        ProposedForm,
        ReviewForm,
        ReportForm
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "cc",
                    label: "预警事项",
                    type: "input",
                    placeholder: "输入预警名称",
                },
                {
                    prop: "bb",
                    label: "发布单位",
                    type: "select",
                    placeholder: "请选择",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "ff",
                    label: "审核状态",
                    type: "select",
                    placeholder: "请选择",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "gg",
                    label: "发布状态",
                    type: "select",
                    placeholder: "请选择",
                    options: [
                        { label: "是", value: "1" },
                        { label: "否", value: "0" },
                    ],
                },
                {
                    prop: "aa",
                    label: "发布时间",
                    type: "daterange",
                    placeholder: "输选择发布时间",
                    format: 'YYYY-MM-DD'
                },
                {
                    prop: "hh",
                    label: "事件类型",
                    type: "cascader",
                    placeholder: "输选择事件类型",
                    options:[
                        {
                            value: 'zhinan',
                            label: '指南',
                            children: [
                                {
                                    value: 'shejiyuanze',
                                    label: '设计原则',
                                }
                            ]
                        }
                    ],
                    props:{
                        checkStrictly: true
                    }
                },
            ],
            searchTips: [

            ],
            // 表格列配置
            tableColumns: [
                { prop: "aa", label: "发布时间", width: 120, sortable: false },
                { prop: "bb", label: "发布单位", width: 140, sortable: false },
                { prop: "cc", label: "预警事项", minWidth: 300, sortable: false },
                { prop: "hh", label: "事件类型", width: 140, sortable: false },
                { prop: "dd", label: "预警等级", width: 100, sortable: false },
                { prop: "ee", label: "起始时间", width: 120, sortable: false },
                { prop: "ff", label: "审核状态", width: 140, sortable: false },
                { prop: "gg", label: "发布状态", width: 100, sortable: false }
                // {
                //     prop: "isCommonlyUsed", label: "是否常用", width: 150, sortable: false, render: (row, index) => {
                //         return ['否', '是'][row.isCommonlyUsed]
                //     }
                // },

            ],
            tableActions: [
                {
                    key: "view",
                    label: '查看',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "publishAapplication",
                    label: '提交发布申请',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "proposed",
                    label: '拟办',
                    type: "text",
                    size: "mini",

                },
                {
                    key: "report",
                    label: '提交报批',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "review",
                    label: '审批',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "publish",
                    label: '发布',
                    type: "text",
                    size: "mini",
                },

            ],
            // 表格刷新key
            tableKey: 0,
            // 表单相关状态
            viewVisible: false,
            proposedVisible:false,
            reviewVisible:false,
            reportVisible:false,
            currentContact: {},
        };
    },
    computed: {
        // 响应式表格列配置
        responsiveTableColumns() {
            return this.tableColumns;
        },
    },
    methods: {
        // 搜索相关方法
        async handleSearch(formData) {
            // 保持当前的组织过滤状态
            const searchParams = { ...formData };
            await this.seachContactList(searchParams);
            await this.getContactList();
            this.$message.success("搜索完成");
        },
        async initData() {
            try {
                await this.getContactList();
            } catch (error) {
                console.error("短信模版页面初始化失败:", error);
                this.$message.error("页面初始化失败，请刷新重试");
            }
        },
        // 重置
        async handleReset() {
            await this.seachContactList({});
            await this.setCurrentChange({ current: 1 })
            await this.getContactList();
        },
        // 提交
        async handleFormSubmit(formData) {
            // if (isEdit) {
            //     await this.updateTextMessageTemplate(formData)
            // } else {
            //     await this.createTextMessageTemplate(formData)
            // }
            // this.initData()
            // this.formVisible = false
        },
        // 点击列表操作
        async handleTableRowAction(action, row, index) {
            let type = action.key
            console.log(type)
            switch (type) {
                case 'view':
                    this.viewVisible = true
                    break;
                case 'publishAapplication':
                    this.$confirm("确定提交发布申请", "提交发布申请", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                      
                    }).catch(() => {
                    });
                    break;
                case 'proposed':
                    this.proposedVisible = true
                    break;
                case 'report':
                    this.reportVisible = true
                    break;
                case 'review':
                    this.reviewVisible = true
                    break;
                case 'publish':
                    
                    this.$confirm("确定提交发布", "发布", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                      
                    }).catch(() => {
                    });
                    break;
                default:
                    break;
            }
        },
        // 分页Current
        async handleCurrentChange(page) {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ current: page })
            await this.getContactList();
        },
        // 分页Size
        async handleSizeChange(size) {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ pageSize: size })
            await this.getContactList();
        },
        // 获取事件类型
        async queryEventType() {
            let data = await getItemList(textMessageTemplateType)
            let list = data.map((row) => {
                return {
                    label: row.itemName,
                    value: row.itemValue
                }
            })
            this.searchConfig[1].options = list
        }
    },
    mounted() {
        this.queryEventType()
        this.initData();
    }
}
</script>

<style scoped>
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}

.search-wrapper {
    margin-bottom: 8px;
}

.table-wrapper {
    flex: 1;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>