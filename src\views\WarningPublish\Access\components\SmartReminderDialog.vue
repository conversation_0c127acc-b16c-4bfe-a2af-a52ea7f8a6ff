<template>
  <general-dialog
    :showFooter="false"
    :dialog-visible="dialogVisible"
    general-dialog-title="知识库"
    dialog-width="1100px"
    @cancel="handleClose"
  >
    <div style="padding: 30px">
      <el-tabs type="border-card" v-model="tableTabsValue">
        <el-tab-pane label="事故案例">
          <portal-table
            style="padding: 20px"
            :tableHeight="300"
            :showAddButton="false"
            :showSelection="false"
            :columns="columnsAccident"
            :pagination="pagination"
            :search-items="searchItemsAccident"
            :table-data="tableDataAccident"
            row-key="name"
            @search="handleSearch"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-tab-pane>
        <el-tab-pane label="应急预案">
          <portal-table
            style="padding: 20px"
            :tableHeight="300"
            :showAddButton="false"
            :showSelection="false"
            :columns="columnsForecast"
            :pagination="pagination"
            :search-items="searchItemsForecast"
            :table-data="tableDataForecast"
            row-key="name"
            @search="handleSearch"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-tab-pane>
        <el-tab-pane label="法律法规">
          <portal-table
            style="padding: 20px"
            :tableHeight="300"
            :showAddButton="false"
            :showSelection="false"
            :columns="columnsLaw"
            :pagination="pagination"
            :search-items="searchItemsLaw"
            :table-data="tableDataLaw"
            row-key="id"
            @search="handleSearch"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-tab-pane>
        <el-tab-pane label="标准规范">
          <portal-table
            style="padding: 20px"
            :tableHeight="300"
            :showAddButton="false"
            :showSelection="false"
            :columns="columnsRules"
            :pagination="pagination"
            :search-items="searchItemsRules"
            :table-data="tableDataRules"
            row-key="id"
            @search="handleSearch"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-tab-pane>
        <el-tab-pane label="专家">
          <portal-table
            style="padding: 20px"
            :tableHeight="300"
            :showAddButton="false"
            :showSelection="false"
            :columns="columnsExpert"
            :pagination="pagination"
            :search-items="searchItemsExpert"
            :table-data="tableDataExpert"
            row-key="name"
            @search="handleSearch"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </general-dialog>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { getItemList, eventLevel } from "@/utils/dictionary";
import { systemManagementApi, warningPublishApi } from "@/api";
export default {
  name: "ContactForm",
  components: {
    PortalTable,
    GeneralDialog,
  },
  props: {
    eventTypeOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      eventLevelOptions: [],
      //0:事故案例,1:应急预案,2:法律法规,3:标准规范,4:专家
      tableTabsValue: "",
      searchParams: {},
      searchType: 1,
      //事故案例
      searchItemsAccident: [
        {
          prop: "name",
          label: "案例名称",
          type: "input",
          placeholder: "输入案例名称",
          width: "150",
        },
        {
          prop: "eventLevel",
          label: "事件等级",
          type: "select",
          placeholder: "请选择事件等级",
          width: "150",
          options: [],
        },
        {
          prop: "eventType",
          label: "事件类型",
          type: "select",
          placeholder: "请选择事件类型",
          width: "150",
          options: [],
        },
      ],
      columnsAccident: [
        { prop: "name", label: "案例名称", text: true },
        {
          prop: "eventType",
          label: "事故类型",
          text: true,
          formatter: this.formatterEventType,
        },
        { prop: "eventLevel", label: "事件等级", text: true },
        {
          prop: "effectiveDate",
          label: "事发日期",
          text: true,
          width: "160px",
        },
      ],
      tableDataAccident: [],
      //应急预案
      searchItemsForecast: [
        {
          prop: "name",
          label: "应急知识标题",
          type: "input",
          placeholder: "输入应急知识标题",
          width: "250",
        },
      ],
      columnsForecast: [
        { prop: "name", label: "应急知识标题", text: true },
        { prop: "updateTime", label: "更新日期", text: true },
      ],
      tableDataForecast: [
        {
          aa: "123",
        },
      ],
      //法律法规
      searchItemsLaw: [
        {
          prop: "name",
          label: "法律法规名称",
          type: "input",
          placeholder: "输入法律法规名称",
          width: "250",
        },
      ],
      columnsLaw: [
        { prop: "name", label: "法律法规名称", text: true },
        { prop: "category", label: "法律法规类别", text: true },
      ],
      tableDataLaw: [],
      //标准规范
      searchItemsRules: [
        {
          prop: "name",
          label: "名称",
          type: "input",
          placeholder: "输入名称",
          width: "250",
        },
      ],
      columnsRules: [{ prop: "name", label: "名称", text: true }],
      tableDataRules: [],
      //专家
      searchItemsExpert: [
        {
          prop: "expertName",
          label: "姓名",
          type: "input",
          placeholder: "输入姓名",
          width: "250",
        },
      ],
      columnsExpert: [
        { prop: "expertType", label: "专家类型", text: true },
        { prop: "expertName", label: "姓名", text: true },
        { prop: "expertPhone", label: "电话", text: true },
      ],
      tableDataExpert: [
        {
          aa: "123",
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 5,
        total: 0,
      },
    };
  },

  watch: {
    tableTabsValue: {
      handler(val) {
        this.pagination.currentPage = 1;
        this.pagination.pageSize = 5;
        this.setSearchType(val);
        this.assistantQuery();
      },
      deep: true,
      immediate: true,
    },
    eventTypeOptions: {
      immediate: true, // 立即执行一次
      handler(newVal) {
        this.searchItemsAccident.forEach((item) => {
          if (item.prop === "eventType") {
            item.options = newVal;
          }
        });
      },
    },
  },
  methods: {
    // 智能辅助查询
    async assistantQuery() {
      const params = {
        searchType: this.searchType,
        ...this.searchParams,
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
      };
      try {
        const res = await warningPublishApi.assistantQuery(params);
        if (this.tableTabsValue === "0") {
          this.tableDataAccident = res.data.records || [];
        } else if (this.tableTabsValue === "1") {
          this.tableDataForecast = res.data.records || [];
        } else if (this.tableTabsValue === "2") {
          this.tableDataLaw = res.data.records || [];
        } else if (this.tableTabsValue === "3") {
          this.tableDataRules = res.data.records || [];
        } else if (this.tableTabsValue === "4") {
          this.tableDataExpert = res.data.records || [];
        }
        this.pagination.total = res.data.total;
      } catch (error) {}
    },
    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1;
      this.searchParams = row;
      this.assistantQuery();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.assistantQuery();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.assistantQuery();
    },
    resetForm() {
      this.$nextTick(() => {});
    },
    handleClose() {
      this.dialogVisible = false;
    },
    async getEventLevelOptions() {
      const res = await getItemList(eventLevel);
      this.eventLevelOptions = res.map((item) => ({
        label: item.itemName,
        value: item.id,
      }));
      this.searchItemsAccident.forEach((item) => {
        if (item.prop === "eventLevel") {
          item.options = this.eventLevelOptions;
        }
      });
    },
    formatterEventType(row) {
      const eventType = this.eventTypeOptions.find(
        (item) => item.value === row.eventType
      );
      return eventType?.label || row.eventType || "";
    },
    setSearchType(val) {
      switch (val) {
        case "0":
          this.searchType = 4;
          break;
        case "1":
          this.searchType = 3;
          break;
        case "2":
          this.searchType = 1;
          break;
        case "3":
          this.searchType = 2;
          break;
        case "4":
          this.searchType = 5;
          break;
      }
    },
  },
  mounted() {
    this.getEventLevelOptions();
    // this.assistantQuery();
  },
};
</script>

<style scoped>
</style>
