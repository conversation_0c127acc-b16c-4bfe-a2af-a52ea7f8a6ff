<template>
  <general-dialog
    :dialog-visible="detailDialogVisible"
    :dialog-width="'700px'"
    general-dialog-title="应急值守评价"
    :show-footer="false"
    @cancel="handleCancel"
  >
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
export default {
  components: {
    GeneralDialog,
  },
  data() {
    return {
      detailDialogVisible: false,
    };
  },
  methods: {
    handleCancel() {
      this.detailDialogVisible = false;
    },
  },
};
</script>

<style>
</style>