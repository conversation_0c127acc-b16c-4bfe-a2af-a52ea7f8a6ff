import request from "@/utils/request";
export default class voiceBroadcast {
  //查询语音广播模板
  static queryTemplatePage(data) {
    return request({
      url: "/ds/notification/templatePage",
      method: "post",
      data,
    });
  }

  //新增语音广播模板
  static createTemplate(data) {
    return request({
      url: "/ds/notification/template/add",
      method: "post",
      data,
    });
  }

  //更新语音广播模板
  static updateTemplate(data) {
    return request({
      url: "/ds/notification/template/update",
      method: "post",
      data,
    });
  }

  //删除语音广播模板
  static deleteTemplate(data) {
    return request({
      url: "/ds/notification/template/delete",
      method: "post",
      data,
    });
  }
}
