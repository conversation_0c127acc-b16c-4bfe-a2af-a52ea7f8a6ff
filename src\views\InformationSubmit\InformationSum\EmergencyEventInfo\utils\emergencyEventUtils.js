// 响应级别映射
export const responseLevelMap = {
  1: "一级响应",
  2: "二级响应", 
  3: "三级响应",
  4: "四级响应",
  5: "五级响应",
};

// 事件状态映射
export const infoStatusMap = {
  0: "正在处置",
  1: "启动响应", 
  2: "处置完毕",
};

// 构建查询参数
export const buildQueryParams = (searchParams, pagination) => {
  const formatToISO = (dateStr) => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? "" : date.toISOString();
  };

  return {
    page: pagination.currentPage,
    count: pagination.pageSize,
    infoEndTime: searchParams.infoTime?.[1]
      ? formatToISO(searchParams.infoTime[1])
      : "",
    infoStartTime: searchParams.infoTime?.[0]
      ? formatToISO(searchParams.infoTime[0])
      : "",
    infoTitle: searchParams.infoTitle || "",
    infoType: searchParams.infoType || "",
  };
};
