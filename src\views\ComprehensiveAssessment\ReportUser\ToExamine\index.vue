<template>
  <div class="to-examine-container">
    <div class="title">{{ title }}</div>
    <div class="subtitle">{{ subtitle }}</div>
    <div class="content">
      <el-card>
        <div class="card-title">
          <div class="title">评价列表</div>
          <div class="search">
            <el-input
              placeholder="单位名称/值班员姓名"
              v-model="searchText"
              prefix-icon="el-icon-search"
              clearable
              @keyup.enter="search"
            ></el-input>
            <el-select v-model="status" placeholder="请选择状态">
              <el-option label="全部状态" value="0"></el-option>
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </div>
        </div>
        <PortalTable
          :tableHeight="450"
          :columns="columns"
          :table-data="tableData"
          row-key="id"
          :pagination="pagination"
          :showAddButton="false"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        />
      </el-card>
    </div>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
export default {
  name: "ToExamine",
  components: {
    PortalTable,
  },
  data() {
    return {
      title: "综合评价管理",
      subtitle: "2025年度应急值守评价",
      searchText: "",
      status: "0",
      columns: [
        { text: true, prop: "name", label: "单位名称", minWidth: 280 },
        { text: true, prop: "criteria", label: "值班员", minWidth: 380 },
        { text: true, prop: "type", label: "自评分数", width: 100 },
        { text: true, prop: "object", label: "系统评分", width: 100 },

        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "查看详情",
              permission: "evaluationCriteria:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "审核通过",
              permission: "evaluationCriteria:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "更多",
              permission: "evaluationCriteria:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          id: "001",
          name: "市应急局",
          criteria: "李四",
          category: "85",
          object: "92",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
        {
          id: "002",
          name: "海淀区政府",
          criteria: "张三",
          category: "78",
          object: "85",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
        {
          id: "003",
          name: "朝阳区应急办",
          criteria: "赵六",
          category: "90",
          object: "88",
          score: "2分",
          method: "自评",
          source: "手动录入、导入",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
    };
  },
  methods: {
    handleEdit(row) {
      console.log("编辑", row);
    },
    handleDelete(row) {
      console.log("删除", row);
    },
    search() {},
    handleDetail(row) {
      console.log("详情", row);
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
    },
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage;
    },
  },
};
</script>

<style lang="scss" scoped>
.to-examine-container {
  height: 100%;
  padding: 20px;
  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
  }
  .content {
    width: 100%;
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .search {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>