<template>
  <div class="form-section">
    <!-- 事件详情表单组件 - Component -->
    <h4 class="section-title">
      <i class="el-icon-edit-outline"></i>
      事件详情
    </h4>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="事件详情" prop="eventDetail">
          <el-input
            :value="value"
            type="textarea"
            :rows="5"
            placeholder="请详细描述事件经过、现场情况、影响范围等信息..."
            maxlength="1000"
            show-word-limit
            resize="vertical"
            @input="handleInput"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "EventDetailForm",
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleInput(value) {
      this.$emit("input", value);
    },
  },
};
</script>

<style scoped>
.form-section {
  margin-bottom: 30px;
  padding: 0;
  background: #ffffff;
  border: none;
  position: relative;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0;
  color: #0a1629;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.section-title::before {
  display: none;
}

.section-title i {
  color: #4569af;
  font-size: 16px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea__inner {
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #ffffff;
  resize: vertical;
  min-height: 100px;
  line-height: 1.6;
}

.el-textarea__inner:hover {
  border-color: #c0c4cc;
}

.el-textarea__inner:focus {
  border-color: #4569af;
  box-shadow: none;
  outline: none;
}
</style>
