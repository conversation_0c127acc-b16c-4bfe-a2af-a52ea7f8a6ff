<template>
  <div class="contact-book-container">
    <!-- 搜索区域 -->
    <div class="search-wrapper">
      <SearchForm
        :form-config="searchConfig"
        :tips="searchTips"
        :show-tips="false"
        @search="handleSearch"
        @reset="handleReset"
        ref="searchFormRef"
      />
    </div>
    <el-tabs v-model="tabsValue" type="card" @edit="handleTabsEdit">
      <el-tab-pane label="统计分析" name="1" :key="1">
        <div class="tab-pane-box">
          <div class="tab-pane-item">
            <p>预警事件趋势分析</p>
            <div class="tab-pane-echarts">
              <Echart :options="lineOptions" />
            </div>
          </div>
          <div class="tab-pane-item">
            <p>预警事件统计表</p>
            <div class="tab-pane-echarts">
              <Echart :options="pieOptions" />
            </div>
          </div>
          <div class="tab-pane-item">
            <p>极端天气预警事件统计表</p>
            <div class="tab-pane-echarts">
              <Echart :options="barOptions" />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="统计数量" name="2" :key="2">
        <portal-table
          style="padding: 20px"
          :tableHeight="600"
          :showAddButton="false"
          :showSelection="false"
          :columns="columns"
          :pagination="pagination"
          :search-items="searchItems"
          :table-data="tableData"
          row-key="name"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 详情弹框 -->
    <contact-form
      ref="contactFormRef"
      :visible.sync="formVisible"
      :form-data="currentContact"
      :isEdit="isEdit"
      :eventTypeOptions="eventTypeOptions"
      @update:isEdit="isEdit = $event"
    />
  </div>
</template>

<script>
import SearchForm from "@/components/SearchForm.vue";
import Echart from "@/components/echarts.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import ContactForm from "@/views/WarningPublish/Access/components/ContactForm.vue";
import { warningPublishApi, systemManagementApi } from "@/api";
import { getItemList, alertLevel, eventType } from "@/utils/dictionary";
export default {
  name: "",
  props: {},
  components: {
    SearchForm,
    Echart,
    PortalTable,
    ContactForm,
  },
  data() {
    return {
      tabsValue: "1",
      // 搜索配置
      searchConfig: [
        {
          prop: "areaCode",
          label: "行政区划",
          type: "select",
          width: 15,
          placeholder: "请选择",
          options: [],
        },
        {
          prop: "year",
          label: "年度",
          type: "dateYear",
          width: 15,
          placeholder: "请选择",
          change: this.handleYearChange,
        },
        {
          prop: "month",
          label: "月度",
          type: "dateMonth",
          width: 15,
          placeholder: "请选择",
          change: this.handleMonthChange,
        },
        // {
        //   prop: "year",
        //   label: "年度",
        //   type: "select",
        //   placeholder: "请选择",
        //   options: [
        //     { label: "2025年", value: "2025" },
        //     { label: "2024年", value: "2024" },
        //     { label: "2023年", value: "2023" },
        //     { label: "2022年", value: "2022" },
        //   ],
        // },
        // {
        //   prop: "month",
        //   label: "月",
        //   type: "select",
        //   placeholder: "请选择",
        //   options: [
        //     { label: "1月", value: "1" },
        //     { label: "2月", value: "2" },
        //     { label: "3月", value: "3" },
        //     { label: "4月", value: "4" },
        //     { label: "5月", value: "5" },
        //     { label: "6月", value: "6" },
        //     { label: "7月", value: "7" },
        //     { label: "8月", value: "8" },
        //     { label: "9月", value: "9" },
        //     { label: "10月", value: "10" },
        //     { label: "11月", value: "11" },
        //     { label: "12月", value: "12" },
        //   ],
        // },
      ],
      searchParams: {},
      searchTips: [],
      lineOptions: {
        color: ["#FECE43"],
        grid: {
          top: "5%",
          left: "0%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "shadow" },
          valueFormatter(value) {
            return value + "条";
          },
        },
        xAxis: {
          type: "category",
          axisTick: {
            show: false,
            lineStyle: {
              color: "rgba(0,0,0,0.1)",
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(0,0,0,0.01)",
            },
          },
          axisLabel: {
            fontSize: 14,
            color: "rgba(0,0,0,0.6)",
          },
        },
        yAxis: {
          type: "value",
          // max: "100",
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(0,0,0,0.6)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(0,0,0,0.05)",
            },
          },
          axisLabel: {
            fontSize: 14,
            color: "rgba(0,0,0,0.6)",
          },
        },
        dataset: {
          source: [
            [
              "1月",
              "2",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
            [160, 150, 140, 150, 170, 162, 184, 150, 170, 162, 184, 170],
          ],
        },
        series: [
          {
            name: "发送数量",
            type: "line",
            smooth: true,
            symbolSize: 0,
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255,184,100,0.8)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255,58,76,1)",
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            seriesLayoutBy: "row",
            areaStyle: {
              color: {
                type: "linear",
                x: 1,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255,184,100,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255,58,76,0)",
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      },
      pieOptions: {
        // legend: {
        //   orient: "horizontal", // 图例横向排列
        //   bottom: 10, // 距离底部10px
        //   data: datas[0].map((item) => item.name),
        // },
        series: [],
      },
      barOptions: {
        color: ["#ffb767", "#f67c4d", "#ff3a4c"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: "5%",
          left: "0%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        legend: {
          data: ["高温", "空气", "大风", "暴雨"],
        },
        xAxis: {
          type: "category",
          data: ["高温", "空气", "大风", "暴雨"],
          axisTick: {
            show: false,
            lineStyle: {
              color: "rgba(0,0,0,0.1)",
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(0,0,0,0.01)",
            },
          },
          axisLabel: {
            fontSize: 14,
            color: "rgba(0,0,0,0.6)",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(0,0,0,0.6)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(0,0,0,0.05)",
            },
          },
          axisLabel: {
            fontSize: 14,
            color: "rgba(0,0,0,0.6)",
          },
        },
        series: [
          {
            type: "bar",
            barWidth: "20%",
            data: [1, 2, 3, 4],
          },
        ],
      },
      searchItems: [],
      columns: [
        { prop: "pushTime", label: "发布时间", text: true },
        { prop: "pushDeptName", label: "发布单位", text: true },
        { prop: "matter", label: "预警事项", text: true },
        {
          prop: "level",
          label: "预警等级",
          text: true,
          width: "160px",
          formatter: this.formatterAlertLevel,
        },
        { prop: "startDate", label: "起始时间", text: true },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "350px",
          operationList: [
            {
              label: "查看详情",
              permission: "",
              buttonClick: this.handleView,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          aa: "123",
        },
      ],
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      defaultForm: {
        year: 2025,
        month: 7,
        areaCode: "110101",
      },
      levelList: [],
      formVisible: false,
      isEdit: "add",
      eventTypeOptions: [],
      currentContact: {},
    };
  },
  computed: {},
  methods: {
    async getWarningPublishList() {
      try {
        const requestParams = {
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          // publishStatus: "1",
          ...this.searchParams,
        };
        const response = await warningPublishApi.getWarningPublishList(
          requestParams
        );
        this.tableData = response.data?.items;
        this.pagination.total = response.data?.total;
      } catch (error) {}
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.getWarningPublishList();
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getWarningPublishList();
    },
    handleTabsEdit() {},
    async handleSearch(formData) {
      this.searchParams = { ...formData };
      if (this.searchParams?.month) {
        this.searchParams.month = this.searchParams.month.split("-")[1];
      }
      this.getWarningPublishList();

      this.resetEcharts(formData);
    },
    async handleReset(formData) {
      this.searchParams = formData;
      this.getWarningPublishList();
      this.resetEcharts(formData);
    },
    async handleView(row) {
      this.formVisible = true;
      this.isEdit = "view";
      this.currentContact = await this.queryWarningPublish(row);
    },
    async queryWarningPublish(params = {}) {
      try {
        const response = await warningPublishApi.queryWarningPublish({
          id: params.id,
        });
        const durationMatch = response.data.duration.match(/(\d+)(\D+)/);
        response.data.duration = durationMatch ? durationMatch[1] : 0;
        this.$refs.contactFormRef.durationUnit = durationMatch
          ? durationMatch[2]
          : "天";
        this.$refs.contactFormRef.areaListProp = response.data.areaList.map(
          (item) => item?.areaId
        );

        return {
          ...response.data,
        };
      } catch (error) {
        throw error;
      }
    },
    resetEcharts() {},
    // 获取北京区域
    queryRegion() {
      try {
        warningPublishApi.queryRegion({ parent: 0 }).then((res) => {
          const areaList = res.data.map((item) => {
            return {
              label: item.townName,
              value: item.code,
            };
          });
          this.searchConfig.forEach((item) => {
            if (item.prop === "areaCode") {
              item.options = areaList;
            }
          });
        });
      } catch (error) {
        throw error;
      }
    },
    async queryAlertLevel() {
      let data = await getItemList(alertLevel);
      this.levelList = data;
    },
    formatterAlertLevel(row) {
      let level = this.levelList.find(
        (item) => Number(item.itemValue) === row.level
      );
      return level?.itemName || row.level || "";
    },
    async getEventTypeOptions() {
      const res = await getItemList(eventType);
      this.eventTypeOptions = res.map((item) => ({
        label: item.itemName,
        value: item.itemValue,
      }));
    },
    handleMonthChange(date) {
      if (date) {
        const monthValue = date.split("-")[0];
        this.$refs.searchFormRef.formData.year = monthValue;
      }
    },
    handleYearChange(date) {
      if (date) {
        if (this.$refs.searchFormRef.formData.month) {
          const [year, month] =
            this.$refs.searchFormRef.formData.month.split("-");
          this.$refs.searchFormRef.formData.month = `${date}-${month}`;
        }
      }
    },
  },
  mounted() {
    this.queryRegion();
    this.queryAlertLevel();
    this.getEventTypeOptions();
    this.getWarningPublishList();
    var datas = [
      [
        { name: "丰台区", value: 5.6 },
        { name: "东城区", value: 1 },
        { name: "西城区", value: 0.8 },
        { name: "通州区", value: 0.5 },
        { name: "大兴区", value: 0.5 },
        { name: "房山区", value: 3.8 },
        { name: "昌平区", value: 0.83 },
        { name: "海淀区", value: 2.8 },
      ],
    ];

    this.pieOptions.series = datas.map(function (data, idx) {
      var top = idx * 33.3;
      return {
        type: "pie",
        radius: [80, 120],
        top: top + "%",
        height: "100%",
        left: "center",
        width: 450,
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 1,
        },
        label: {
          alignTo: "edge",
          formatter: "{name|{b}}\n{time|{c} 小时}",
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 10,
              color: "#999",
            },
          },
        },
        labelLine: {
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80,
        },
        labelLayout: function (params) {
          const isLeft = params.labelRect.x < myChart.getWidth() / 2;
          const points = params.labelLinePoints;
          // Update the end point.
          points[2][0] = isLeft
            ? params.labelRect.x
            : params.labelRect.x + params.labelRect.width;
          return {
            labelLinePoints: points,
          };
        },
        data: data,
      };
    });
  },
};
</script>

<style scoped lang="scss">
.contact-book-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 20px;

  .search-wrapper {
    margin-bottom: 8px;
  }

  .tab-pane-box {
    display: flex;
    justify-content: space-around;

    .tab-pane-item {
      width: 32%;
      height: 400px;

      p {
        height: 56px;
        line-height: 56px;
        background: inherit;
        background-color: rgba(255, 255, 255, 1);
        box-sizing: border-box;
        border-width: 1px;
        border-style: solid;
        border-color: rgba(235, 238, 245, 1);
        border-radius: 0px;
        box-shadow: none;
        font-size: 20px;
        text-align: center;
      }

      .tab-pane-echarts {
        height: 300px;
        // background: red;
      }
    }
  }
}
</style>