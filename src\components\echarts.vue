<template>
    <div ref="element" class="container" ></div>
</template>

<script>
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { TitleComponent, Tooltip<PERSON>omponent, GridComponent, GraphicComponent, LegendComponent, ToolboxComponent, DatasetComponent, DataZoomComponent } from 'echarts/components'
import { SVGRenderer } from 'echarts/renderers'

echarts.use([LegendComponent, TitleComponent, TooltipComponent, Grid<PERSON>omponent, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>hart, SVGRenderer, ToolboxComponent, DatasetComponent, GraphicComponent, DataZoomComponent])

export default {
  props: {
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      charts: null
    }
  },
  watch: {
    options: {
      handler(value) {
        if (this.charts) {
          this.charts.setOption(value, true)
        }
      },
      deep: true
    }
  },
  methods: {
    resize() {
      if (this.charts) this.charts.resize()
    }
  },
  mounted() {
    this.$nextTick(()=>{
        this.charts = echarts.init(this.$refs.element, `default`, { renderer: `svg` })
        this.charts.setOption(this.options, true)
    })
    
    
    // // 图表点击事件
    // this.charts.on('click', (params) => {
    //   this.$emit(`echartClick`, params)
    // })
    
    window.addEventListener(`resize`, this.resize)
  },
  beforeDestroy() {
    window.removeEventListener(`resize`, this.resize)
    if (this.charts) {
      this.charts.dispose()
      this.charts = null
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
}
</style>