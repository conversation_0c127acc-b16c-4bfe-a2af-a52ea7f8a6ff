<!-- 
author：ghl
time:2025-01-23

应急演练分析统计
-->
<template>
    <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    props: {
        chartsThreeList: {
            type: Array,
            default: [],
        },
    },
    mounted() {
        this.initChart();
    },
    methods: {
        initChart() {

            var xData = []
            var color = ["#1a9bfc",  "#fa704d", "#01babc"];

            var name = ['新品', '堪用', '报废'];
            var yData = [
                [],
                [],
                [],
            ];
            this.chartsThreeList.map((item) => {
                xData.push(item.item_name.substring(0, 2))
                yData[0].push(item.newCount)
                yData[1].push(item.usableCount)
                yData[2].push(item.scrapCount)
            })

            var series = [];
            for (var i = 0; i < 3; i++) {
            series.push({
                name: name[i],
                type: "line",
                symbolSize: 3, //标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10[ default: 4 ]
                symbol: "circle", //标记的图形。ECharts 提供的标记类型包括 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'
                smooth: true, //是否平滑曲线显示
                showSymbol: false, //是否显示 symbol, 如果 false 则只有在 tooltip hover 的时候显示
                areaStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                        {
                        offset: 0,
                        color: color[i],
                        },
                        {
                        offset: 0.8,
                        color: "rgba(255,255,255,0)",
                        },
                    ],
                    false
                    ),
                    // shadowColor: 'rgba(255,255,255, 0.1)',
                },
                },
                itemStyle: {
                normal: {
                    color: color[i],
                    lineStyle: {
                    width: 2,
                        type: "solid", //'dotted'虚线 'solid'实线
                    },
                    label: {
                        show: false,
                    },
                },
                },
                data: yData[i],
            });
            }

            const chartDom = this.$refs.chart;
            const myChart = echarts.init(chartDom);
            const option = {
                legend: {
                    top: 0,
                    right:20,
                    itemGap: 5,
                    itemWidth: 5,
                    textStyle: {
                    // color: "#fff",
                    fontSize: "10",
                    },
                    data: name,
                },
                title: {
                    text: "",
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                        label: {
                            show: false,
                        },
                    },
                },
                grid: {
                    left: "2%",
                    top: "15%",
                    right: "2%",
                    bottom: "2%",
                    containLabel: true,
                },
                xAxis: [
                    {
                    type: "category",
                    axisLine: {
                        lineStyle: {
                            color: '#B2C2D3'  // 将 X 轴颜色设置为红色
                        }
                    },
                    splitLine: {
                        show: false,
                    },
                    boundaryGap: false, //坐标轴两边留白策略，类目轴和非类目轴的设置和表现不一样
                    axisTick: {
                        show: false,
                    },
                    splitArea: {
                        show: false,
                    },
                    
                    axisLabel: {
                        inside: false,
                        textStyle: {
                            color: "#000000",
                            fontWeight: "normal",
                            fontSize: "12",
                        },
                    },
                    data: xData,
                    },
                ],
                yAxis: {
                    name:'单位(件)',
                    nameTextStyle: {
                        color: "#000000",
                        fontSize: 12,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(160,160,160,0.3)",
                            type:'dashed'//背景色为虚线
                        },
                    },
                    axisLabel: {
                        textStyle: {
                            color: "#000000",
                            fontWeight: "normal",
                            fontSize: "12",
                        },
                    },
                },
                series: series,
            };


            myChart.setOption(option);
        }
    }
}

</script>
<style>
</style>