<template>
    <div>
        <general-dialog :dialog-visible="dialogVisible" :general-dialog-title="'转发'" dialog-width="800px"
            @confirm="handleSubmit" @cancel="handleClose">
            <el-form class="add-form" ref="form" :model="form" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="短信主题">{{ form.theme }}</el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="发送单位"> {{ form.editor }} </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="发送人"> {{ form.reviewer }} </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="发送时间"> {{ form.planSendTime }} </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="短信内容"> {{ form.content }} </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="收信人" prop="newPersonal">
                            <div class="personal">
                                <InputTag placeholder="请选择收信人" v-model="form.newPersonal" style="flex: 1;"></InputTag>
                                <el-button @click="handleAction('personnelGrouping')"
                                    class="groupingButton">选择分组</el-button>
                                <el-button @click="handleAction('addUser')">添加人员</el-button>
                            </div>

                        </el-form-item>

                    </el-col>
                </el-row>
            </el-form>
        </general-dialog>
        <PersonnelGrouping ref="personnelGroupingRef" :visible.sync="personnelGroupingVisible"
            :modelValue="form.newPersonal" @update:modelValue="form.newPersonal = $event"></PersonnelGrouping>
        <AddUser :visible.sync="addUserVisible" :modelValue="form.newPersonal"
            @update:modelValue="form.newPersonal = $event">
        </AddUser>
    </div>
</template>

<script>
import InputTag from '@/components/InputTag.vue'
import GeneralDialog from '@/components/GeneralDialog.vue'
import AddUser from '../../Components/addUser.vue'
import PersonnelGrouping from '../../Components/personnelGrouping.vue'
import {textMessageManagementApi} from '@/api/index.js'
export default {
    name: '',
    components: {
        GeneralDialog,
        InputTag,
        AddUser,
        PersonnelGrouping
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        formData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            form: {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                newPersonal: [],
            },
            rules: {
                newPersonal: [
                    {
                        required: true,
                        message: ' ',
                    },
                ],

            },
            eventList: [],
            personnelGroupingVisible: false,//选择分组状态
            addUserVisible: false,
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
    },
    watch: {
        formData: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.form = { ...this.form, ...val };
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        resetForm() {
            this.form = {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                newPersonal: [],
            }
            this.$refs['form'].resetFields();
        },
        // 保存
        handleSubmit() {
            this.$refs['form'].validate(async (valid) => {
                if (valid) {
                    const formData = { ...this.form };
                    let data = {
                        taskId: formData.taskId,
                        detailList: formData.newPersonal
                    }
                    let response = await textMessageManagementApi.forward(data)
                    if (response.code == 0) {
                        this.resetForm();
                        this.$emit("submit");
                        this.$message.success('转发短信成功')
                    }
                } else {

                    return false;
                }
            });
        },
        // 关闭
        handleClose() {
            this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.dialogVisible = false;
                    this.resetForm();
                })
                .catch(() => {
                    // 取消关闭
                })
        },
        handleAction(type) {
            switch (type) {
                case 'personnelGrouping':
                    this.personnelGroupingVisible = true
                    this.$nextTick(() => {
                        this.$refs.personnelGroupingRef.incoming(this.form.newPersonal)
                    })
                    break;
                case 'addUser':
                    this.addUserVisible = true
                    break;
                default:
                    break;
            }

        }
    },
    mounted() {
    }
}
</script>

<style scoped>
.personal {
    display: flex;
}

.groupingButton {
    margin-left: 10px;
}
</style>