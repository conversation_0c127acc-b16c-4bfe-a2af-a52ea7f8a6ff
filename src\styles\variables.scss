// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;
$white: #ffffff;
$black: #000000;
$themeColor: #507ac2; //主题色
$green: #3058ee; //主题色 2
$yellow: #35363A; //主题色 3
$decolorization: grayscale(0%); //去色值
//$orange: #eb4d0f; //主题色 4
$lightGray1: rgba($themeColor, 0.1);
$lightGray2: rgba($themeColor, 0.2);
$lightGray3: rgba($themeColor, 0.3);
$lightGray4: rgba($themeColor, 0.4);
$lightGray5: rgba($themeColor, 0.5);
$lightGray6: rgba($themeColor, 0.6);
$lightGray7: rgba($themeColor, 0.7);
$lightGray8: rgba($themeColor, 0.8);
$lightGray9: rgba($themeColor, 0.9);

$personality: #d8e0f0ff;
$personalityBackground: #f7f8faff;

//radius
$radiusFive: 5px;
$radiusTen: 10px;
$radiusFifteen: 15px;
$radiusTwenty: 20px;

// sidebar
$menuText: #edf0f5;
$menuActiveText: #fff;
$subMenuActiveText: #fff; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #801d1400;
$menuHover: #b0312500;

$subMenuBg: #801d1400;
$subMenuHover: #b0312500;

$sideBarWidth: 210px;

// dialog
$dialogHeight: 756px;
$dialogTitleColor: #0a1629;

//tabs
$tabsItemWidth: 80px;
$tabsItemHeight: 100px;

//dialog和tabs最小高度
$tabsAndDialogMinHeight: 356px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;

  themeColor: $themeColor; //主题色
  green: $green; //主题色 2
  yellow: $yellow; //主题色 3
  //orange: $orange; //主题色 4
}
