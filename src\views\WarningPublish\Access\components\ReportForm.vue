<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    general-dialog-title="提交报批"
    dialog-width="800px"
    @cancel="handleClose"
    @confirm="handleSubmit"
  >
    <!-- class="add-form" -->
    <el-form
      ref="form"
      class="add-form"
      :model="form"
      :rules="rules"
      label-width="110px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="呈批单位" prop="reportDept">
            <el-input
              v-model="form.reportDept"
              placeholder="请输入呈批单位"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="请示名称" prop="requestName">
            <el-input
              v-model="form.requestName"
              placeholder="请输入请示名称"
              size="small"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情况说明" prop="describe">
            <el-input
              v-model="form.describe"
              placeholder="请输入情况说明"
              type="textarea"
              resize="none"
              :rows="5"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传附件" prop="fileName">
            <el-upload
              ref="upload"
              class="upload-demo"
              action="#"
              drag
              :http-request="customUpload"
              :before-upload="beforeUpload"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                只能上传pdf文件，<em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="请示领导" prop="leaderName">
            <el-cascader
              v-model="form.leaderName"
              :props="cascaderProps"
              :options="options"
              :show-all-levels="false"
              clearable
              placeholder="选择请示领导"
              @change="handleChange"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { getItemList, textMessageTemplateType } from "@/utils/dictionary";
import {
  warningPublishApi,
  systemManagementApi,
  emergencyKnowledgeBaseApi,
} from "@/api";
export default {
  name: "ContactForm",
  components: {
    GeneralDialog,
  },
  data() {
    return {
      form: {
        warningId: "",
        reportDept: "",
        requestName: "",
        leaderId: "",
        leaderDeptName: "",
        leaderDeptId: "",
        leaderName: "",
        describe: "",
        fileName: "",
        fileUrl: "",
      },
      rules: {
        reportDept: [
          {
            required: true,
            message: "请输入呈批单位",
            trigger: "blur",
          },
        ],
        requestName: [
          {
            required: true,
            message: "请输入请示名称",
            trigger: "blur",
          },
        ],
        describe: [
          {
            required: true,
            message: "请输入情况说明",
            trigger: "blur",
          },
        ],
        fileName: [
          {
            required: true,
            message: "请上传附件",
            trigger: "change",
          },
        ],
        leaderName: [
          {
            required: true,
            message: "请选择请示领导",
            trigger: "change",
          },
        ],
      },
      leadership: [], //级别
      dialogVisible: false,
      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        checkStrictly: true,
        lazy: true, // 启用懒加载
        emitPath: false,
        lazyLoad: this.loadChildren,
        // 自定义节点显示内容
        renderLabel: ({ data }) => {
          return data.type === 1
            ? `${data.userName} (${data.phone})`
            : data.orgName;
        },
      },
      options: [],
      nodeMap: new Map(),
      selectedNode: null,
    };
  },
  mounted() {},

  methods: {
    resetForm() {
      this.form = {
        warningId: "",
        reportDept: "",
        requestName: "",
        leaderId: "",
        leaderDeptName: "",
        leaderDeptId: "",
        leaderName: "",
        describe: "",
        fileName: "",
        fileUrl: "",
      };
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            this.form.leaderId = this.selectedNode.id;
            this.form.leaderDeptName = this.selectedNode.orgName;
            this.form.leaderDeptId = this.selectedNode.parentId;
            this.form.leaderName = this.selectedNode.userName;
            warningPublishApi.submitForApproval(this.form).then((res) => {
              if (res.code === 0) {
                this.$message.success("提交成功");
                this.dialogVisible = false;
                this.$emit("initData");
              } else {
                this.$message.error(res.msg);
              }
            });
          } catch {}
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },
    handleChange(value) {
      if (value) {
        this.selectedNode = this.nodeMap.get(value);
      } else {
        this.selectedNode = null;
      }
    },
    // 懒加载子节点
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map((item) => ({
            ...item,
            disabled:
              item.type === 0 && item.children && item.children.length === 0, // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({
          orgId: data?.id || 0,
        });
        const nodes = response.data
          .filter((item) => {
            if (item.type === 1 && !item.orgName) {
              return false;
            } else {
              return true;
            }
          })
          .map((item) => {
            this.nodeMap.set(item.id, item); // 存储节点映射
            // 用户节点
            if (item.type === 1) {
              return {
                ...item,
                orgName: item.userName || "未命名用户",
                leaf: true,
              };
            }
            // 部门节点
            return {
              ...item,
              disabled:
                item.type === 0 &&
                (!item.children || item.children.length === 0), // 空部门禁用
            };
          });
        resolve(nodes);
      } catch (error) {
        console.error("加载子节点失败:", error);
        resolve([]);
      }
    },
    async customUpload(options) {
      const formData = new FormData();
      formData.append("file", options.file);
      try {
        const res = await emergencyKnowledgeBaseApi.uploadFile(formData);
        if (res.code === 0) {
          this.form.fileName = res.data.fileName;
          this.form.fileUrl = res.data.fileUrl;
          this.$nextTick(() => {
            this.$refs.form.validateField("fileName"); // 手动触发 fileName 字段验证
          });
        }
      } catch (error) {
        console.error("上传失败", error);
      }
    },
    beforeUpload(file) {
      const ext = file.name.split(".").pop().toLowerCase();
      const targetExtensions = ["pdf"];
      const includes = targetExtensions.includes(ext);
      if (!includes) {
        this.$message.warning("上传文件只能是 pdf 格式!");
      }
      return includes;
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件，新上传的文件将替换当前文件");
      this.$refs.upload.clearFiles();
      const file = files[0];
      this.$refs.upload.handleStart(file);
      this.customUpload({ file });
    },
    handleRemove(file, fileList) {
      this.form.fileName = "";
      this.form.fileUrl = "";
    },
  },
};
</script>

<style scoped>
.sort {
  width: 100%;
}

/* .dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
} */
</style>
