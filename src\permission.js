// import router from './router'
// import NProgress from 'nprogress' // 进度条
// import 'nprogress/nprogress.css' // 进度栏样式
// import { getToken } from '@/utils/auth' // 从cookie获取令牌
// import getPageTitle from '@/utils/get-page-title'
// import store from '@/store'

// NProgress.configure({ showSpinner: false }) // NProgress配置

// const whiteList = ['/login', '/register', '/404', '/401'] // 没有重定向白名单

// // const isPageRefresh = !window.performance.navigation.type === 1;

// router.beforeEach(async(to, from, next) => {
//   // 开始进度条
//   NProgress.start()

//   // 设置页面标题
//   document.title = getPageTitle(to.meta.title)
  
//   // 确定用户是否已登录
//   const hasToken = getToken()
//   if (hasToken) {
//     if (to.path === '/login') {
//       // 如果已登录，请重定向到主页
//       next({ path: '/home' })
//       NProgress.done()
//     } else {
//       next()
//     }
//   } else {
//     /* 没有token*/
//     if (whiteList.indexOf(to.path) !== -1) {
//       // 在免费登录白名单中，直接进入
//       next()
//     } else {
//       // 其他无权访问的页面将被重定向到登录页面。
//       next({ path: '/login', query: {} })
//       NProgress.done()
//     }
//   }
// })

// router.afterEach(() => {
//   // 完成进度栏
//   NProgress.done()
// })
