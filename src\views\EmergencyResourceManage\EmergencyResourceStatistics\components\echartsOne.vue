<!-- 
author：ghl
time:2025-01-23

应急演练分析统计
-->
<template>
    <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    props: {
        chartsOneList: {
            type: Array,
            default: [],
        },
    },
    mounted() {
        this.initChart()
    },
    methods: {
        initChart() {
           
            var xAxisData = [];
            var seriesData1 = [];
            this.chartsOneList.forEach((item, index) => {
                if (index < 13) {
                    xAxisData.push(item.equipmentName.substring(0, 4));
                    seriesData1.push(item.equipmentNum);
                }
                
            });

            const chartDom = this.$refs.chart;
            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: "",
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                        label: {
                            show: false,
                        },
                    },
                },
                grid: {
                    left: "2%",
                    top: "15%",
                    right: "2%",
                    bottom: "2%",
                    containLabel: true,
                },
                xAxis: {
                    data: xAxisData,
                    gap:50,
                    splitLine: {
                        show: false,
                    },
                    // boundaryGap: false, //坐标轴两边留白策略，类目轴和非类目轴的设置和表现不一样
                    axisTick: {
                        show: false,
                    },
                    splitArea: {
                        show: false,
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#B2C2D3'  // 将 X 轴颜色设置为红色
                        }
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            fontSize: 11,
                            fontWeight: "normal",
                            // color: "#ffffff",
                        },
                        formatter:function(value) {
                            return value.split("").join("\n")
                        },
                    },
                },
                yAxis: {
                    name:'单位(件)',
                    nameTextStyle: {
                        // color: "#ffffff",
                        fontSize: 12,
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(160,160,160,0.3)",
                            type:'dashed'//背景色为虚线
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisLabel: {
                        textStyle: {
                            fontSize: 12,
                            fontWeight: "normal",
                            // color: "#ffffff",
                        },
                    },
                },
                series: [
                    // {
                    // name: "柱顶部",
                    // // type: "pictorialBar",
                    // type: "bar",
                    // // symbolSize: [13, 6],
                    // // symbolOffset: [0, -5],
                    // // z: 12,
                    // itemStyle: {
                    //     normal: {
                    //         color: '#02c3f1'
                    //     },
                    // },
                    // label: {
                    //     show: true,
                    //     position: "top",
                    //     fontSize: 16,
                    // },
                    // symbolPosition: "end",
                    // data: seriesData1,
                    // },
                    // 老版本
                    // {
                    // name: "柱底部",
                    // type: "pictorialBar",
                    // symbolSize: [13, 6],
                    // symbolOffset: [0, 5],
                    // z: 12,
                    // itemStyle: {
                    //     normal: {
                    //         color: '#02c3f1'
                    //     },
                    // },
                    // data: seriesData1,
                    // },


                    // {
                    // name: "第一圈",
                    // type: "pictorialBar",
                    // symbolSize: [20, 8],
                    // symbolOffset: [0, 0],
                    // z: 11,
                    // itemStyle: {
                    //     normal: {
                    //     color: "transparent",
                    //     borderColor: "#3ACDC5",
                    //     borderWidth: 2,
                    //     },
                    // },
                    // data: seriesData1,
                    // },
                    // {
                    // name: "第二圈",
                    // type: "pictorialBar",
                    // symbolSize: [30, 10],
                    // symbolOffset: [0, 4],
                    // z: 10,
                    // itemStyle: {
                    //     normal: {
                    //     color: "transparent",
                    //     borderColor: '#02c3f1',
                    //     borderWidth: 2,
                    //     },
                    // },
                    // data: seriesData1,
                    // },
                    {
                    type: "bar",
                    itemStyle: {
                        normal: {
                        borderWidth:2,
                        borderColor:'#f0e034',
                        color: function (params) {
                            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(249, 231, 31, 1)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(249, 231, 31, .1)',
                            },
                            ]);
                            
                        },
                        // opacity: 0.8,
                        },
                    },
                    showSymbol: false,
                    barWidth: 13,
                    data: seriesData1,
                    },
                ],
            };


            myChart.setOption(option);
        }
    }
}

</script>
<style>
</style>