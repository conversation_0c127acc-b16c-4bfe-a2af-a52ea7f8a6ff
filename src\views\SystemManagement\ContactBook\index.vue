<template>
  <div class="org-management-container">
    <!-- 通讯录 - Page -->

    <div class="org-management-main">
      <!-- 左侧树形结构 -->
      <div class="left-panel" :class="{ collapsed: isTreeCollapsed }">
        <div class="tree-header">
          <el-button type="text" @click="toggleTree" class="collapse-btn">
            <i
              :class="isTreeCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            ></i>
          </el-button>
        </div>
        <div
          v-show="!isTreeCollapsed"
          class="tree-content"
          v-loading="treeLoading"
          element-loading-text="加载组织架构中..."
          element-loading-spinner="el-icon-loading"
        >
          <TreePanel
            ref="treePanel"
            :tree-data="orgTreeData"
            :tree-props="{ children: 'children', label: 'orgName' }"
            :show-search="true"
            :show-actions="false"
            :expand-all="true"
            @node-click="handleOrgClick"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <portal-table
        style="width: 100%"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        :loading="loading"
        row-key="id"
        :selection="true"
        :show-dropdown="true"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import TreePanel from "@/components/TreePanel.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import { orgApi } from "@/api/index.js";

export default {
  name: "OrganizationManagement",
  components: {
    TreePanel,
    PortalTable,
  },
  data() {
    return {
      tableData: [],
      orgTreeData: [],
      loading: false,
      treeLoading: false,
      searchKeyword: "",
      selectedRows: [], // 选中的行数据

      columns: [
        { text: true, prop: "orgName", label: "所属机构" },
        { text: true, prop: "userName", label: "用户姓名" },
        { text: true, prop: "phone", label: "联系电话（手机）" },
        { text: true, prop: "phoneTel", label: "联系电话（座机）" },
        { text: true, prop: "positions", label: "用户职务" },
        { text: true, prop: "userClass", label: "用户分级" },
        { text: true, prop: "remark", label: "用户描述" },
      ],
      searchItems: [
        {
          prop: "orgName",
          label: "所属机构",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "userName",
          label: "用户名称",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "phone",
          label: "联系电话",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "positions",
          label: "用户职务",
          type: "select",
          placeholder: "请选择",
          options: [],
        },
        {
          prop: "remark",
          label: "用户描述",
          type: "input",
          placeholder: "请输入",
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },

      currentEditData: null,
      selectedOrgId: null,
      isTreeCollapsed: false,
    };
  },
  computed: {},
  mounted() {
    this.initData();
  },

  methods: {
    // 初始化数据
    async initData() {
      try {
        await this.loadOrgTree();
        await this.loadUserList();
      } catch (error) {
        console.error("组织机构管理页面初始化失败:", error);
        this.$message.error("页面初始化失败，请刷新重试");
      }
    },

    // 加载组织机构树
    async loadOrgTree() {
      try {
        this.treeLoading = true;

        const params = {
          id: "",
          orgName: "",
          orderRule: 0,
          parentId: "",
        };

        const response = await orgApi.queryOrgTree(params);
        // console.log("树API响应数据:", JSON.stringify(response, null, 2));

        if (response && response.code === 0) {
          this.orgTreeData = response.data || [];
          // console.log(
          //   "处理后的树数据:",
          //   JSON.stringify(this.orgTreeData, null, 2)
          // );
          // 默认选中第一个节点
          // this.setDefaultSelectedNode();
        } else {
          this.$message.error(response?.message || "加载组织架构失败");
          // 如果API失败，使用备用模拟数据
          // this.loadFallbackTreeData();
        }
      } catch (error) {
        this.$message.error("加载组织架构失败，请重试");
        // 如果API失败，使用备用模拟数据
        // this.loadFallbackTreeData();
      } finally {
        this.treeLoading = false;
      }
    },

    // 备用模拟数据（当API失败时使用）
    loadFallbackTreeData() {
      // 默认选中第一个节点
      this.setDefaultSelectedNode();
    },

    // 设置默认选中的树节点
    setDefaultSelectedNode() {
      if (this.orgTreeData && this.orgTreeData.length > 0) {
        // 选中第一个根节点
        const firstNode = this.orgTreeData[0];
        this.selectedOrgId = firstNode.id;
        console.log(
          "默认选中第一个节点:",
          firstNode.orgName,
          "ID:",
          firstNode.id
        );

        // 在下一个tick中设置树的选中状态
        this.$nextTick(() => {
          this.setTreeCurrentNode(firstNode.id);
        });

        // 加载该节点的列表数据
        this.loadUserList();
      }
    },

    // 设置树的当前选中节点
    setTreeCurrentNode(nodeId) {
      try {
        if (this.$refs.treePanel && this.$refs.treePanel.$refs.tree) {
          this.$refs.treePanel.$refs.tree.setCurrentKey(nodeId);
          console.log("设置树当前选中节点:", nodeId);
        } else {
          console.warn("树组件引用不存在，无法设置当前节点");
        }
      } catch (error) {
        console.error("设置树当前节点失败:", error);
      }
    },

    // 加载组织机构列表 - 使用真实API数据
    async loadUserList() {
      try {
        this.loading = true;

        // 根据API文档，列表接口参数 - 动态参数
        const params = {
          count: this.pagination.pageSize, // 每页条数
          page: this.pagination.currentPage, // 页数从1开始
          orgId: this.selectedOrgId || "", // 选中的组织机构ID
          orgName: this.searchKeyword || "", // 组织机构名称搜索关键字
        };

        // console.log("查询参数:", JSON.stringify(params, null, 2));
        // 调试：打印查询参数
        // console.log("查询参数:", JSON.stringify(params, null, 2));

        const response = await orgApi.querySysUserList(params);
        // console.log("API响应数据:", JSON.stringify(response, null, 2));

        if (response && response.code === 0) {
          // 检查数据结构并适配
          let tableData = [];
          let total = 0;

          if (response.data) {
            // 如果data是数组，直接使用
            if (Array.isArray(response.data)) {
              tableData = response.data;
              total = response.data.length;
            }
            // 如果data是对象且包含items
            else if (response.data.items) {
              tableData = response.data.items;
              total = response.data.total || response.data.items.length;
            }
            // 如果data是对象且包含list
            else if (response.data.list) {
              tableData = response.data.list;
              total = response.data.total || response.data.list.length;
            }
            // 其他情况，尝试直接使用data
            else {
              tableData = [response.data];
              total = 1;
            }
          }

          this.tableData = tableData;
          this.pagination.total = total;

          // console.log("处理后的表格数据:", JSON.stringify(tableData, null, 2));
          // console.log("总数:", total);
        } else {
          this.$message.error(response?.message || "加载组织机构列表失败");
          // 如果API失败，使用备用模拟数据
          this.loadFallbackListData();
        }
      } catch (error) {
        this.$message.error("加载组织机构列表失败，请重试");
        // 如果API失败，使用备用模拟数据
        this.loadFallbackListData();
      } finally {
        this.loading = false;
      }
    },

    // 备用列表模拟数据（当API失败时使用）
    loadFallbackListData() {
      this.tableData = [];
      this.pagination.total = 0;
    },

    // 组织架构树节点点击
    handleOrgClick(data) {
      this.selectedOrgId = data.id;
      // 点击树节点时重新加载列表数据
      this.pagination.currentPage = 1; // 重置到第一页
      this.loadUserList();
    },

    // 搜索
    handleSearch(formData) {
      this.searchKeyword = formData.orgName || "";
      this.pagination.currentPage = 1; // 搜索时重置到第一页
      this.loadUserList(); // 重新加载数据
    },

    // 分页相关方法
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.loadUserList(); // 重新加载数据
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.loadUserList(); // 重新加载数据
    },

    // 切换树形面板折叠状态
    toggleTree() {
      this.isTreeCollapsed = !this.isTreeCollapsed;
    },

    // 选择变化处理
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
  },
};
</script>

<style lang="scss" scoped>
.org-management-container {
  width: 99%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.org-management-main {
  flex: 1;
  display: flex;
  gap: 8px;
  padding: 8px;
}

.left-panel {
  width: 260px;
  flex-shrink: 0;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 40px;
  overflow: hidden;
  max-width: 260px;
}

.left-panel.collapsed {
  width: 40px;
  min-width: 40px;
}

.tree-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--content-bg);
  border: none;
}

.collapse-btn {
  padding: 8px;
  color: #606266;
  font-size: 16px;
}

.collapse-btn:hover {
  color: #409eff;
}

.tree-content {
  flex: 1;
  overflow: auto;
  height: calc(100% - 40px);
  max-height: calc(100vh - 200px); /* 确保不超过视口高度 */
  position: relative; /* 为loading定位 */

  // 自定义loading样式
  .el-loading-spinner {
    .el-loading-text {
      color: #606266;
      font-size: 14px;
      margin-top: 15px;
    }

    .circular {
      width: 36px;
      height: 36px;
      animation: loading-rotate 2s linear infinite;
    }

    .path {
      stroke: var(--themeColor);
      stroke-width: 2;
      stroke-dasharray: 90, 150;
      stroke-dashoffset: 0;
      stroke-linecap: round;
      animation: loading-dash 1.5s ease-in-out infinite;
    }
  }

  @keyframes loading-rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 150;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -124;
    }
  }
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.search-wrapper {
  margin-bottom: 8px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .org-management-main {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    max-width: none;
    height: 200px;
  }

  .left-panel.collapsed {
    height: 40px;
  }
}
</style>
