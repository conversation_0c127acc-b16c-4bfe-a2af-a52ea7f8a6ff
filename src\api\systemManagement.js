import request from "@/utils/request";

export default {
  /**
   *  数据字典相关接口
   */
  queryItemListById(params) {
    return request({
      url: "/ds/systemDictionary/queryItemListById",
      method: "post",
      data: params,
    });
  },

  /**
   * 数据字典
   * 查询区域、街道分页列表
   */
  queryTownsBeijingList(params) {
    return request({
      url: "/ds/townsBeijing/query",
      method: "post",
      data: params,
    });
  },

  // 下载模板 1 组织机构表 2 用户信息表 3 评价通知信息
  systemDownloadTemplate(type) {
    return request({
      url: "/ds/download/template?type=" + type,
      method: "get",
      responseType: 'blob',
    });
  },

  /**
   *  文件上传
   */
  uploadFile(params) {
    return request({
      url: "/ds/upload/uploadFile",
      method: "post",
      data: params,
    });
  },

  /**
   * 菜单相关
   */
  // 根据用户查询菜单
  getUserMenu(userId) {
    return request({
      url: `/ds/menu/getUserMenu?userId=${userId}`,
      method: "get",
    });
  },
  // 查询所有菜单 /ds/menu/getMenu
  getMenu(params) {
    return request({
      url: "/ds/menu/getMenu",
      method: "post",
      data: params,
    });
  },
  // 添加菜单 /ds/menu/getMenu
  createMenu(params) {
    return request({
      url: "/ds/menu/createMenu",
      method: "post",
      data: params,
    });
  },
  //删除菜单
  deleteMenu(params) {
    return request({
      url: "/ds/menu/deleteMenu",
      method: "post",
      data: params,
    });
  },
  //修改菜单
  updateMenu(params) {
    return request({
      url: "/ds/menu/updateMenu",
      method: "post",
      data: params,
    });
  },

  /**
   * 查询IP登录管理
   * @param {Object} params - 查询参数对象
   * @returns {Promise} 返回请求的Promise对象
   */
  queryIpLoginPage(params) {
    return request({
      url: "/ds/ipLogin/queryIpLoginPage",
      method: "post",
      data: params,
    });
  },
  /**
   * 添加IP登录管理
   * @param {Object} params - 添加参数对象
   * @returns {Promise} 返回请求的Promise对象
   */
  addIpLogin(params) {
    return request({
      url: "/ds/ipLogin/createIpLogin",
      method: "post",
      data: params,
    });
  },

  /**
   * IP免密登录
   * @param {Object} params - 登录参数对象，包含ipAddress
   * @returns {Promise} 返回请求的Promise对象
   */
  doIpLogin(params) {
    return request({
      url: "/ds/doIpLogin",
      method: "post",
      data: params,
    });
  },

  /**
   * 发送短信验证码
   * @param {Object} params - 参数对象，包含phone
   * @returns {Promise} 返回请求的Promise对象
   */
  doSmsMessage(params) {
    return request({
      url: "/ds/doSmsMessage",
      method: "post",
      data: params,
    });
  },

  /**
   * 短信验证码登录
   * @param {Object} params - 登录参数对象，包含phone和code
   * @returns {Promise} 返回请求的Promise对象
   */
  doSmsLogin(params) {
    return request({
      url: "/ds/doSmsLogin",
      method: "post",
      data: params,
    });
  },

  /**
   * 门户跳转中屏免登录
   * @param {Object} params - 登录参数对象，包含tokenCode
   * @returns {Promise} 返回请求的Promise对象
   */
  doCodeLogin(params) {
    return request({
      url: "/ds/doCodeLogin",
      method: "post",
      data: params,
    });
  },


  // 根据用户名字模糊查询用户列表
  getUserListByName(params) {
    return request({
      url: "/ds/user/getUserListByName",
      method: "post",
      data: params,
    });
  },
  // 查询角色列表
  queryRolePage(params) {
    return request({
      url: "/ds/role/queryRolePage",
      method: "post",
      data: params,
    });
  },
  // 添加角色
  createRole(params) {
    return request({
      url: "/ds/role/createRole",
      method: "post",
      data: params,
    });
  },
  // 注销角色/ds/role/disableRole
  disableRole(params) {
    return request({
      url: "/ds/role/disableRole",
      method: "post",
      data: params,
    });
  },
  // 修改角色
  updateRole(params) {
    return request({
      url: "/ds/role/updateRole",
      method: "post",
      data: params,
    });
  },

  // 查询分配菜单(菜单默认选中)
  queryRoleMenu(params) {
    return request({
      url: "/ds/roleMenu/queryRoleMenu",
      method: "post",
      data: params,
    });
  },
  // 分配菜单(提交按钮)
  createRoleMenu(params) {
    return request({
      url: "/ds/roleMenu/createRoleMenu",
      method: "post",
      data: params,
    });
  },
  // 查询组织机构树形结构
  queryOrg(params) {
    return request({
      url: "/ds/org/queryOrg",
      method: "post",
      data: params,
    });
  },
  // 查询分配数据
  queryRoleOrg(params) {
    return request({
      url: "/ds/roleOrg/queryRoleOrg",
      method: "post",
      data: params,
    });
  },
  // 分配数据
  createRoleOrg(params) {
    return request({
      url: "/ds/roleOrg/createRoleOrg",
      method: "post",
      data: params,
    });
  },
  // 查询分组管理
  queryGroupManagePage(params) {
    return request({
      url: "/ds/groupManage/queryGroupManagePage",
      method: "post",
      data: params,
    });
  },
  // 添加分组管理
  createGroupManage(params) {
    return request({
      url: "/ds/groupManage/createGroupManage",
      method: "post",
      data: params,
    });
  },
  // 删除分组管理
  deleteGroupManage(params) {
    return request({
      url: "/ds/groupManage/deleteGroupManage",
      method: "post",
      data: params,
    });
  },
  // 修改分组管理
  updateGroupManage(params) {
    return request({
      url: "/ds/groupManage/updateGroupManage",
      method: "post",
      data: params,
    });
  },
  // 添加分组人员
  addUsers(params) {
    return request({
      url: "/ds/groupManage/addUsers",
      method: "post",
      data: params,
    });
  },
  // 查询通讯录列表列表(查询人员ids功能使用)
  queryAddressBookList(params) {
    return request({
      url: "/ds/addressBook/queryAddressBookList",
      method: "post",
      data: params,
    });
  },
  // 查询组织架构通讯录列表
  queryOrgAddressBook(params) {
    return request({
      url: "/ds/addressBook/queryOrgAddressBook",
      method: "post",
      data: params,
    });
  },
};
