import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementUI from "element-ui";
import { Message } from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import "./styles/global.css";
import { auth } from "@/utils";
import directive from "./directive"; // directive
import permissionMixin from "@/mixins/permission"; // 权限混入
import "@/styles/index.scss"; // 全局CSS
import "@/styles/uni-portal.scss"; //公共样式变量

// 配置 Vue 开发工具
Vue.config.productionTip = false;
Vue.config.devtools = process.env.NODE_ENV === "development";

Vue.use(ElementUI);
Vue.use(directive);
Vue.mixin(permissionMixin); // 全局注册权限混入;
// 配置Message默认选项，添加关闭按钮，设置2秒自动消失
const originalMessage = Message;
const messageWithClose = (options) => {
  if (typeof options === "string") {
    options = {
      message: options,
    };
  }
  return originalMessage({
    showClose: true,
    duration: 2000,
    ...options,
  });
};

// 保留原有的方法
["success", "warning", "info", "error"].forEach((type) => {
  messageWithClose[type] = (options) => {
    if (typeof options === "string") {
      options = {
        message: options,
      };
    }
    return originalMessage[type]({
      showClose: true,
      duration: 2000,
      ...options,
    });
  };
});

// 替换Vue原型上的$message
Vue.prototype.$message = messageWithClose;

// 应用启动时清理历史遗留数据
auth.cleanupLegacyData();

// 开发环境下加载权限调试工具
// if (process.env.NODE_ENV === "development") {
//   import("@/utils/permissionDebug").then(({ showPermissionHelp }) => {
//     console.log("🔐 权限系统已加载，输入 showPermissionHelp() 查看使用帮助");
//   });
// }

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
