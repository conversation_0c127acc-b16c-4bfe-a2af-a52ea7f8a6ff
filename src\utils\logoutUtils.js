/**
 * 登出工具类 - 统一处理登出和数据清除逻辑
 */

import auth from "./auth";
import routeService from "@/services/routeService";
import store from "@/store";

/**
 * 执行完整的登出流程
 * @param {boolean} callLogoutApi - 是否调用登出接口
 * @param {boolean} showMessage - 是否显示提示消息
 * @returns {Promise<void>}
 */
export const performLogout = async (
  callLogoutApi = true,
  showMessage = false
) => {
  try {
    // 如果需要调用登出接口
    if (callLogoutApi) {
      await store.dispatch("user/logout");
    } else {
      // 直接清除本地数据
      clearAllUserData();
    }

    if (showMessage && window.Vue && window.Vue.prototype.$message) {
      window.Vue.prototype.$message.success("已安全退出");
    }
  } catch (error) {
    console.error("登出过程中发生错误:", error);
    // 即使接口调用失败，也要清除本地数据
    clearAllUserData();
  }
};

/**
 * 清除所有用户相关数据
 */
export const clearAllUserData = () => {
  try {
    console.log("开始清除所有用户数据...");

    // 清除Vuex状态
    store.commit("user/CLEAR_AUTH");

    // 清除认证相关数据
    auth.clearAuth();

    // 清除路由和权限数据
    routeService.clearAll();

    // 清除可能的全局状态
    if (window.isShowingAuthDialog) {
      window.isShowingAuthDialog = false;
    }

    console.log("所有用户数据已清除");
  } catch (error) {
    console.error("清除用户数据时发生错误:", error);
  }
};

/**
 * Token过期处理
 * @param {string} redirectPath - 重定向路径
 */
export const handleTokenExpired = (redirectPath = "/") => {
  // 清除所有数据
  clearAllUserData();

  // 跳转到登录页
  if (window.Vue && window.Vue.prototype.$router) {
    window.Vue.prototype.$router.push({
      path: "/login",
      query: { redirect: redirectPath },
    });
  } else {
    // 兜底方案
    window.location.href = `/login?redirect=${encodeURIComponent(
      redirectPath
    )}`;
  }
};

/**
 * 菜单加载失败处理
 * @param {string} redirectPath - 重定向路径
 * @param {string} errorMessage - 错误消息
 */
export const handleMenuLoadFailed = (
  redirectPath = "/",
  errorMessage = "菜单加载失败"
) => {
  // 清除所有用户数据
  clearAllUserData();

  // 显示错误提示
  if (window.Vue && window.Vue.prototype.$message) {
    window.Vue.prototype.$message.error(errorMessage);
  }

  // 跳转到登录页
  if (window.Vue && window.Vue.prototype.$router) {
    window.Vue.prototype.$router.push({
      path: "/login",
      query: { redirect: redirectPath, error: "menu_load_failed" },
    });
  } else {
    // 兜底方案
    window.location.href = `/login?redirect=${encodeURIComponent(
      redirectPath
    )}&error=menu_load_failed`;
  }
};

export default {
  performLogout,
  clearAllUserData,
  handleTokenExpired,
  handleMenuLoadFailed,
};
