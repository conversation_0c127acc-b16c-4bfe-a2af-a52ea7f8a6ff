<template>
  <el-submenu v-if="item.children && item.children.length" :index="basePath">
    <template #title>
      <i :class="item.icon ? item.icon : 'el-icon-s-operation'"></i>
      <span>{{ item.name }}</span>
    </template>
    <menu-item
      v-for="child in item.children"
      :key="child.path"
      :item="child"
      :base-path="resolvePath(child.path)"
    />
  </el-submenu>
  
  <el-menu-item v-else :index="resolvePath(item.path)">
    <i :class="item.icon ? item.icon : 'el-icon-s-operation'"></i>
    <span>{{ item.name }}</span>
  </el-menu-item>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  methods: {
    resolvePath(routePath) {
      // 处理绝对路径
      if (routePath.startsWith('/')) {
        return routePath
      }
      // 处理相对路径并去除多余的斜杠
      return `${this.basePath}/${routePath}`.replace(/\/+/g, '/')
    }
  }
}
</script>