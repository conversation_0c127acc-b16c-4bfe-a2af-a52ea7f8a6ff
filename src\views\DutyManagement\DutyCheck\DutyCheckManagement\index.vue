<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="检查时间" prop="inspectionTime">
          <el-date-picker
            :disabled="this.styleType === 3"
            v-model="form.inspectionTime"
            format="yyyy-MM-dd HH:mm"
            placeholder="选择时间"
            style="width: 184px;"
            type="datetime"
            @change="handleChangeMonth">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="检查方式" prop="inspectionType">
          <el-select :disabled="this.styleType === 3" v-model="form.inspectionType" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in inspectionTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="被检查单位" prop="inspectionUnit">
          <el-input :disabled="this.styleType === 3" style="width: 400px;" v-model="form.inspectionUnit" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="被检查岗位" prop="inspectionJob">
          <el-input :disabled="this.styleType === 3" style="width: 400px;" v-model="form.inspectionJob" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="被检查人员" prop="inspectionUser">
          <el-input :disabled="this.styleType === 3" style="width: 400px;" v-model="form.inspectionUser" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="检查结果" prop="inspectionResult">
          <el-select :disabled="this.styleType === 3" v-model="form.inspectionResult" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in inspectionResultList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="附件信息" prop="fileList">
          <el-upload
            :disabled="this.styleType === 3"
            ref="uploadRef"
            class="upload-demo"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :http-request="uploadFile"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button v-if="styleType != 3" size="small" type="primary">点击上传</el-button>
            <div v-if="styleType != 3" slot="tip" class="el-upload__tip">
              只能上传1个文件，且不超过10M
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </general-dialog>

    <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, dutyManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond, getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "DutyCheckManagement",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "inspectionTime",
          label: "检查时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          label: "检查方式",
          prop: "inspectionType",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "inspectionResult",
          label: "检查结果",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "inspectionUnit",
          label: "被检查单位",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
      ],
      columns: [
        { prop: "inspectionTime", label: "检查时间", text: true },
        { prop: "inspectionType", label: "检查方式", text: true },
        { prop: "inspectionUnit", label: "被检查单位", text: true },
        { prop: "inspectionJob", label: "被检查岗位", text: true },
        { prop: "inspectionUser", label: "被检查人员", text: true },
        { prop: "inspectionResult", label: "检查结果", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '260px',
          operationList: [
            {
              label: '查看',
              permission: 'dutyCheck:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'dutyCheck:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '通报',
              permission: 'dutyCheck:report',
              buttonClick: this.handleApproval,
              isShow: (row, $index) => {
                if(this.tableData[$index].inspectionResult === 0){
                    return false
                }else{
                    return true
                }
              }
            }
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      inspectionTypeList: [],
      inspectionResultList: [],
      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增值班检查",

      form: {
        inspectionTime: "",
        inspectionType: "",
        inspectionUnit: "",
        inspectionJob: "",
        inspectionUser: "",
        inspectionResult: "",
        fileList:[]
      },
      rules: {
        inspectionTime: [
          {required: true, message: '检查时间不能为空', trigger: 'blur'}
        ],
        inspectionType: [
          {required: true, message: '检查方式不能为空', trigger: 'blur'}
        ],
        inspectionUnit: [
          {required: true, message: '被检查单位不能为空', trigger: 'blur'}
        ],
        inspectionJob: [
          {required: true, message: '被检查岗位不能为空', trigger: 'blur'}
        ],
        inspectionUser: [
          {required: true, message: '被检查人员不能为空', trigger: 'blur'}
        ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleChangeMonth(value) {
      this.form.inspectionTime = conversionDateNotSecond(value);
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.inspectionTypeList = await getItemList(inspectionDictionaryType);
        this.searchItems[1].options = this.inspectionTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.inspectionResultList = await getItemList(inspectionResultType);
        this.searchItems[2].options = this.inspectionResultList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      dutyManagementApi.queryInspectionInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        this.form = {
          ...data,
        };
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.form = {};
      this.fileList = [];
      this.form.fileList = [];
      this.generalDialogTitle = "新增值班检查";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑值班检查";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看值班检查";
    },

    // 通报
    handleApproval() {
      setTimeout(() => {
        this.$message.success("通报成功！");
      }, 500);
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryInspectionList(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await dutyManagementApi.createInspection(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await dutyManagementApi.updateInspection(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(file.url);
      }
      // window.open(fileUrl, '_blank');
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
