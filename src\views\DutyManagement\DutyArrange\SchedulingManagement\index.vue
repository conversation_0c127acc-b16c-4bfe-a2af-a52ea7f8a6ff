<template>
  <div style="padding: 0 20px 20px 20px" class="user-index-container">
    <div class="dutyTitleStyle">
      <span class="title-text">{{ tableTitle }}</span>
      <el-date-picker
        class="date-picker"
        v-model="templateDate"
        type="month"
        placeholder="选择月"
        @change="handleChangeMonth"
      >
      </el-date-picker>
    </div>
    <MultiLevelTable
      ref="multiLevelTableRef"
      :tableType="tableStyleType"
      :columns="columns"
      :tableData="tableData"
      :otherTableData="otherTableData"
      @handle-click-cell="handleClickCell"
    />

    <UploadFileDialog
      ref="uploadFileDialogRef"
      :uploadTitle="uploadTitle"
      :visible="uploadVisible"
      @handle-import-file="handleImportFile"
      @success="handleImportSuccess"
      @cancel="handleImportCancel"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :showFooter="false"
      @cancel="handleCancel"
    >
      <addEidt ref="addEditClient" @ok="loadData" />
    </general-dialog>
  </div>
</template>

<script>
import { dutyManagementApi } from "@/api/index";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { getCurrentDate } from '@/utils/publicMethod'
import addEidt from './components/addEidt.vue'
import MultiLevelTable from "@/components/MultiHeader.vue";
import UploadFileDialog from "@/components/UploadFileDialog.vue";
import { auth } from "@/utils"; 

export default {
  name: 'UserIndex',
  components: {GeneralDialog, MultiLevelTable, UploadFileDialog,  addEidt},
  props: {
    // table组件使用场景
    tableScene: {
      type: Number,
      default: 1, // 1：混合场景，2：普通表格场景，3：多级表头多列单元格场景
    },
    
  },
  data() {
    return {
      deptData:{},
      tableStyleType: 1,
      tableTitle: '', //'北京市应急管理局'+dutyYear+'年'+dutyMonth+'月联合值守工作安排表'
      templateDate: '',
      dutyYear: '',
      dutyMonth: '',
      currentTemplate: "",
      columns: [],
      searchItems: [],
      originalData: [],
      otherTableData: [],

      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增值班岗位",

      uploadVisible: false,
      uploadTitle: "导入值班数据",
    };
  },
  computed: {
    tableData() {
      return this.originalData.map((item) => {
        // 创建基础对象，包含日期信息
        const formattedItem = {
          // dateTime: [ {userName:item.dat},{userName:item.week}]
          dateTime: [
            {
              userName: `${item.dat} ${item.week}`,
              dat: item.dat,
              week: item.week,
            },
          ],
        };

        // 如果有安排数据，则按positionId分组
        if (item.arrangements && item.arrangements.length > 0) {
          // 使用reduce来按positionId分组
          const grouped = item.arrangements.reduce((acc, curr) => {
            const positionId = curr.positionId;
            if (!acc[positionId]) {
              acc[positionId] = [];
            }
            acc[positionId].push(curr);
            return acc;
          }, {});

          // 将分组后的数据合并到formattedItem中
          Object.assign(formattedItem, grouped);
        }

        return formattedItem;
      });
    },
  },
  created() {},
  mounted() {
    // 获取当前登录用户的部门信息
    this.deptData = auth.getUserInfo();
    if (this.tableScene === 1) {
      // 混合场景下，如果是应急局部门，则使用多级表头多列单元格
      if (this.deptData.orgName === '市应急管理局') {
        this.tableStyleType = 1;
      } else {
        // 其他部门使用普通表格
        this.tableStyleType = 2;
      }
    } else if (this.tableScene === 2) {
      // 普通表格场景下
      this.tableStyleType = 2;
    } else {
      // 多级表头多列单元格表格场景下
      this.tableStyleType = 1;
    }
    this.getCurrentYearMonth(getCurrentDate("YYYY-M"));
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.handleDownloadTemplate,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "import_top",
        handler: this.handleImportDutyTable,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.handleExportDytyFile,
      });
    },
    async handleDownloadTemplate() {
      // 下载模版
      const res = await dutyManagementApi.downloadTemplate(
        this.dutyYear,
        this.dutyMonth
      );
      console.log(res.data);
      var blob = new Blob([res.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
      var downloadElement = document.createElement("a");
      var href = window.URL.createObjectURL(blob); // 创建下载的链接
      downloadElement.href = href;
      downloadElement.download =
        this.dutyYear + "年" + this.dutyMonth + "月排班表模版.xlsx";
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href); // 释放掉blob对象
    },
    handleImportDutyTable() {
      // 导入
      this.uploadVisible = true;
    },
    async handleImportFile(formData) {
      // 开始导入
      try {
        const response = await dutyManagementApi.importDutyInfo(formData);
        if (response && response.code === 0) {
          this.handleImportSuccess();
        } else {
          this.$nextTick(function () {
            this.$refs.uploadFileDialogRef.handleResponseResult(response);
          })
        }
      } catch (error) {
        this.$nextTick(function () {
          this.$refs.uploadFileDialogRef.handleResponseResult(error);
        })
      }
    },
    handleImportSuccess() {
      // 导入成功
      this.uploadVisible = false;
      this.getDutyTableColumnsAndData();
    },
    handleImportCancel() {
      // 导入失败
      this.uploadVisible = false;
    },
    handleExportDytyFile() {
      const param = {year:this.dutyYear, month:this.dutyMonth};
      this.startExportDutyFile(param);
    },
    handleExportFile() {
      const param = {year:this.dutyYear, month:this.dutyMonth, dept:this.deptData.orgId};
      this.startExportDutyFile(param);
    },
    // 如果不传orgId则是排班管理页面导出操作，如果传则是值班安排统计页面导出
    async startExportDutyFile(param) {
      // 导出表格
      const res = await dutyManagementApi.exportDutyInfo(param);
      var blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
      var downloadElement = document.createElement('a');
      var href = window.URL.createObjectURL(blob); // 创建下载的链接
      downloadElement.href = href;
      downloadElement.download = this.tableTitle + '.xlsx'; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href); // 释放掉blob对象
    },
    
    getCurrentYearMonth(value) {
      this.templateDate = value;
      const result = this.templateDate.split("-");
      this.dutyYear = result[0];
      this.dutyMonth = result[1];
      const orgName = this.deptData.orgName || '';
      this.tableTitle = this.dutyYear+'年'+this.dutyMonth+'月'+orgName+'应急值守安排表';
      this,this.getDutyTableColumnsAndData();
    },

    changeCurrentDeptData(deptData) {
      this.deptData = deptData;
      this.getCurrentYearMonth(getCurrentDate("YYYY-M"));
    },

    // 获取表头和表格数据
    getDutyTableColumnsAndData() {
      if (this.tableScene === 1) {
        if (this.deptData.orgName === '市应急管理局') {
          // 获取应急管理局的值班表
          this.getDutyTableColumns();
        } else {
          // 获取其他部门的值班表
          this.columns = [
            {text: true, prop: 'datAndWeek', label: '日期'},
            {text: true, prop: 'leader', label: '带班领导'},
            {text: true, prop: 'phoneNum', label: '手机号码'},
            {text: true, prop: 'operator', label: '值班人员'}
          ];
          this.getOtherDeptTableData();
        }
      } else {
        // 获取值班安排统计下不带职务的值班表
        this.columns = [
          { text: true, prop: "datAndWeek", label: "日期" },
          { text: true, prop: "leader", label: "带班领导" },
          { text: true, prop: "phoneNum", label: "手机号码" },
          { text: true, prop: "operator", label: "值班人员" },
        ];
        this.getNoPositionDeptTableData();
      }
    },

    handleChangeMonth(value) {
      this.getCurrentYearMonth(getCurrentDate("YYYY-M", value));
    },
    handleClickCell(params, row, column, type) {
      console.log("xc-----", params);
      // const departmentPath = this.findDepartmentPath(this.columns, params.colData.property);
      // params.path = departmentPath;
      // if (params.type === 4) {
      //   // 删除值班人员

      //   return;
      // }
      switch (params.type) {
        case 0:
          this.generalDialogTitle = "新增值班人员";
          break;
        case 1:
          this.generalDialogTitle = "替班";
          break;
        case 2:
          this.generalDialogTitle = "换班";
          break;
        case 3:
          this.generalDialogTitle = "修改值班人员";
          break;
        case 4:
          this.generalDialogTitle = "删除值班人员";
          break;

        default:
          this.generalDialogTitle = "";
          break;
      }
      this.dialogVisible = true;
      this.$nextTick(function () {
        this.$refs.addEditClient.addFormFn(params);
      });
    },
    // 获取排班表头
    async getDutyTableColumns() {
      const param = { year: this.dutyYear, month: this.dutyMonth };
      const res = await dutyManagementApi.queryPositionByMonth(param);
      const { code, data, error } = res;

      if (code === 0) {
        this.columns = data;
        this.columns.unshift({ id: "dateTime", positionName: "日期", num: 1 });
        // this.postTemplateList = data;
        // this.currentTemplate = this.postTemplateList[0].id;
        this.getDutyTableData();
      } else {
        this.$message.error(error);
      }
    },
    // 获取排班表格数据
    async getDutyTableData() {
      const param = { year: this.dutyYear, month: this.dutyMonth };
      const res = await dutyManagementApi.queryArrangementByMonth(param);
      const { code, data, error } = res;

      if (code === 0) {
        this.originalData = data;
      } else {
        this.$message.error(error);
      }
    },
    // 获取其他处室的排班表格数据
    async getOtherDeptTableData() {
      const param = { year: this.dutyYear, month: this.dutyMonth };
      const res = await dutyManagementApi.queryDeptArrangement(param);
      const { code, data, error } = res;
      if (code === 0) {
        this.otherTableData = data;
      } else {
        this.$message.error(error);
      }
    },
    // 获取值班安排统计下不带职务的值班表
    async getNoPositionDeptTableData() {
      if (this.deptData.orgId) {
        const param = {year:this.dutyYear, month:this.dutyMonth, dept:this.deptData.orgId};
        const res = await dutyManagementApi.queryDeptArrangementNoPosition(param);
        const {code, data, error} = res;
        if (code === 0) {
          this.otherTableData = data;
        } else {
          this.$message.error(error)
        }
      }
    },

    loadData(e) {
      this.dialogVisible = false;
      this.templateDialog = false;
      if (e === 1) {
        return;
      }
      this.getDutyTableData();
      this.$nextTick(function () {
        this.$refs.multiLevelTableRef.resetHandleData();
      });
      // window.location.reload(); // 刷新当前页面
    },

    handleCancel() {      
      this.dialogVisible = false;
      this.templateDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.dutyTitleStyle {
  position: relative;
  height: 70px;
}

.title-text {
  display: inline-block;
  width: 100%;
  line-height: 70px;
  font-size: 25px;
  text-align: center;
}

.date-picker {
  position: absolute;
  width: 150px;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
