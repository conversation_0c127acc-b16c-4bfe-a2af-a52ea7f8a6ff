import { textMessageManagementApi,textMessageDraftApi } from "@/api";
// 全局混入
export default {
    data() {
        return {
            // 短信模版列表
            contactList: [],
            // 分页信息
            pagination: {
                current: 1,
                pageSize: 10,
                total: 0,
            },
            // 搜索条件
            searchParams: {},
            // 加载状态
            loading: false,
        }
    },
    methods: {
        SET_CONTACT_LIST(list) {
            this.contactList = list || [];
        },
        SET_PAGINATION(pagination) {
            this.pagination = { ...this.pagination, ...pagination };
        },
        SET_SEARCH_PARAMS(params) {
            this.searchParams = params;
        },
        SET_LOADING(loading) {
            this.loading = loading;
        },
        // 获取
        async getContactList(params = {}) {
            this.SET_LOADING(true)
            try {
                const requestParams = {
                    pageNum: this.pagination.current,
                    pageSize: this.pagination.pageSize,
                    ...this.searchParams,
                    ...params,
                };

                const response = await textMessageDraftApi.getTextMessageeDraftList(requestParams);
                this.SET_CONTACT_LIST(response.data.items)
                this.SET_PAGINATION({
                    total: response.data.total,
                    current: response.data.page,
                })
                this.SET_LOADING(false)
                return (
                    response?.data || {
                        list: this.contactList,
                        total: this.pagination.total,
                    }
                );
            } catch (error) {
                this.SET_LOADING(false)
                throw error;
            }
        },
        // 搜索
        async seachContactList(params = {}) {
            this.SET_SEARCH_PARAMS(params)
        },

        //删除
        async removeTextMessage(params = {}) {
            console.log(params)
            try {
                const requestParams = {
                    taskId: params.taskId
                };
                await textMessageManagementApi.removeTextMessageManagement(requestParams);

            } catch (error) {

                throw error;
            }
        },

        // 分页
        async setCurrentChange(params = {}) {
            try {
                this.SET_PAGINATION(params)
            } catch (error) {
                throw error;
            }
        },
    },
}
