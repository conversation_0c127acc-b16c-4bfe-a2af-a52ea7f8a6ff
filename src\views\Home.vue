<template>
  <div class="home-page">
    <!-- 首页 - Page -->
    <div class="welcome-section">
      <div class="welcome-card">
        <h1 class="welcome-title">欢迎使用应急值守业务系统</h1>
        <!-- <p class="welcome-subtitle">应急值守业务系统</p> -->
        <div class="system-info">
          <div class="info-item">
            <i class="el-icon-time"></i>
            <span>当前时间：{{ currentTime }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-user"></i>
            <span>当前用户：{{ userInfo?.name || "admin" }}</span>
            <span v-if="userInfo?.positions">（{{ userInfo.positions }}）</span>
          </div>
          <div class="info-item" v-if="userInfo?.userClass">
            <i class="el-icon-office-building"></i>
            <span>用户级别：{{ userInfo.userClass }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="quick-access">
      <h2 class="section-title">快速访问</h2>
      <div class="access-grid">
        <div class="access-card" @click="goToContact">
          <i class="el-icon-phone-outline"></i>
          <h3>通讯录管理</h3>
          <p>管理应急联系人信息</p>
        </div>
        <div class="access-card" @click="showComingSoon('/comprehensiveAssessment/detailedRulesManagement')">
          <i class="el-icon-view"></i>
          <h3>综合评价</h3>
          <p>应急值守自评和系统评价</p>
        </div>
        <div class="access-card" @click="showComingSoon('/informationSubmit/informationSum/receiveInformation')">
          <i class="el-icon-document"></i>
          <h3>信息报送</h3>
          <p>上报和处理信息</p>
        </div>
        <div class="access-card" @click="showComingSoon('/dutyManagement/dutyArrange/schedulingManagement')">
          <i class="el-icon-date"></i>
          <h3>值班管理</h3>
          <p>值班安排和管理</p>
        </div>

        <div class="access-card" @click="showComingSoon('/warningPublish/access')">
          <i class="el-icon-warning-outline"></i>
          <h3>预警信息发布</h3>
          <p>发布预警相关信息</p>
        </div>
        <div class="access-card" @click="showComingSoon('/textMessage/template')">
          <i class="el-icon-receiving"></i>
          <h3>短信平台</h3>
          <p>各部门联络通信</p>
        </div>
        <div class="access-card" @click="showComingSoon('/meetingManagement/meetingReadyManagement/meetingNotice')">
          <i class="el-icon-monitor"></i>
          <h3>会议管理</h3>
          <p>发布和报名会议</p>
        </div>
        <div class="access-card" @click="showComingSoon('/emergencyResourceManage/emergencyResourceStatistics')">
          <i class="el-icon-help"></i>
          <h3>应急资源</h3>
          <p>应急资源和知识库管理</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "Home",
  data() {
    return {
      currentTime: "",
      timer: null,
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
  },
  mounted() {
    this.updateTime();
    this.timer = setInterval(this.updateTime, 1000);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    updateTime() {
      const now = new Date();
      this.currentTime = now.toLocaleString("zh-CN");
    },
    goToContact() {
      this.$router.push("/system/contact");
    },
    showComingSoon(module) {
      this.$router.push(module);
      // debugger
      // this.$message.info(`${module}功能开发`);
    },
  },
};
</script>

<style scoped>
.home-page {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-card {
  background: transparent;
  padding: 40px;
  text-align: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 10px 0;
}

.welcome-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 30px 0;
}

.system-info {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.info-item i {
  color: var(--icon-primary);
  font-size: 16px;
}

.quick-access {
  background: transparent;
  padding: 30px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
  text-align: center;
}

.access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  gap: 50px;
}

.access-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.access-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--icon-primary);
}

.access-card i {
  font-size: 36px;
  color: var(--icon-primary);
  margin-bottom: 15px;
}

.access-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 10px 0;
}

.access-card p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .home-page {
    padding: 15px;
  }

  .welcome-card {
    padding: 30px 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .system-info {
    flex-direction: column;
    gap: 15px;
  }

  .access-grid {
    grid-template-columns: 1fr;
  }
}
</style>
