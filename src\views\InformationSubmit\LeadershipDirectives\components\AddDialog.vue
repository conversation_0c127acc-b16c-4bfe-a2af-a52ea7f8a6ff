<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    dialog-width="800px"
    general-dialog-title="新增领导批示"
    @cancel="handleClose"
    @confirm="handleSubmit"
  >
    <!-- 新增领导批示对话框 - Component -->
    <el-form
      ref="addForm"
      :model="formData"
      :rules="formRules"
      class="add-form"
      label-position="top"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="事件标题" prop="reportInfoId">
        <el-select
          v-model="formData.reportInfoId"
          placeholder="请选择事件标题"
          style="width: 100%"
          filterable
          clearable
          @change="handleTitleChange"
        >
          <el-option
            v-for="item in eventTitleList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          >
            <span style="float: left">{{ item.title }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.createTime }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="事件详情" prop="eventInfo">
        <el-input
          v-model="formData.eventInfo"
          type="textarea"
          :rows="4"
          placeholder="请输入事件详情"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="批示信息" prop="approvalInfo">
        <el-input
          v-model="formData.approvalInfo"
          type="textarea"
          :rows="4"
          placeholder="请输入批示信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="批示时间" prop="approvalTime">
        <el-date-picker
          v-model="formData.approvalTime"
          type="datetime"
          placeholder="请选择批示时间"
          style="width: 100%"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="批示领导" prop="leaderUserId">
        <!-- <leader-selector
          v-model="formData.leaderUserId"
          placeholder="请选择批示领导"
          @select="handleLeaderSelect"
        /> -->
        <el-cascader
          v-model="formData.leaderUserId"
          :props="cascaderProps"
          :options="options"
          :show-all-levels="false"
          clearable
          placeholder="请选择领导"
          @change="handleChange"
        ></el-cascader>
      </el-form-item>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import LeaderSelector from "./LeaderSelector.vue";
import { leadershipDirectivesApi, receiveInformationApi, systemManagementApi } from "@/api";

export default {
  name: "LeadershipDirectivesAddDialog",
  components: {
    GeneralDialog,
    LeaderSelector,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      submitting: false,
      eventTitleList: [],
      formData: {
        reportInfoId: "",
        eventInfo: "",
        approvalInfo: "",
        approvalTime: "",
        leaderUserId: "",
        leaderOrgId: "",
      },
      formRules: {
        reportInfoId: [
          { required: true, message: "请选择事件标题", trigger: "change" },
        ],
        approvalInfo: [
          { required: true, message: "请输入批示信息", trigger: "blur" },
        ],
        approvalTime: [
          { required: true, message: "请选择批示时间", trigger: "change" },
        ],
        leaderUserId: [
          { required: true, message: "请选择批示领导", trigger: "change" },
        ],
      },
      
      nodeMap: new Map(), // 用于存储id到对象的映射
      options: [],      // 初始加载的顶级部门
      cascaderProps: {
        value: 'id',
        label: 'orgName',
        children: 'children',
        checkStrictly: true, // 允许选择任意级
        lazy: true,          // 启用懒加载
        emitPath: false, 
        lazyLoad: this.loadChildren,
        // 自定义节点显示内容
        renderLabel: ({ data }) => {
          return data.type === 1 ? `${data.userName} (${data.phone})` : data.orgName;
        }
      },
    };
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        this.initDialog();
      } else {
        this.resetForm();
      }
    },
  },
  methods: {
    // 懒加载子节点
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map(item => ({
            ...item,
            disabled: item.type === 0 && item.children && item.children.length === 0 // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({orgId: data?.id || '0'});
        const nodes = response.data.map(item => {
           this.nodeMap.set(item.id, item); // 存储映射关系
          // 用户节点
          if (item.type === 1) {
            return {
              ...item,
              orgName: item.userName || '未命名用户',
              isLeaf: true
            };
          }
          // 部门节点
          return {
            ...item,
            disabled: item.type === 0 && (!item.children || item.children.length === 0) // 空部门禁用
          };
          
        });
        resolve(nodes);
      } catch (error) {
        console.error('加载子节点失败:', error);
        resolve([]);
      }
    },

    // 处理选择变化
    handleChange(value) {
      if (value && value.length > 0) {
        // const lastId = value[value.length - 1];
        this.formData.leaderUserId = value;
      }
    },

    handleTitleChange(value) {
      this.formData.eventInfo = "";
      receiveInformationApi.queryReportInfo({ id: value }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.formData.eventInfo = data?.eventInfo;
      });
    },

    async initDialog() {
      this.loading = true;
      try {
        await this.fetchEventTitleList();
      } catch (error) {
        console.error("初始化对话框失败:", error);
        this.$message.error("初始化失败，请重试");
      } finally {
        this.loading = false;
      }
    },

    async fetchEventTitleList() {
      try {
        const response = await leadershipDirectivesApi.queryReportTitleList({});
        if (response.code === 0 && response.data) {
          this.eventTitleList = response.data.map((item) => ({
            value: item.title || item.infoTitle,
            title: item.title || item.infoTitle,
            createTime: item.createTime,
            id: item.id,
          }));
        }
      } catch (error) {
        console.error("获取事件标题列表失败:", error);
        this.eventTitleList = [];
      }
    },

    handleLeaderSelect(selectedData) {
      if (selectedData) {
        this.formData.leaderUserId = selectedData.userId;
        this.formData.leaderOrgId = selectedData.parentId || "";

        // 触发表单验证
        this.$refs.addForm.validateField("leaderUserId");
      } else {
        // 清空选择
        this.formData.leaderUserId = "";
        this.formData.leaderOrgId = "";
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.addForm.validate();

        const response = await leadershipDirectivesApi.createLeadership(
          this.formData
        );

        if (response.code === 0) {
          this.$message.success("新增成功");
          this.$emit("success");
          this.handleClose();
        } else {
          throw new Error(response.message || "新增失败");
        }
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error(error.message || "提交失败，请重试");
      }
    },

    handleClose() {
      this.$emit("close");
    },

    resetForm() {
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields();
      }
      this.formData = {
        reportInfoId: "",
        eventInfo: "",
        approvalInfo: "",
        approvalTime: "",
        leaderUserId: "",
        leaderOrgId: "",
      };
    },
  },
};
</script>

<style scoped>
/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

/* 下拉选项样式 */
:deep(.el-select-dropdown__item) {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}
</style>
