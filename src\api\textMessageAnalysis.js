/**
 *  短信管理相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";
// 短信分析
export default {
    // 编发短信条数
    querySendSmsNumber(params = {}) {
        return request({
            url: '/ds/smsStatistics/querySendSmsNumber',
            method: 'post',
            data: params
        })

    },
    // 发送人次
    querySendPeopleNumber(params = {}) {
        return request({
            url: '/ds/smsStatistics/querySendPeopleNumber',
            method: 'post',
            data: params
        })

    },
    // 短信类型
    queryTemplateTypeNumber(params = {}) {
        return request({
            url: '/ds/smsStatistics/queryTemplateTypeNumber',
            method: 'post',
            data: params
        })

    },
    // 发送总条数
    querySendItemsNumber(params = {}) {
        return request({
            url: '/ds/smsStatistics/querySendItemsNumber',
            method: 'post',
            data: params
        })

    },
};
