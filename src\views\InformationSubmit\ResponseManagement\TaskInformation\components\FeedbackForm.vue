<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    general-dialog-title="反馈"
    dialog-width="700px"
    @cancel="handleCancel"
    @confirm="handleSubmit"
  >
    <div class="form-contaner">
      <el-form ref="form" :rules="rules" :model="form" label-width="80px">
        <div class="form-title">基本信息</div>
        <el-form-item label="事件标题">
          <el-input v-model="form.infoTitle" disabled></el-input>
        </el-form-item>
        <el-form-item label="事发时间">
          <el-input v-model="form.infoTime" disabled></el-input>
          <!-- <el-date-picker
            type="datetime"
            placeholder="请选择事发时间"
            v-model="form.infoTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          ></el-date-picker> -->
        </el-form-item>
        <el-form-item label="事发地点">
          <el-input v-model="form.infoLocationDetail" disabled></el-input>
        </el-form-item>
        <el-form-item label="事件类型" prop="infoChildTypeName">
          <el-input v-model="form.infoChildTypeName" disabled></el-input>
          <!-- <el-cascader
            v-model="form.eventType"
            :options="emergencyTypeList"
            :props="cascaderProps"
            style="width: 100%"
          ></el-cascader> -->
        </el-form-item>
        <el-form-item label="响应指令" prop="responseDirective">
          <el-input v-model="form.responseDirective" disabled></el-input>
          <!-- <el-select
            v-model="form.responseDirective"
            placeholder="请选择上报方式"
            style="width: 100%"
          >
            <el-option
              v-for="item in responseDirectiveList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="完成时间">
          <el-input v-model="form.completionTime" disabled></el-input>
        </el-form-item>
        <div class="form-title">详情信息</div>
        <el-form-item label="事件详情">
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入事件详情"
            v-model="form.eventInfo"
            disabled
          ></el-input>
        </el-form-item>
        <div class="form-title">职责任务</div>
        <el-form-item label="任务情况">
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入任务情况"
            v-model="form.responsibilities"
            disabled
          ></el-input>
        </el-form-item>
        <div class="form-title">任务动作</div>
        <el-form-item label="动作情况" prop="taskFeedback">
          <el-input
            :disabled="styleType === 2" 
            type="textarea"
            :rows="6"
            :placeholder="styleType === 1 ? '请输入动作情况' : '暂无反馈内容'"
            v-model="form.taskFeedback"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { receiveInformationApi } from "@/api";
export default {
  name: "FeedbackForm",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    GeneralDialog,
  },
  data() {
    return {
      styleType: 1, // 1反馈，2查看
      form: {
        infoTitle: "",
        infoTime: "",
        infoLocationDetail: "",
        eventType: "",
        responseDirective: "",
        completionTime: "",
      },
      rules: {
        taskFeedback: [
          { required: true, message: "请输入动作情况", trigger: "blur" },
        ],
      },
      emergencyTypeList: [],
      cascaderProps: {
        value: "id",
        label: "name",
        children: "children",
      },
      responseDirectiveList: [
        {
          label: "短信",
          value: "1",
        },
        {
          label: "电话",
          value: "2",
        },
        {
          label: "视频",
          value: "3",
        },
      ],
    };
  },
  methods: {
    handleCancel() {
      this.$emit("close");
    },
    handleSubmit() {
      if (this.styleType === 2) {
        this.handleCancel();
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          receiveInformationApi
            .backResponseTaskInfo({
              id: this.form.id,
              taskFeedback: this.form.taskFeedback,
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "反馈成功",
                  type: "success",
                });
                this.$emit("close");
              }
            });
        }
      });
    },
    queryResponseTaskInfoDetail(id, styleType) {
      this.styleType = styleType;
      this.form.id = id;
      receiveInformationApi
        .queryResponseTaskInfoDetail({
          id,
        })
        .then((res) => {
          if (res.code === 0) {
            this.form = res.data;
          }
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.form-contaner {
  margin: 80px 60px 10px;
  .form-title {
    position: relative;
    padding-left: 20px;
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: bold;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: -2px;
      height: 120%;
      width: 2px;
      background-color: #409eff;
    }
  }
}
</style>