<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    dialog-width="800px"
    general-dialog-title="提交发布申请"
    @cancel="handleClose"
    @confirm="handleSubmit"
  >
    <el-form
      class="application-form"
      ref="form"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="申请审批领导" prop="selectedUserId">
        <el-cascader
          v-model="formData.selectedUserId"
          :props="cascaderProps"
          :options="options"
          :show-all-levels="false"
          clearable
          placeholder="选择申请审批领导"
          @change="handleChange"
        ></el-cascader>
      </el-form-item>
    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { warningPublishApi, systemManagementApi } from "@/api";
export default {
  name: "applicationDialog",
  components: {
    GeneralDialog,
  },
  data() {
    return {
      dialogVisible: false,
      formData: {
        selectedUserId: "",
      },
      options: [],
      cascaderProps: {
        value: "id",
        label: "orgName",
        children: "children",
        checkStrictly: true,
        lazy: true, // 启用懒加载
        emitPath: false,
        lazyLoad: this.loadChildren,
        // 自定义节点显示内容
        renderLabel: ({ data }) => {
          return data.type === 1
            ? `${data.userName} (${data.phone})`
            : data.orgName;
        },
      },
      rules: {
        selectedUserId: [
          { required: true, message: "请选择申请审批领导", trigger: "blur" },
        ],
      },
      nodeMap: new Map(),
      selectedNode: null,
      warningId: "",
    };
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          try {
            const requestParams = {
              leaderDeptId: this.selectedNode.parentId,
              warningId: this.warningId,
              leaderId: this.selectedNode.id,
              leaderDeptName: this.selectedNode.orgName,
              leaderName: this.selectedNode.userName,
            };
            warningPublishApi.submitApplication(requestParams).then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: "发布成功",
                  type: "success",
                });
                this.dialogVisible = false;
                this.$emit("initData");
              }
            });
          } catch (error) {
            throw error;
          }
        }
      });
    },

    // 懒加载子节点
    async loadChildren(node, resolve) {
      const { level, data } = node;
      try {
        // 如果已经是用户节点，直接返回空
        if (data?.type === 1) {
          resolve([]);
          return;
        }

        // 如果有本地children数据直接使用
        if (data?.children && data?.children.length > 0) {
          const nodes = data.children.map((item) => ({
            ...item,
            disabled:
              item.type === 0 && item.children && item.children.length === 0, // 空部门禁用
          }));
          resolve(nodes);
          return;
        }

        // 否则从API获取

        const response = await systemManagementApi.queryOrgAddressBook({
          orgId: data?.id || 0,
        });
        const nodes = response.data
          .filter((item) => {
            if (item.type === 1 && !item.orgName) {
              return false;
            } else {
              return true;
            }
          })
          .map((item) => {
            this.nodeMap.set(item.id, item); // 存储节点映射
            // 用户节点
            if (item.type === 1) {
              return {
                ...item,
                orgName: item.userName || "未命名用户",
                leaf: true,
              };
            }
            // 部门节点
            return {
              ...item,
              disabled:
                item.type === 0 &&
                (!item.children || item.children.length === 0), // 空部门禁用
            };
          });
        resolve(nodes);
      } catch (error) {
        console.error("加载子节点失败:", error);
        resolve([]);
      }
    },
    // 处理选择变化
    handleChange(value) {
      if (value) {
        this.selectedNode = this.nodeMap.get(value);
      } else {
        this.selectedNode = null;
      }
    },
  },
};
</script>

<style>
.application-form {
  padding: 30px 60px 160px 60px;
}
</style>