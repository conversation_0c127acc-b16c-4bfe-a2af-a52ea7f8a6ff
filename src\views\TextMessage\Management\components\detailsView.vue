<template>
    <general-dialog :showFooter="false" :dialog-visible="dialogVisible" :general-dialog-title="'查看'" dialog-width="800px"
        @confirm="handleSubmit" @cancel="handleClose">
        <el-form class="add-form" ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="短信主题">{{ form.theme }}</el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送单位"> {{ form.editor }} </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送人"> {{ form.reviewer }} </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发送时间"> {{ form.planSendTime }} </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="短信内容"> {{ form.content }} </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="关联事件" v-if="eventName"> {{ eventName }} </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table  :height="300" :data="tableList" row-key="detailId" :tree-props="{children: 'detailList'}" default-expand-all>
            <el-table-column  label="分组/成员" prop="name" align="center" width="320" show-overflow-tooltip ></el-table-column>
            <el-table-column label="接收号码" prop="mobile" align="center" show-overflow-tooltip />
            <el-table-column label="发送状态" align="center" show-overflow-tooltip>
                <template #default="scope">
                    <span v-if="scope.row.status == 1" style="color: #E6A23C;">待发送</span>
                    <span v-if="scope.row.status == 2" style="color: #67C23A;">发送成功</span>
                    <span v-if="scope.row.status == 3" style="color: #F56C6C;">发送失败</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center"  width="160">
                <template #default="scope">
                    <!-- -->
                    <el-button  v-if="scope.row.detailId.indexOf('_') <0 && scope.row.status != 2" class="blue-btn btn-small"  size="small" @click="onReSend(scope.row)">重新发送</el-button>
                </template>
            </el-table-column>
        </el-table>
    </general-dialog>

</template>

<script>
import { textMessageManagementApi } from '@/api/index.js'
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
    name: '',
    components: {
        GeneralDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        formData: {
            type: Object,
            default: () => ({}),
        },
    },
    watch: {
        formData: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.form = { ...this.form, ...val };
                    // 获取详情
                    this.queryDetails()

                }
            },
            deep: true,
            immediate: true,
        },
    },
    data() {
        return {
            form: {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                eventeId: "",
                
            },
            eventName:'',
            rules: {
                eventId: [
                    {
                        required: true,
                        message: ' ',
                    },
                ],

            },
            tableList: [
               
            ]
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            },
        },
    },
    methods: {
        resetForm() {
            this.form = {
                theme: '',
                editor: "",
                reviewer: "",
                planSendTime: "",
                content: "",
                eventId: "",
            }
            this.$refs['form'].resetFields();
        },
        // 保存
        handleSubmit() {
           
        },
        selectable(row) {
            
        },
        // 关闭
        handleClose() {
            this.dialogVisible = false
        },
        // 获取详情
        queryDetails(){
            textMessageManagementApi.queryGroupInfoById({taskId:this.formData.taskId}).then((res)=>{
                this.tableList = res.data.detailList
                this.eventName = res.data.eventName
                if(this.tableList.length){
                    this.tableList.map((row,index)=>{
                        row.name = row.groupName
                        row.detailId = new Date().getTime()+'_'+index
                    })
                }
            })
        },
        // 重新发送
        onReSend(row){
            console.log(row)
            textMessageManagementApi.resend({taskDetailId:row.detailId}).then((res)=>{
                this.$message.success('重新发送成功')
                this.queryDetails()
            })
            console.log(row)
        }
    },
    mounted() {
    }
}
</script>

<style scoped></style>