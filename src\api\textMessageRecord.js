/**
 *  短信记录相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";
export default {
    // 获取短信记录列表
    async getTextMessageRecordList(params = {}) {
        return request({
            url: '/ds/smsRecord/queryList',
            method: 'get',
            params: params
        })
    },
    // 删除短信记录
    async removeTextMessageRecord(params = {}) {
        return request({
            url: '/ds/smsRecord/deleteById',
            method: 'post',
            data: params
        })
    },
};
