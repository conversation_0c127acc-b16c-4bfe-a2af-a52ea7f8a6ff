<template>
  <div class="voice-broadcast">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @switch-change="handleSwitch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-top="'40px'"
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="form.templateName"
            placeholder="请输入模板名称"
            size="small"
            clearable
          />
        </el-form-item>

        <el-form-item label="模板类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择模板类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容描述" prop="message">
          <el-input
            v-model="form.message"
            type="textarea"
            :rows="6"
            placeholder="请输入内容描述"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态" prop="isEnable">
          <el-radio-group v-model="form.isEnable">
            <el-radio :label="0" border>停用</el-radio>
            <el-radio :label="1" border>启用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { dutyManagementApi, voiceBroadcastApi } from "@/api";

export default {
  name: "VoiceBroadcast",
  components: { GeneralDialog, PortalTable },
  data() {
    return {
      searchItems: [
        {
          prop: "keyWord",
          label: "模板名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "contentMentionRegionList",
          label: "模板类型",
          type: "select",
          placeholder: "请选择",
          width: "150",
          options: [],
        },
      ],
      tableData: [],
      columns: [
        { prop: "templateName", label: "模板名称", text: true },
        { prop: "typeName", label: "模板类型", text: true },
        {
          prop: "isEnable",
          label: "状态",
          switch: true,
          activeValue: 1,
          inactiveValue: 0,
        },
        { prop: "message", label: "内容描述", text: true },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "编辑",
              permission: "voiceBroadcast:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "voiceBroadcast:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      options: [
        {
          id: "1",
          name: "会议通知",
        },
        {
          id: "2",
          name: "文件通知",
        },
        {
          id: "3",
          name: "其他通知",
        },
        {
          id: "4",
          name: "一键通知",
        },
      ],
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "添加语音模板",
      form: {
        templateName: "",
        type: "1",
        message: "",
        isEnable: 0,
      },
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择模板类型", trigger: "blur" }],
        message: [
          { required: true, message: "请输入内容描述", trigger: "blur" },
        ],
        isEnable: [{ required: true, message: "请选择状态", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.fetchData();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },

    async fetchData(searchParams = {}) {
      let params = {
        page: this.pagination.currentPage, // 修正前：pageSize
        count: this.pagination.pageSize, // 修正前：currentPage
        ...searchParams,
      };

      const { code, data, message, error } =
        await voiceBroadcastApi.queryTemplatePage(params);
      if (code !== 0) return this.$message.error(message || error);
      if (data) {
        data.items.forEach((item) => {
          item.typeName = this.options.find(
            (opt) => opt.id === item.type
          )?.name;
          /*item.status = item.isEnable === 0 ? "停用" : "启用";*/
        });
      }
      this.tableData = data.items;
      this.pagination.total = data.total;
    },

    handleSearch(row) {
      this.pagination.currentPage = 1; // 搜索时重置到第一页
      this.fetchData(row); // 重新加载数据
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    handleAdd() {
      this.dialogVisible = true;
      this.generalDialogTitle = "添加语音模板";
    },

    handleEdit(row) {
      this.dialogVisible = true;
      this.generalDialogTitle = "修改语音模板";
      this.form = { ...row };
    },

    //关注/取消关注
    handleSwitch(row) {
      let msg;
      if (row.isEnable === 1) {
        msg = "您确定要启用当前语音通知模板？";
      } else if (row.isEnable === 0) {
        msg = "您确停用当前语音通知模板？";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          voiceBroadcastApi.updateTemplate({ ...row }).then((res) => {
            const { code, error } = res;
            if (code === 0) {
              this.$message.success("操作成功");
              this.fetchData();
            } else {
              row.isEnable = row.isEnable === 1 ? 0 : 1;
              this.$message.error(error);
            }
          });
        })
        .catch(() => {
          row.isEnable = row.isEnable === 1 ? 0 : 1;
        });
    },

    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { code, message, error } = await voiceBroadcastApi.deleteTemplate(
          {
            id: row.id,
          }
        );
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("删除成功");
        await this.fetchData();
      });
    },

    async handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.form.id) {
            const { code, message, error } =
              await voiceBroadcastApi.updateTemplate({
                ...this.form,
              });
            if (code !== 0) return this.$message.error(message || error);
            this.$message.success("修改成功");
          } else {
            const { code, message, error } =
              await voiceBroadcastApi.createTemplate({
                ...this.form,
              });
            if (code !== 0) return this.$message.error(message || error);
            this.$message.success("添加成功");
          }
          await this.fetchData();
          this.handleCancel();
        }
      });
    },

    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        templateName: "",
        type: "1",
        message: "",
        isEnable: 0,
      };
    },
  },
};
</script>

<style scoped lang="scss"></style>
