<template>
  <div class="command-configuration-form">
    <!-- 指令配置表单 - Component -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      label-position="top"
      class="command-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事故类型" prop="reportId">
            <el-cascader
              ref="accidentRef"
              v-model="formData.reportId"
              :options="emergencyTypeList"
              :props="cascaderProps"
              style="width: 100%"
              :disabled="type !== 'config'"
              @change="handleAccidentTypeChange"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="响应指令" prop="responseDirective">
            <el-select
              v-model="formData.responseDirective"
              placeholder="请选择响应指令"
              clearable
              filterable
              :disabled="type !== 'config'"
            >
              <el-option
                v-for="item in responseCommandList"
                :key="item.id"
                :label="item.commandName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="formData.responseDirective === 0" :gutter="20">
        <el-col :span="24">
          <el-form-item label="响应指令自定义" prop="responseDirectiveCustom">
            <el-input
              v-model="formData.responseDirectiveCustom"
              type="textarea"
              :rows="3"
              placeholder="请输入响应指令"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="机构/单位/名称" prop="orgList">
            <el-select
              v-model="formData.orgId"
              placeholder="请选择机构/单位/名称"
              clearable
              filterable
              style="width: 100%"
              @change="handleOrgChange"
            >
              <el-option
                v-for="item in orgOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指挥部/现场指挥部" prop="commandCenter">
            <el-select
              v-model="formData.commandCenter"
              placeholder="请选择指挥部/现场指挥部"
              clearable
              filterable
            >
              <el-option
                v-for="item in commandCenterList"
                :key="item.id"
                :label="item.itemName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="现场指挥部" prop="fieldCommand">
            <el-select
              v-model="formData.fieldCommand"
              placeholder="请选择现场指挥部"
              clearable
              filterable
              style="width: 100%"
              @change="handleOrgChange"
            >
              <el-option
                v-for="item in fieldCommandList"
                :key="item.id"
                :label="item.itemName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="formData.fieldCommand === '20250726183012'" :gutter="20">
        <el-col :span="24">
          <el-form-item label="现场指挥部自定义" prop="fieldCommandCustom">
            <el-input
              v-model="formData.fieldCommandCustom"
              placeholder="请输入现场指挥部自定义"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色" prop="commandRole">
            <el-input
              v-model="formData.commandRole"
              placeholder="请输入角色"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成时间" prop="completionTime">
            <el-input
              v-model="formData.completionTime"
              placeholder="请输入完成时间，如：30分钟内"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="职责/任务" prop="responsibilities">
            <el-input
              v-model="formData.responsibilities"
              type="textarea"
              :rows="4"
              placeholder="请输入职责/任务描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
import { commandConfigurationApi, systemConfigApi } from "@/api";
import {
  commandCenterType,
  fieldCommandType,
  getItemList,
  inspectionDictionaryType,
} from "@/utils/dictionary";

export default {
  name: "CommandConfigurationForm",
  props: {
    type: {
      type: String,
      default: "config", //config:指令配置可以编辑事故类型和响应指令
    },
    isEditMode: {
      type: Boolean,
      default: false,
    },
    editingRow: {
      type: Object,
      default: null,
    },
    orgOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      submitting: false,

      emergencyTypeList: [],
      cascaderProps: {
        value: "id", // 指定选项的值为 id 字段
        label: "name", // 指定选项标签为 name 字段
        children: "children", // 指定子选项为 children 字段
      },
      responseCommandList: [
        {
          id: 0,
          commandName: "自定义",
        },
        {
          id: 1,
          commandName: "无",
        },
      ],
      commandCenterList: [],
      fieldCommandList: [],

      formData: {
        reportId: "",
        responseDirective: "",
        responseDirectiveCustom: "",
        orgList: [],
        commandCenter: "",
        fieldCommand: "",
        fieldCommandCustom: "",
        commandRole: "",
        completionTime: "",
        responsibilities: "",
      },
      formRules: {
        reportId: [
          { required: true, message: "请选择事故类型", trigger: "change" },
        ],
        responseDirective: [
          { required: true, message: "请选择响应指令", trigger: "blur" },
        ],
        orgList: [
          {
            required: true,
            message: "请选择机构/单位/名称",
            trigger: "change",
          },
        ],
        commandCenter: [
          {
            required: true,
            message: "请选择指挥部/现场指挥部",
            trigger: "change",
          },
        ],
        fieldCommand: [
          { required: true, message: "请选择现场指挥部", trigger: "change" },
        ],
        commandRole: [
          { required: true, message: "请输入角色", trigger: "blur" },
        ],
        completionTime: [
          { required: true, message: "请输入完成时间", trigger: "blur" },
        ],
        responsibilities: [
          { required: true, message: "请输入职责/任务", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    editingRow: {
      handler(newVal) {
        if (newVal) {
          this.initFormData();
        } else {
          this.resetForm();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.queryDictionaryType();
    this.queryEmergencyTypeList();
  },
  methods: {
    queryEmergencyTypeList() {
      try {
        systemConfigApi.queryEmergencyTypeList({}).then((res) => {
          this.emergencyTypeList = res?.data || [];
        });
      } catch (error) {}
    },

    async queryDictionaryType() {
      try {
        //指挥部/现场指挥部
        this.commandCenterList = await getItemList(commandCenterType);
        //现场指挥部
        this.fieldCommandList = await getItemList(fieldCommandType);
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    handleAccidentTypeChange() {
      const checkedNodes = this.$refs.accidentRef.getCheckedNodes(true);
      const selectOrgData = checkedNodes.map((node) => node.data);
      let reportId = selectOrgData.map((node) => node.id).join("");
      this.formData.reportId = reportId;
      this.queryResponseCommandList(reportId);
    },

    queryResponseCommandList(reportId) {
      try {
        commandConfigurationApi
          .queryInstructionTwoList({
            reportId,
          })
          .then((res) => {
            this.responseCommandList = res?.data || [];
            this.responseCommandList.push(
              {
                id: 0,
                commandName: "自定义",
              },
              {
                id: 1,
                commandName: "无",
              }
            );
          });
      } catch (error) {}
    },

    initFormData() {
      if (this.editingRow.reportId) {
        this.queryResponseCommandList(this.editingRow.reportId);
      }
      if (this.isEditMode && this.editingRow) {
        this.formData = {
          ...this.editingRow,
        };
      } else {
        this.resetForm(this.editingRow);
      }
    },

    resetForm(row) {
      this.formData = {
        reportId: row.reportId || "",
        responseDirective: row.responseDirective || "",
        responseDirectiveCustom: "",
        orgId: "",
        orgList: [],
        commandCenter: "",
        fieldCommand: "",
        fieldCommandCustom: "",
        commandRole: "",
        completionTime: "",
        responsibilities: "",
      };
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate();
      }
    },

    handleOrgChange(value) {
      const selectedOrg = this.orgOptions.find((item) => item.value === value);
      this.formData.orgName = selectedOrg ? selectedOrg.label : "";
      this.formData.orgList = [value];
    },

    handleCancel() {
      this.$emit("cancel");
    },

    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.submitting = true;

          // 模拟提交延迟
          setTimeout(() => {
            this.submitting = false;
            this.$emit("confirm", { ...this.formData });
          }, 500);
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.command-configuration-form {
  padding: 20px 65px 20px 65px;
}

.command-form {
  padding: 10px 0;
}

.form-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-footer .el-button {
  width: 100px;
  margin: 0 10px;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
}

::v-deep .el-input,
::v-deep .el-select,
::v-deep .el-textarea {
  width: 100%;
}

::v-deep .el-textarea__inner {
  resize: vertical;
}
</style>
