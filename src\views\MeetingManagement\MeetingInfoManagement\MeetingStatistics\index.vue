<!-- 会议统计---MeetingStatistics -->
 <template>
  <div class="duty-evaluation-statistics-container">
    <el-row style="margin-top: 10px;">
      <el-col :span="24">
        <!-- <el-card shadow="hover"> -->
          <el-form :inline="true" :model="searchData" class="demo-form-inline">
            <el-form-item label="时间">
              <el-date-picker
                v-model="searchData.publishTime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择时间"
                style="width: 400px;"
                type="datetime"
                @change="handleChangeMonth">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="searchData.noticeType" placeholder="请选择" style="width: 184px;">
                <el-option
                  v-for="item in noticeTypeList"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        <!-- </el-card> -->
      </el-col>
    </el-row>
    <el-row :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="chart-title">会议类型Top5排名</div>
          <el-divider content-position="right">
            <!-- <el-radio-group
              @change="changeScoreRadio"
              v-model="scoreRadio"
              size="small"
            >
              <el-radio-button label="平均分"></el-radio-button>
              <el-radio-button label="最高分"></el-radio-button>
              <el-radio-button label="最低分"></el-radio-button>
            </el-radio-group> -->
          </el-divider>
          <div class="chart-container">
            <echarts-component :options="scoreOptions" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="chart-title">部门入会比率</div>
          <el-divider></el-divider>
          <div class="chart-container">
            <div class="left">
              <echarts-component :options="typeOptions" />
            </div>
            <div class="right">
              <div class="percentage">
                <div
                  class="percentage-item"
                  v-for="item in percentageData"
                  :key="item.title"
                >
                  <div class="percentage-text">
                    <div class="title">{{ item.title }}</div>
                    <div class="number">{{ item.number }}%</div>
                  </div>
                  <el-progress
                    :show-text="false"
                    :stroke-width="10"
                    :percentage="item.number"
                    :color="item.color"
                  ></el-progress>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="chart-title">部门会议信息类别统计</div>
          <el-divider></el-divider>
          <div class="chart-container">
            <echarts-component :options="completionOptions1" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="chart-title">开会频率</div>
          <el-divider></el-divider>
          <div class="chart-container">
            <echarts-component :options="completionOptions2" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <detail-dialog ref="detailDialogRef" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import echartsComponent from "@/components/echarts.vue";
import detailDialog from "@/views/ComprehensiveAssessment/OverallEvaluationStatement/components/detailDialog.vue";
import { conversionDate, getKKFilePreviewUrl } from "@/utils/publicMethod";
import * as echarts from "echarts/core";
import { getItemList, workNoticeType } from "@/utils/dictionary";

export default {
  name: "DutyEvaluationStatistics",
  components: {
    PortalTable,
    echartsComponent,
    detailDialog,
  },
  data() {
    return {
      noticeTypeList: [],
      searchData: {
        publishTime: "",
        noticeType: "",
      },
      StatisticsData: [
        {
          title: "总发布通知",
          data: "92",
          percenTip: "相比上一年度提升",
          percentage: "+3.2%",
        },
        {
          title: "参与通知单位",
          data: "125",
          percenTip: "全市共125个单位参与",
          percentage: "+5",
        },
        {
          title: "最长响应时间（分钟）",
          data: "52",
          percentage: "+3.2%",
        },
        // {
        //   title: "系统评分占比",
        //   data: "65%",
        //   percentage: "3.2%",
        // },
      ],

      typeOptions: {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {d}%",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542", "#24d2d3"],
        series: [
          {
            name: "通知类型分布",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 30, name: "市应急管理局" },
              { value: 25, name: "市公安局" },
              { value: 25, name: "市交通委员会" },
              { value: 20, name: "市气象局" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "119",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "总数",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData: [
        {
          title: "市应急管理局",
          number: 30,
          color: "#fb788a",
        },
        {
          title: "市公安局",
          number: 25,
          color: "#52c1f5",
        },
        {
          title: "市交通委员会",
          number: 25,
          color: "#ffc542",
        },
        {
          title: "市气象局",
          number: 20,
          color: "#24d2d3",
        },
      ],
      
      scoreRadio: "平均分",
      scoreOptions: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "1%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          // axisLine: {
          //   show: false, // 隐藏X轴线
          // },
          // axisTick: {
          //   show: false, // 隐藏X轴刻度
          // },
          // splitLine: {
          //   show: false, // 隐藏X轴网格线
          // },
          // axisLabel: {
          //   show: false, // 保留Y轴文字标签
          // },
        },
        yAxis: {
          type: "category",
          data: [
            "培训会议", 
            "视频例会", 
            "风险研判会", 
            "专题讨论会", 
            "领导精神传达会"
          ],
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            show: false, // 隐藏Y轴线
          },
          axisTick: {
            show: false, // 隐藏Y轴刻度
          },
          axisLabel: {
            show: true, // 保留Y轴文字标签
            margin: 10, // 增加Y轴标签与柱子的距离
          },
        },
        series: [
          {
            name: "参加次数",
            type: "bar",
            barWidth: "30%", // 减小柱子宽度以增加间距
            barGap: "30%", // 设置柱子之间的间距
            itemStyle: {
              color: "#6ab7ff", // 修改柱子颜色为蓝色
              borderRadius: [50, 50, 50, 50], // 设置柱子端头为圆角（右侧圆角）
            },
            // label: {
            //   show: true,
            //   position: "insideRight", // 数值显示在柱子右侧
            //   formatter: "{c}", // 显示数据值
            //   color: "#fff", // 文字颜色
            //   fontSize: 12, // 文字大小
            //   fontWeight: "bold", // 文字加粗
            //   offset: [-10, 0], // 向右偏移5像素
            // },
            data: [80, 18, 15, 80, 89],
          },
          {
            name: "组织次数",
            type: "bar",
            barWidth: "30%", // 减小柱子宽度以增加间距
            barGap: "30%", // 设置柱子之间的间距
            itemStyle: {
              color: "#ff9912", // 修改柱子颜色为蓝色
              borderRadius: [50, 50, 50, 50], // 设置柱子端头为圆角（右侧圆角）
            },
            // label: {
            //   show: true,
            //   position: "insideRight", // 数值显示在柱子右侧
            //   formatter: "{c}", // 显示数据值
            //   color: "#fff", // 文字颜色
            //   fontSize: 12, // 文字大小
            //   fontWeight: "bold", // 文字加粗
            //   offset: [-30, 0], // 向右偏移5像素
            // },
            data: [100, 20, 18, 120, 90],
          },
        ],
      },

      completionOptions1: {
        // 修改为两种颜色：绿色(#80FFA5)和灰色(#CCCCCC)
        color: ["#80FFA5", "#CCCCCC"],
        title: {
          text: "", // 清空标题（根据原图无标题）
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        // 修改图例为两个数据系列
        legend: {
          data: ["网络视频会", "线下会"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            // 修改为12个月份
            data: [
              "市委总值班室",
              "市应急委办公室",
              "市应急指挥中心",
              "局总值班室",
              // "5月",
              // "6月",
              // "7月",
              // "8月",
              // "9月",
              // "10月",
              // "11月",
              // "12月",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            // 坐标轴线样式
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#333", // 深色坐标轴
              },
            },
            // 坐标轴标签
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            // 禁用网格线（无网格线）
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          // 系统评价（灰色系列）
          {
            name: "网络视频会",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#CCCCCC" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [
              // 5, 1, 6, 
              5, 15, 37, 15, 
              // 8, 5, 2, 9, 6
            ],
          },
          // 自评完成（绿色系列）
          {
            name: "线下会",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(141, 204, 171, 0.8)" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [
              // 0, 5, 15, 20, 
              30, 5, 40, 8,
              // 12, 15, 10, 0
            ], // 模拟峰值数据
          },
        ],
      },
      typeOptions1: {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {d}%",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542"],
        series: [
          {
            name: "评价分布",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 40, name: "系统评价" },
              { value: 35, name: "自评" },
              { value: 25, name: "待审核" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "84.5",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "平均分",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData1: [
        {
          title: "预警信息",
          number: 30,
          color: "#fb788a",
        },
        {
          title: "处置任务",
          number: 25,
          color: "#52c1f5",
        },
        {
          title: "应急演练",
          number: 25,
          color: "#ffc542",
        },
        {
          title: "其他情况",
          number: 20,
          color: "#24d2d3",
        },
      ],

      completionOptions2: {
        // 修改为两种颜色：绿色(#80FFA5)和灰色(#CCCCCC)
        color: ["#80FFA5", "#CCCCCC"],
        title: {
          text: "", // 清空标题（根据原图无标题）
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        // 修改图例为两个数据系列
        legend: {
          data: ["网络视频会", "线下会"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            axisLabel: {
              // color: "rgba(148, 204, 254, 1)",
              // fontSize: 16,
              rotate: 45,
            },
            // 修改为12个月份
            data: [
              "2025-01",
              "2025-02",
              "2025-03",
              "2025-04",
              "2025-05",
              "2025-06",
              "2025-07",
              "2025-08",
              "2025-09",
              "2025-10",
              "2025-11",
              "2025-12",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            // 坐标轴线样式
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#333", // 深色坐标轴
              },
            },
            // 坐标轴标签
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            // 禁用网格线（无网格线）
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          // 系统评价（灰色系列）
          {
            name: "开会次数",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              color: "#385C90",
              width: 3,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#CCCCCC" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [
              10, 30, 21, 
              45, 47, 58, 38, 
              31, 27, 70, 39, 80
            ],
          },
        ],
      },
    };
  },
  mounted() {
    this.queryDictionaryType();
  },
  methods: {
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.noticeTypeList = await getItemList(workNoticeType);
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    handleChangeMonth(value) {
      this.searchData.publishTime = conversionDate(value);
    },
    handleSearch(params) {
      console.log(params);
    },
    getPercentageColor(percentage) {
      const value = parseFloat(percentage);
      if (value > 1) return "#67C23A";
      if (value < 1) return "#F56C6C";
      return "#999"; // 等于1时的颜色
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
    },
    handleDetail(row) {
      this.$refs.detailDialogRef.detailDialogVisible = true;
    },
    changeScoreRadio(item) {
      console.log(item);
    },
    onSubmit() {
      console.log("sssssssss");
    },
  },
};
</script>

<style lang="scss" scoped>
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
  .Statistics {
    display: flex;
    gap: 30px;
    .Statistics-item {
      padding-left: 15px;
      height: 140px;
      display: flex;
      gap: 15px;
      .vertical-line {
        width: 6px;
        height: 120px;
      }
      .bg-color-0 {
        background-color: #007bff; // 蓝色
      }
      .bg-color-1 {
        background-color: #9f47ec; // 绿色
      }
      .bg-color-2 {
        background-color: #0ce63f; // 黄色
      }
      .bg-color-3 {
        background-color: #cc3b4e; // 红色
      }

      .content {
        height: 100px;
        display: flex;
        align-items: center;
        margin-top: 10px;
        gap: 20px;
        .image {
          width: 50px;
          height: 50px;
        }
        .content-text {
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-bottom: 1px solid #3b3a3a;
          width: 200px;
          .title {
            font-size: 15px;
            margin-bottom: 5px;
          }
          .data {
            display: flex;
            gap: 40px;
            align-items: flex-end;
            margin-bottom: 5px;
            .number {
              font-size: 20px;
              font-weight: bold;
              color: #007bff;
            }
            .unit {
              font-size: 10px;
              color: #999;
            }
          }
          .percentage {
            font-size: 15px;
            color: #999;
          }
        }
      }
    }
  }
  .chart-title,
  .table-title {
    font-size: 20px;
    font-weight: bold;
  }
  .chart-container {
    height: 300px;
    display: flex;
    gap: 20px;
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 8%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .table-container {
    height: 400px;
    // margin-top: 30px;
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>