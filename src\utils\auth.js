import { STORAGE_KEYS } from "@/constants";
import crypto from "./crypto";

/**
 * 认证工具类
 */
class AuthUtils {
  /**
   * 获取token
   */
  getToken() {
    const encryptedToken =
      localStorage.getItem(STORAGE_KEYS.TOKEN) ||
      sessionStorage.getItem(STORAGE_KEYS.TOKEN);
    return encryptedToken ? crypto.decrypt(encryptedToken) : null;
  }

  /**
   * 设置token
   * @param {string} token - 访问令牌
   * @param {boolean} remember - 是否记住登录状态
   */
  setToken(token, remember = false) {
    const encryptedToken = crypto.encrypt(token);
    if (remember) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, encryptedToken);
      sessionStorage.removeItem(STORAGE_KEYS.TOKEN);
    } else {
      sessionStorage.setItem(STORAGE_KEYS.TOKEN, encryptedToken);
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
    }
  }

  /**
   * 移除token
   */
  removeToken() {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    sessionStorage.removeItem(STORAGE_KEYS.TOKEN);
  }

  /**
   * 获取刷新token
   */
  getRefreshToken() {
    const encryptedToken =
      localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN) ||
      sessionStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    return encryptedToken ? crypto.decrypt(encryptedToken) : null;
  }

  /**
   * 设置刷新token
   * @param {string} refreshToken - 刷新令牌
   * @param {boolean} remember - 是否记住登录状态
   */
  setRefreshToken(refreshToken, remember = false) {
    const encryptedToken = crypto.encrypt(refreshToken);
    if (remember) {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, encryptedToken);
      sessionStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    } else {
      sessionStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, encryptedToken);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    }
  }

  /**
   * 移除刷新token
   */
  removeRefreshToken() {
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    sessionStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  /**
   * 获取token类型
   */
  getTokenType() {
    return (
      localStorage.getItem(STORAGE_KEYS.TOKEN_TYPE) ||
      sessionStorage.getItem(STORAGE_KEYS.TOKEN_TYPE) ||
      "Bearer"
    );
  }

  /**
   * 设置token类型
   * @param {string} tokenType - token类型
   * @param {boolean} remember - 是否记住登录状态
   */
  setTokenType(tokenType, remember = false) {
    if (remember) {
      localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokenType);
      sessionStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
    } else {
      sessionStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokenType);
      localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
    }
  }

  /**
   * 移除token类型
   */
  removeTokenType() {
    localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
    sessionStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    const encryptedUserInfo =
      localStorage.getItem(STORAGE_KEYS.USER_INFO) ||
      sessionStorage.getItem(STORAGE_KEYS.USER_INFO);
    return encryptedUserInfo ? crypto.decryptObject(encryptedUserInfo) : null;
  }

   /**
   * 获取文件服务器地址
   */
  getFileBaseUrl() {
    const encryptedUserInfo =
      localStorage.getItem(STORAGE_KEYS.USER_INFO) ||
      sessionStorage.getItem(STORAGE_KEYS.USER_INFO);
    const userInfo = encryptedUserInfo ? crypto.decryptObject(encryptedUserInfo) : null;
    return userInfo.fileBaseUrl;
  }

  /**
   * 设置用户信息
   * @param {object} userInfo - 用户信息
   * @param {boolean} remember - 是否记住登录状态
   */
  setUserInfo(userInfo, remember = false) {
    const encryptedUserInfo = crypto.encryptObject(userInfo);
    if (remember) {
      localStorage.setItem(STORAGE_KEYS.USER_INFO, encryptedUserInfo);
      sessionStorage.removeItem(STORAGE_KEYS.USER_INFO);
    } else {
      sessionStorage.setItem(STORAGE_KEYS.USER_INFO, encryptedUserInfo);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    }
  }

  /**
   * 移除用户信息
   */
  removeUserInfo() {
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    sessionStorage.removeItem(STORAGE_KEYS.USER_INFO);
  }

  /**
   * 获取用户权限
   */
  getPermissions() {
    const encryptedPermissions =
      localStorage.getItem(STORAGE_KEYS.PERMISSIONS) ||
      sessionStorage.getItem(STORAGE_KEYS.PERMISSIONS);
    return encryptedPermissions
      ? crypto.decryptObject(encryptedPermissions)
      : [];
  }

  //获取是否存在具体权限码
  hasPermission = (permissionCode) => {
    // 导入权限管理器进行实际权限检查
    const { hasPermission } = require("./permissionManager");
    return hasPermission(permissionCode);
  };

  /**
   * 设置用户权限
   * @param {array} permissions - 权限列表
   * @param {boolean} remember - 是否记住登录状态
   */
  setPermissions(permissions, remember = false) {
    const encryptedPermissions = crypto.encryptObject(permissions);
    if (remember) {
      localStorage.setItem(STORAGE_KEYS.PERMISSIONS, encryptedPermissions);
      sessionStorage.removeItem(STORAGE_KEYS.PERMISSIONS);
    } else {
      sessionStorage.setItem(STORAGE_KEYS.PERMISSIONS, encryptedPermissions);
      localStorage.removeItem(STORAGE_KEYS.PERMISSIONS);
    }
  }

  /**
   * 移除用户权限
   */
  removePermissions() {
    localStorage.removeItem(STORAGE_KEYS.PERMISSIONS);
    sessionStorage.removeItem(STORAGE_KEYS.PERMISSIONS);
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken();
  }

  /**
   * 清除所有认证信息
   */
  clearAuth() {
    this.removeToken();
    this.removeRefreshToken();
    this.removeTokenType();
    this.removeUserInfo();
    this.removePermissions();

    // 清除菜单和路由相关数据
    this.clearMenuAndRouteData();

    // 清理历史遗留的不需要的数据
    this.cleanupLegacyData();
  }

  /**
   * 清除菜单和路由相关数据
   */
  clearMenuAndRouteData() {
    // 清除菜单数据
    localStorage.removeItem("userMenu");
    sessionStorage.removeItem("userMenu");

    // 清除按钮权限数据
    localStorage.removeItem("userPermissions");
    sessionStorage.removeItem("userPermissions");
  }

  /**
   * 清理历史遗留的不需要的存储数据
   */
  cleanupLegacyData() {
    const legacyKeys = [
      "token_expiration",
      "refresh_token_expiration",
      "login_time",
      "rememberedCredentials",
      "loginAttempts",
    ];

    legacyKeys.forEach((key) => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  }
}

export default new AuthUtils();
