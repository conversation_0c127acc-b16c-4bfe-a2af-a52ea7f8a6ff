<template>
  <div class="evaluation-criteria">
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover">
          <el-form :inline="true" :model="searchData" class="demo-form-inline">
            <el-form-item label="通知标题">
              <el-input
                v-model="searchData.noticeTitle"
                placeholder="请输入通知标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送状态">
              <el-select
                v-model="searchData.sendStatus"
                placeholder="请选择发送状态"
              >
                <el-option
                  v-for="item in sendStatusOption"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="onSubmit"
                >高级筛选</el-button
              >
              <el-button type="primary" icon="el-icon-refresh" @click="onReset"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <PortalTable
            :tableHeight="600"
            :columns="columns"
            :table-data="tableData"
            row-key="id"
            :pagination="pagination"
            :showAddButton="false"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-card>
      </el-col>
    </el-row>
    <addDialog ref="addDialogRef" @refreshTableData="refreshTableData" />
    <viewDialog ref="viewDialogRef" />
    <!-- <el-row style="margin: 0">
      <el-col :span="24">
        <cardComponent :pieceList="pieceList"></cardComponent>
      </el-col>
    </el-row> -->
  </div>
</template>

<script>
import cardComponent from "./components/cardComponent.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import addDialog from "./components/addDialog.vue";
import viewDialog from "./components/viewDialog.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  name: "EvaluationCriteria",
  components: {
    cardComponent,
    PortalTable,
    addDialog,
    viewDialog,
  },
  data() {
    return {
      pieceList: [
        {
          title: "本月发生通知",
          text: "5",
          status: "25",
          statusText: "较上月 +25%",
        },
        {
          title: "通知阅读率",
          text: "83%",
          status: "8",
          statusText: "较上月 +8%",
        },
        {
          title: "待处理通知",
          text: "3",
          status: "1",
          statusText: "较昨日新增 1",
        },
      ],
      searchData: {
        noticeTitle: "",
        sendStatus: "",
      },
      sendStatusOption: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "待发送",
          value: "0",
        },
        {
          label: "已发送",
          value: "1",
        },
        {
          label: "已阅读",
          value: "2",
        },
        {
          label: "待阅读",
          value: "3",
        },
      ],
      columns: [
        { text: true, prop: "noticeNum", label: "通知编号", minWidth: 280 },
        { text: true, prop: "noticeTitle", label: "通知标题", minWidth: 380 },
        { text: true, prop: "sendTime", label: "发送时间", width: 120 },
        { text: true, prop: "receiverUnit", label: "接收单位", width: 180 },
        { text: true, prop: "sendStatus", label: "状态", width: 100 },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "详情",
              permission: "notice:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
    };
  },
  methods: {
    // 查询评价通知列表分页
    queryNoticePage() {
      comprehensiveAssessmentApi
        .queryNoticePage({
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          ...this.searchData,
        })
        .then((res) => {
          this.tableData = res.data.items;
          this.pagination.total = res.data.total;
        });
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryNoticePage();
    },
    onReset() {
      this.searchData = {
        noticeTitle: "",
        sendStatus: "",
      };
      this.queryNoticePage();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.queryNoticePage();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.queryNoticePage();
    },
    handleDetail(row) {
      this.$refs.viewDialogRef.dialogVisible = true;
      this.$refs.viewDialogRef.queryNoticeInfo(row);
    },

    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "import_top",
        handler: this.importTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.exportTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.downloadTopHandler,
      });
    },
    addTopHandler() {
      console.log("添加");
      this.$refs.addDialogRef.resetForm();
      this.$refs.addDialogRef.dialogVisible = true;
      this.$refs.addDialogRef.dialogTitle = "新增评价通知";
    },
    async importTopHandler() {
      try {
        // 创建文件输入元素
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".xlsx,.xls";

        input.onchange = async (e) => {
          const file = e.target.files[0];
          if (!file) return;

          // 创建FormData对象
          const formData = new FormData();
          formData.append("file", file);

          // 添加其他参数（如果需要）
          // formData.append("noticeTitle", this.searchData.noticeTitle);
          // formData.append("sendStatus", this.searchData.sendStatus);

          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: "文件上传中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          try {
            // 调用API
            const res = await comprehensiveAssessmentApi.importNotice(formData);

            if (res.code === 0) {
              this.$message.success("导入成功");
              this.refreshTableData(); // 刷新表格数据
            } else {
              this.$message.error(res.message || "导入失败");
            }
          } catch (error) {
            console.error("导入错误:", error);
            this.$message.error("导入失败: " + (error.message || "未知错误"));
          } finally {
            loading.close();
          }
        };

        // 触发文件选择
        input.click();
      } catch (error) {
        console.error("导入异常:", error);
        this.$message.error("导入异常: " + error.message);
      }
    },
    async exportTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.exportList({
          noticeTitle: this.searchData.noticeTitle,
          sendStatus: this.searchData.sendStatus,
        });

        if (!res.data) {
          throw new Error("API返回数据为空");
        }

        // 添加响应类型检查;
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价通知.xlsx";
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败: " + error.message);
      }
    },
    async downloadTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.downloadTemplate({
          type: 3,
        });
        if (!res.data) {
          throw new Error("API返回数据为空");
        }
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价通知模板.xlsx";
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败: " + error.message);
      }
    },
    refreshTableData() {
      this.pagination.currentPage = 1;
      this.searchData = {
        noticeTitle: "",
        sendStatus: "",
      };
      this.queryNoticePage();
    },
  },
  mounted() {
    this.registerHandlers();
    this.queryNoticePage();
  },
};
</script>

<style lang="scss" scoped>
.evaluation-criteria {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  // overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>