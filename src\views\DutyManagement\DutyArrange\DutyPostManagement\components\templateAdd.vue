<template>
  <div class="add-eidt">
    <div>
      <el-form
        ref="addForm"
        :model="formData"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="值班模板名称" prop="name">
          <el-input
            v-model="formData.name"
            autocomplete="off"
            placeholder="请输入"
            type="text"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="所属月份" prop="monthList">
          <!-- <el-date-picker
            v-model="formData.months"
            format="yyyy-MM"
            placeholder="选择时间"
            style="width: 184px;"
            type="month"
            @change="handleChangeMonth">
          </el-date-picker> -->

          <el-select v-model="formData.monthList" multiple placeholder="请选择月份">
            <el-option
              v-for="item in monthsList"
              :key="item.value"
              :label="item.month"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
  
        <el-form-item class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </el-form-item>
      </el-form>
    
    </div>
  </div>
</template>

<script>
import { dutyManagementApi } from '@/api/index'
import { conversionDateNotTime } from '@/utils/publicMethod'

export default {
  components: {},
  data() {
    return {
      styleType: 1, // 1新增; 2修改
      formData: {
        name: '',
        monthList: []
      },
      monthsList:[
        {
          month:"1月",
          value:"1"
        },
        {
          month:"2月",
          value:"2"
        },
        {
          month:"3月",
          value:"3"
        },
        {
          month:"4月",
          value:"4"
        },
        {
          month:"5月",
          value:"5"
        },
        {
          month:"6月",
          value:"6"
        },
        {
          month:"7月",
          value:"7"
        },
        {
          month:"8月",
          value:"8"
        },
        {
          month:"9月",
          value:"9"
        },
        {
          month:"10月",
          value:"10"
        },
        {
          month:"11月",
          value:"11"
        },
        {
          month:"12月",
          value:"12"
        },
      ],
      rules: {
        name: [
          {required: true, message: '模板名称不能为空', trigger: 'blur'}
        ],
        monthList: [
          {required: true, message: '所属日期不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    // handleChangeMonth(value) {
    //   this.formData.months = conversionDateNotTime(value);
    // },

    handleConfirm() {
      //提交
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await dutyManagementApi.createPositionTemplate(this.formData)
            const {code, error} = res
            if (code === 0) {
              this.$message.success('新增成功')
              this.$emit('ok')
              this.reset()
              this.close()
            } else {
              this.$message.error(error)
            }
          } else {
            // params.id = this.model.id
            const res = await dutyManagementApi.updatePositionTemplate(this.formData)
            const {code, error} = res
            if (code === 0) {
              this.$message.success('修改成功')
              this.$emit('ok')
              this.close()
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false
        }
      })
    },
    close() {
      this.reset()
      this.$emit('ok', 1)
    },
    reset() {
      this.formData = {
        name: '',
        monthList: []
      }
      this.model = {}
      this.menuTypeList = []
      this.parentMenuList = []
    },
    addFormFn() {
      this.reset()
      this.styleType = 1
    },
    edit(templateId) {
      this.styleType = 2
      this.getPositionTemplateById(templateId);
    },
    async getPositionTemplateById(id) {
      const res = await dutyManagementApi.queryPositionTemplateById({id})
      const {code, data, error} = res;
      if (code === 0) {
        this.formData.id = data.id;
        this.formData.name = data.name;
        this.formData.monthList = data.monthList;
      } else {
        this.$message.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dialog-footer ::v-deep {
  text-align: center;
  margin: 30px auto;

  .el-button {
    width: 100px;
    height: 40px;
  }
}

.icon-box {
  width: 100%;
  overflow: hidden;

  .svg-color {
    width: 40px;
    height: 40px;
    margin: 2.7%;
    color: var(--themeColor);
    float: left;
    display: block;
    cursor: pointer;

    :hover {
      color: $tiffany;
    }
  }
}

.img-svg {
  display: inline;
  font-size: 18px;
  position: absolute;
  left: 10px;
  top: 1px;
  color: var(--themeColor);
}

.svg-input-box {
  width: 78.5%;
  margin-right: 3%;

  ::v-deep .el-input__inner {
    padding-left: 36px;
  }
}
</style>
