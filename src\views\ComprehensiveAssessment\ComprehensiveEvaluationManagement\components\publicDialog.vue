<template>
  <div class="dialog-container" v-if="dialogVisible">
    <div class="dialog-title">
      {{ title }}
    </div>
    <div class="divider-line"></div>
    <el-row>
      <el-card class="box-card" shadow="hover">
        <div class="dialog-content">
          <div class="user-info">
            <div class="item-title">个人基本信息</div>
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item
                v-for="item in userInfoList"
                :label="item.label"
                :key="item.key"
              >
                <el-input
                  v-model="item.value"
                  disabled
                  :placeholder="`请输入${item.label}`"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>
    </el-row>
    <el-row>
      <el-card class="box-card" shadow="hover">
        <div class="item-title">评分细则</div>
        <el-table border :data="tableData" height="300px" style="width: 100%">
          <el-table-column
            v-for="(item, index) in columnList"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center"
            :width="item.width"
            :min-width="item.minWidth"
          >
            <template slot-scope="scope">
              <el-input
                v-if="item.type === 'input'"
                v-model.number="scope.row[item.prop]"
                :disabled="dialogType === 'detail' || dialogType === 'audit'"
                :index="key"
                @input="handleInput(scope.row, item.prop, scope.row[item.prop])"
              />
              <el-upload
                v-else-if="item.type === 'upload'"
                class="upload-demo"
                action="#"
                :http-request="(options) => customUpload(scope.row, options)"
                :before-upload="beforeUpload"
                :file-list="scope.row.fileList"
                :on-remove="
                  (file, fileList) => handleRemove(scope.row, file, fileList)
                "
              >
                <el-button
                  slot="trigger"
                  size="mini"
                  type="primary"
                  :disabled="dialogType === 'detail' || dialogType === 'audit'"
                >
                  上传材料
                </el-button>
              </el-upload>

              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-row>
    <el-row v-if="dialogType === 'audit'">
      <el-card class="box-card" shadow="hover">
        <div class="item-title">审核操作</div>
        <div class="audit-content">
          <el-form label-position="top">
            <el-form-item label="综合得分调整">
              <div class="finalScore">
                <div>当前得分：{{ totalFinalScore }}</div>
                <el-input
                  v-model.number="auditForm.auditFinalScore"
                  type="number"
                ></el-input>
                <div>分</div>
              </div>
            </el-form-item>
            <el-form-item label="调整说明">
              <el-input
                type="textarea"
                v-model="auditForm.auditRemark"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核意见">
              <el-input
                type="textarea"
                v-model="auditForm.auditOpinion"
              ></el-input>
            </el-form-item>
            <el-form-item class="sub-btn">
              <el-button @click="returnClick">返回</el-button>
              <el-button type="success" @click="auditPassComprehensive"
                >审核通过</el-button
              >
              <el-button type="warning" @click="auditRejectComprehensive"
                >退回修改</el-button
              >
              <el-button type="info" @click="auditSaveComprehensive"
                >保存草稿</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </el-row>
    <div class="sub-btn" v-if="dialogType !== 'audit'">
      <el-button @click="returnClick">返回</el-button>
      <el-button
        type="primary"
        @click="submitComprehensive"
        v-if="dialogType !== 'detail'"
        >提交评价</el-button
      >
      <el-button
        type="primary"
        @click="saveComprehensive"
        v-if="dialogType !== 'detail'"
        >保存草稿</el-button
      >
    </div>
  </div>
</template>

<script>
import { auth } from "@/utils";
import { comprehensiveAssessmentApi, emergencyKnowledgeBaseApi } from "@/api";

export default {
  data() {
    return {
      dialogType: "",
      dialogVisible: false,
      title: "",
      userInfoList: [
        {
          label: "姓名",
          value: "",
          key: "username",
        },
        {
          label: "部门",
          value: "",
          key: "orgName",
        },
        {
          label: "职位",
          value: "",
          key: "positions",
        },
        {
          label: "联系电话",
          value: "",
          key: "phone",
        },
      ],
      //  列
      columnList: [
        { type: "text", prop: "name", label: "细则名称", minWidth: 180 },
        { type: "text", prop: "criteria", label: "评分标准", minWidth: 380 },
        { type: "text", prop: "score", label: "分值", width: 100 },
        { type: "text", prop: "systemScore", label: "系统得分", width: 100 },
        {
          type: "input",
          prop: "selfRatedScore",
          label: "自评分数",
          width: 100,
        },
        {
          type: "calculate",
          prop: "finalScore",
          label: "最终分数",
          width: 100,
        },
        { type: "upload", prop: "file", label: "材料", width: 250 },
      ],
      tableData: [],
      key: 0,
      form: {
        userId: "",
        evaluateList: [],
        id: "",
      },
      input: 0,
      totalFinalScore: 0, //获取回来的最终分数
      auditForm: {
        id: "",
        auditFinalScore: 0,
        auditRemark: "",
        auditOpinion: "",
      },
    };
  },

  methods: {
    getUserInfo() {
      const info = auth.getUserInfo();
      this.userInfoList.map((item) => {
        item.value = info[item.key];
      });
      this.form.userId = info.id;
    },
    // 查询评价细则列表分页
    async queryCriteriaList() {
      const params = {
        page: 1,
        count: 100,
      };
      const res = await comprehensiveAssessmentApi.queryCriteriaList(params);
      if (res.code === 0) {
        this.tableData = res.data.items;
        this.tableData.map((item) => {
          item.selfRatedScore = 0;
          item.systemScore = 0;
          item.finalScore = 0;
          item.fileList = [];
        });
      }
    },
    handleInput(row, prop, value) {
      this.key++;
      this.finalScoreComputed(row);
    },
    finalScoreComputed(row) {
      row.finalScore = (row.selfRatedScore + row.systemScore) / 2;
    },
    systemScoreComputed(row) {
      row.systemScore = (row.selfRatedScore + row.systemScore) / 2;
    },
    async customUpload(row, options) {
      const formData = new FormData();
      formData.append("file", options.file);
      try {
        const res = await emergencyKnowledgeBaseApi.uploadFile(formData);
        if (res.code === 0) {
          res.data.name = res.data.fileName;
          row.systemScore = 2;
          this.finalScoreComputed(row);
          row.fileList.push(res.data);
          this.key++;
        }
      } catch (error) {
        console.error("上传失败", error);
      }
    },
    beforeUpload(file) {
      const ext = file.name.split(".").pop().toLowerCase();
      const targetExtensions = ["rar", "zip", "doc", "docx", "pdf", "jpg"];
      const includes = targetExtensions.includes(ext);
      if (!includes) {
        this.$message.warning(
          "上传文件只能是 rar、zip、doc、docx、pdf、jpg 格式!"
        );
      }
      return includes;
    },
    handleRemove(row, file, fileList) {
      row.fileList = fileList;
      console.log(row.fileList, fileList);
      if (row.fileList.length === 0) {
        row.systemScore = 0;
        this.finalScoreComputed(row);
        this.key++;
      }
    },
    // 提交用户评价上报
    async submitComprehensive() {
      this.form.evaluateList = this.tableData;
      const res = await comprehensiveAssessmentApi.submitComprehensive(
        this.form
      );
      if (res.code === 0) {
        this.$message.success("提交成功");
        this.dialogVisible = false;
        this.$emit("refreList");
      }
    },
    // 保存用户评价上报草稿
    async saveComprehensive() {
      this.form.evaluateList = this.tableData;
      const res = await comprehensiveAssessmentApi.saveComprehensive(this.form);
      if (res.code === 0) {
        this.$message.success("保存成功");
        this.dialogVisible = false;
        this.$emit("refreList");
      }
    },
    // 查看用户评价上报详情
    async queryComprehensiveById() {
      const res = await comprehensiveAssessmentApi.queryComprehensiveById({
        id: this.form.id,
      });
      if (res.code === 0) {
        this.tableData = res.data.evaluateList;
        console.log(this.tableData, "jjjjjjjjjjj");
        this.tableData.forEach((item) => {
          if (item.fileList && item.fileList.length > 0) {
            item.fileList = item.fileList.map((file) => {
              file.name = file.fileName;
              return file;
            });
          } else {
            item.fileList = [];
          }
        });
        this.totalFinalScore = res.data.totalFinalScore;
        this.auditForm.auditFinalScore = res.data.auditFinalScore;
        this.auditForm.id = res.data.id;
        this.userInfoList.map((item) => {
          item.value = res.data[item.key];
        });
      }
    },
    // 审核通过
    async auditPassComprehensive() {
      const res = await comprehensiveAssessmentApi.auditPassComprehensive(
        this.auditForm
      );
      if (res.code === 0) {
        this.$message.success("审核通过");
        this.dialogVisible = false;
        this.$emit("refreList");
      }
    },
    // 审核驳回
    async auditRejectComprehensive() {
      const res = await comprehensiveAssessmentApi.auditRejectComprehensive(
        this.auditForm
      );
      if (res.code === 0) {
        this.$message.success("审核驳回");
        this.dialogVisible = false;
        this.$emit("refreList");
      }
    },
    // 审核保存草稿
    async auditSaveComprehensive() {
      const res = await comprehensiveAssessmentApi.auditSaveComprehensive(
        this.auditForm
      );
      if (res.code === 0) {
        this.$message.success("保存草稿");
        this.dialogVisible = false;
        this.$emit("refreList");
      }
    },
    returnClick() {
      console.log("ooooooooo");

      this.dialogVisible = false;
      this.$emit("dutyShow");
    },
  },
  mounted() {
    this.getUserInfo();
  },
};
</script>;
<style lang="scss" scoped>
.dialog-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #fff;
  padding: 10px 30px;
  .dialog-title {
    font-size: 20px;
    font-weight: bold;
  }
  .divider-line {
    height: 1px;
    background-color: #e0e0e0;
    margin: 20px 0;
  }
  .dialog-content {
    margin: 0px 0 0 10px;
    .user-info {
      width: 100%;
      .demo-form-inline {
        margin-left: 20px;
      }
    }
  }
  .audit-content {
    width: 100%;
    .finalScore {
      display: flex;
      justify-content: start;
      align-items: center;
      gap: 10px;
      .el-input {
        width: 100px;
      }
    }
  }
  .sub-btn {
    display: flex;
    justify-content: center;
    gap: 20px;
  }
}
.item-title {
  font-size: 17px;
  font-weight: bold;
  margin-bottom: 20px;
}
::v-deep .el-row {
  margin-bottom: 20px;
}
</style>
