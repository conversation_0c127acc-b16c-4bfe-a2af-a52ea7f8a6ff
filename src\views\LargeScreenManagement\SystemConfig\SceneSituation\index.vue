<template>
  <div class="user-index-container">
    <portal-table
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :multilineWhiteSpace="'pre-line'"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />
    <!-- <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column v-if="hasExpandableRow(row)" type="expand">
        <template #default="{ row }">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="商品名称">
              <span>{{ row.name }}</span>
            </el-form-item>
            <el-form-item label="所属店铺">
              <span>{{ props.row.shop }}</span>
            </el-form-item>
            <el-form-item label="商品 ID">
              <span>{{ props.row.id }}</span>
            </el-form-item>
            <el-form-item label="店铺 ID">
              <span>{{ props.row.shopId }}</span>
            </el-form-item>
            <el-form-item label="商品分类">
              <span>{{ props.row.category }}</span>
            </el-form-item>
            <el-form-item label="店铺地址">
              <span>{{ props.row.address }}</span>
            </el-form-item>
            <el-form-item label="商品描述">
              <span>{{ props.row.desc }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        label="商品 ID"
        prop="id">
      </el-table-column>
      <el-table-column
        label="商品名称"
        prop="name">
      </el-table-column>
      <el-table-column
        label="描述"
        prop="desc">
      </el-table-column>
    </el-table> -->

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="资源分类名称" prop="name">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 430px;" 
            v-model="form.name" 
            :rows="2" />
        </el-form-item>

        <el-form-item label="类型" prop="name">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 430px;" 
            v-model="form.name" 
            :rows="2" />
        </el-form-item>

        <el-form-item label="关联事件" prop="sourceList">
          <el-select 
            :disabled="styleType === 3" 
            v-model="form.sourceList" 
            multiple 
            placeholder="请选择关联事件" 
            style="width: 430px;">
            <el-option
              v-for="item in mapSourceList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="styleType != 4" label="排序" prop="sort">
          <el-input
            :disabled="styleType === 3"
            style="width: 430px;" 
            v-model="form.sort"
            :maxlength="5"
            autocomplete="off"
            placeholder="请输入排序号"
            type="number"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 430px;" 
            v-model="form.remark" 
            placeholder="请输入" 
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5}" />
        </el-form-item>
      </el-form>
    </general-dialog>

    <sceneGroup ref="sceneGroupRef"> </sceneGroup>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, systemConfigApi } from "@/api";
import { getItemList, infoEventDictionaryType } from "@/utils/dictionary";

import { conversionDateNotSecond } from "@/utils/publicMethod";

import sceneGroup from "./components/sceneGroup.vue";


export default { 
  name: "SceneSituation",
  components: {
    GeneralDialog,
    PortalTable,
    sceneGroup,
  },
  data() {
    return {
      searchItems: [
        {
          label: "部门名称",
          prop: "name",
          type: "input",
          placeholder: "请输入部门名称",
          width: "200",
        },
        {
          label: "事件",
          prop: "name",
          type: "input",
          placeholder: "请输入关联事件",
          width: "200",
        },
      ],
      columns: [
        // { prop: "name", label: "展开", text: true },
        // { prop: "shopId", label: "排序", text: true },
        { prop: "name", width: "150px", label: "名称", text: true },
        { prop: "shopId", width: "150px", label: "类型", text: true },
        { prop: "desc", label: "关联事件", text: true },
        { prop: "address", width: "140px", label: "现场组", text: true },
        // { prop: "desc", label: "排序", text: true },
        // { prop: "infoType", label: "事件类型", text: true },
        // { prop: "infoStatus", label: "事件状态", text: true },
        // { prop: "status", label: "状态", text: true },
        // { prop: "recordMessage", label: "记录信息", text: true },
        // {
        //   prop: 'attentionStatus',
        //   label: '是否关注',
        //   switch: true,
        //   activeValue: 1,
        //   inactiveValue: 0,
        // },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '350px',
          operationList: [
            // {
            //   label: '查看',
            //   permission: 'mapResource:view',
            //   buttonClick: this.handleReview,
            //   isShow: (row, $index) => {
            //     return true
            //   }
            // },
            {
              label: '编辑',
              permission: 'mapResource:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'mapResource:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '管理组',
              permission: 'mapResource:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '管理成员',
              permission: 'mapResource:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      tableData: [
        {
          id: '12987122',
          name: '应急部',
          category: '江浙小吃、小吃零食',
          desc: '西城区荷兰优质淡奶奶香浓而不腻',
          address: '保障组\n通讯组\n新闻组\n应急救援组',
          shop: '王小虎夫妻店',
          shopId: '10333',
          testRows:[
            {
          id: '12987123',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃、小吃零食',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区真北路',
          shop: '王小虎夫妻店',
          shopId: '10333'
        },
        {
          id: '12987123',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃、小吃零食',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区真北路',
          shop: '王小虎夫妻店',
          shopId: '10333'
        },
        {
          id: '12987123',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃、小吃零食',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区真北路',
          shop: '王小虎夫妻店',
          shopId: '10333'
        },
        {
          id: '12987123',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃、小吃零食',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区真北路',
          shop: '王小虎夫妻店',
          shopId: '10333'
        },
        {
          id: '12987123',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃、小吃零食',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区真北路',
          shop: '王小虎夫妻店',
          shopId: '10333'
        }
          ]
        }, {
          id: '12987122',
          name: '应急部',
          category: '江浙小吃、小吃零食',
          desc: '西城区荷兰优质淡奶奶香浓而不腻',
          address: '保障组\n通讯组\n新闻组',
          shop: '王小虎夫妻店',
          shopId: '10333',
        }, {
          id: '12987122',
          name: '应急部',
          category: '江浙小吃、小吃零食',
          desc: '西城区荷兰优质淡奶奶香浓而不腻',
          address: '保障组\n通讯组\n新闻组',
          shop: '王小虎夫妻店',
          shopId: '10333',
        }, {
          id: '12987122',
          name: '应急部',
          category: '江浙小吃、小吃零食',
          desc: '西城区荷兰优质淡奶奶香浓而不腻',
          address: '保障组\n通讯组\n新闻组',
          shop: '王小虎夫妻店',
          shopId: '10333',
        }
      ],
      // hasExpandableRow: this.tableData.some(item => item.id === '12987126'),

      styleType: 1, //1：编辑，2：查看

      mapSourceList: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增现场部门",

      form: {
        name: "",
        sourceList: [],
        sort: "",
        description: ""
      },
      rules: {
        name: [
          {required: true, message: '资源分类名称不能为空', trigger: 'blur'}
        ],
        // inspectionType: [
        //   {required: true, message: '检查方式不能为空', trigger: 'blur'}
        // ],
        // inspectionUnit: [
        //   {required: true, message: '被检查单位不能为空', trigger: 'blur'}
        // ],
        // inspectionJob: [
        //   {required: true, message: '被检查岗位不能为空', trigger: 'blur'}
        // ],
        // inspectionUser: [
        //   {required: true, message: '被检查人员不能为空', trigger: 'blur'}
        // ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  // computed: {
  //   hasExpandableRow(row) {
  //     const isshow = this.tableData.some(row => row.id === '12987126');
  //     debugger
  //     return isshow;
  //   },
  // },
  mounted() {
    // this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    // handleChangeMonth(value) {
    //   this.form.inspectionTime = conversionDateNotSecond(value);
    // },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await systemConfigApi.querySourceList();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.mapSourceList = data;
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionTypeList = await getItemList(infoEventDictionaryType);
      //   this.searchItems[2].options = this.inspectionTypeList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    //查看详情
    getRowDataInfo(row) {
      systemConfigApi.queryResClassInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.form = data;
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.resetFormData();
      this.generalDialogTitle = "新增现场部门";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑现场部门";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看地图资源分类";
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await systemConfigApi.queryResClassPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      // if (row.inspectionTime && row.inspectionTime.length > 0) {
      //   row.startTime = conversionDateNotSecond(row.inspectionTime[0])
      //   row.endTime = conversionDateNotSecond(row.inspectionTime[1])
      //   delete row.inspectionTime
      // }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await systemConfigApi.createResClass(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else if (this.styleType === 2) {
            const res = await systemConfigApi.updateResClass(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            this.dialogVisible = false;
            this.resetFormData();
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.resetFormData();
    },

    //删除
    handleDelete(row) {
      this.$refs.sceneGroupRef.showReportInfoList(row.id, row.meetingName);
      return
      let txt = "此操作将永久删除该条数据, 是否继续?";
      this.$confirm(txt, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        systemConfigApi.deleteResClass({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success("删除成功");
          this.getTableDataList();
        });
      });
    },

    resetFormData() {
      this.form = {
        name: "",
        sourceList: [],
        sort: "",
        description: ""
      };
    },
  },

};
</script>

<style lang="scss" scoped>
.portal-table {
  padding: 20px !important;
}

.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
