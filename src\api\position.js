import request from "@/utils/request";

// 职务管理API
export default {
  // 查询用户职务列表
  async queryPositions(params = {}) {
    return request({
      url: "/ds/positions/queryPositions",
      method: "post",
      data: params,
    });
  },

  // 更新用户职务
  async updatePositions(data = {}) {
    return request({
      url: "/ds/positions/updatePositions",
      method: "post",
      data: data,
    });
  },

  // 删除用户职务
  async deletePositions(data = {}) {
    return request({
      url: "/ds/positions/deletePositions",
      method: "post",
      data: data,
    });
  },

  // 新增用户职务
  async createPositions(data = {}) {
    return request({
      url: "/ds/positions/createPositions",
      method: "post",
      data: data,
    });
  },
};
