import { warningPublishApi, systemManagementApi } from "@/api";
// 全局混入
export default {
    data() {
        return {
            // 短信模版列表
            contactList: [],
            // 分页信息
            pagination: {
                current: 1,
                pageSize: 10,
                total: 0,
            },
            // 搜索条件
            searchParams: {},
            // 加载状态
            loading: false,
        }
    },
    methods: {
        SET_CONTACT_LIST(list) {
            this.contactList = list || [];
        },
        SET_PAGINATION(pagination) {
            this.pagination = { ...this.pagination, ...pagination };
        },
        SET_SEARCH_PARAMS(params) {
            this.searchParams = params;
        },
        SET_LOADING(loading) {
            this.loading = loading;
        },
        // 获取预警接入列表列表
        async getContactList(params = {}) {
            this.SET_LOADING(true)
            try {
                const requestParams = {
                    page: this.pagination.current,
                    count: this.pagination.pageSize,
                    ...this.searchParams,
                    ...params,
                };

                const response = await warningPublishApi.getWarningPublishList(requestParams);
                this.SET_CONTACT_LIST(response.data.items)
                this.SET_PAGINATION({
                    total: response.data.total,
                    current: response.data.page,
                })
                this.SET_LOADING(false)
                return (
                    response?.data || {
                        list: this.contactList,
                        total: this.pagination.total,
                    }
                );
            } catch (error) {
                this.SET_LOADING(false)
                throw error;
            }
        },
        // 搜索
        async seachContactList(params = {}) {
            this.SET_SEARCH_PARAMS(params)
        },
        // 新增预警接入
        async createWarningPublish(params = {}) {
            try {
                const requestParams = {
                    ...params,
                };
                // requestParams.eventType = requestParams.eventType.join(',')
                await warningPublishApi.createWarningPublish(requestParams);

            } catch (error) {

                throw error;
            }
        },
        // 更新预警接入
        async updateWarningPublish(params = {}) {
            try {
                const requestParams = {
                    ...params,
                };
                // requestParams.eventType = requestParams.eventType.join(',')
                await warningPublishApi.updateWarningPublish(requestParams);

            } catch (error) {


            }
        },
        //删除预警接入
        async removeWarningPublish(params = {}) {
            try {
                const requestParams = {
                    id: params.id
                };
                await warningPublishApi.removeWarningPublish(requestParams);

            } catch (error) {

                throw error;
            }
        },
        // 获取预警接入详情
        async queryWarningPublish(params = {}) {
            try {
                const requestParams = {
                    id: params.id
                };
                const response = await warningPublishApi.queryWarningPublish(requestParams);
                const durationMatch = response.data.duration.match(/(\d+)(\D+)/);
                response.data.duration = durationMatch ? durationMatch[1] : 0;
                this.$refs.contactFormRef.durationUnit = durationMatch
                    ? durationMatch[2]
                    : "天";
                this.$refs.contactFormRef.areaListProp = response.data.areaList.map(item => item?.areaId)

                return (
                    {
                        ...response.data
                    }
                );

            } catch (error) {

                throw error;
            }
        },

        // 分页
        async setCurrentChange(params = {}) {
            try {
                this.SET_PAGINATION(params)
            } catch (error) {
                throw error;
            }
        },

        // 查询审批流程记录
        async queryProcess(params = {}) {
            try {
                const requestParams = {
                    id: params.id,
                };
                const response = await warningPublishApi.queryProcess(requestParams);
                return response?.data || []
            } catch (error) {
                throw error;
            }
        },
    },
}




