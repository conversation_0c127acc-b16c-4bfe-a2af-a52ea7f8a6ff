/**
 * IP地址获取工具类
 * 提供多种IP获取方法，优先使用WebRTC，备用API接口
 */

// IP获取服务配置
const IP_SERVICES = {
  backup: "https://qifu-api.baidubce.com/ip/local/geo/v1/district",
};

/**
 * 使用WebRTC获取IP地址
 * @returns {Promise<Object>} 返回包含IP地址的对象
 */
export function getIPWithWebRTC() {
  return new Promise((resolve, reject) => {
    try {
      console.log("尝试使用WebRTC获取IP地址");

      // 免费公共STUN服务
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: "stun:stun.l.google.com:19302" },
          { urls: "stun:stun1.l.google.com:19302" },
          { urls: "stun:stun.qq.com:3478" },
          { urls: "stun:stun.miwifi.com:3478" },
          { urls: "stun:stun.chat.bilibili.com:3478" },
          { urls: "stun:stun.hitv.com:3478" },
        ],
      });

      let localIP = "";

      // 监听ICE候选
      pc.onicecandidate = function (event) {
        if (event.candidate) {
          const candidate = event.candidate.candidate;
          console.log("ICE候选:", candidate);

          // 提取IP地址
          const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
          if (ipMatch && ipMatch[1]) {
            const ip = ipMatch[1];

            // 过滤掉本地IP地址
            if (
              !ip.startsWith("192.168.") &&
              !ip.startsWith("10.") &&
              !ip.startsWith("172.") &&
              !ip.startsWith("127.") &&
              ip !== "0.0.0.0"
            ) {
              localIP = ip;
              console.log("获取到公网IP:", ip);

              const result = {
                ip: ip,
                method: "WebRTC",
                success: true,
              };

              pc.close();
              resolve(result);
            }
          }
        }
      };

      // 创建数据通道触发ICE收集
      pc.createDataChannel("test");

      // 创建offer
      pc.createOffer()
        .then((offer) => {
          pc.setLocalDescription(offer);
        })
        .catch((error) => {
          console.error("创建offer失败:", error);
          reject(new Error(`WebRTC创建offer失败: ${error.message}`));
        });

      // 超时处理
      setTimeout(() => {
        if (!localIP) {
          pc.close();
          const error = new Error("WebRTC获取IP超时");
          console.warn(
            "WebRTC方法超时 - 可能的原因：网络防火墙、STUN服务器不可达、NAT限制"
          );
          reject(error);
        }
      }, 8000);
    } catch (error) {
      console.error("WebRTC方法失败:", error);
      reject(new Error(`WebRTC方法失败: ${error.message}`));
    }
  });
}

/**
 * 使用备用API获取IP地址
 * @returns {Promise<Object>} 返回包含IP地址的对象
 */
export function getIPWithBackupAPI() {
  return new Promise(async (resolve, reject) => {
    try {
      console.log("尝试备用API方法:", IP_SERVICES.backup);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);

      const response = await fetch(IP_SERVICES.backup, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log("备用API获取成功:", data);

      // 解析百度云API返回的数据
      let ipAddress = "";
      if (data.data && data.ip) {
        ipAddress = data.ip;
      }

      if (!ipAddress) {
        throw new Error("API返回数据格式异常");
      }

      const result = {
        ip: ipAddress,
        method: "百度云API",
        success: true,
        details: data.data, // 包含位置信息等详细数据
      };

      resolve(result);
    } catch (error) {
      console.error("备用API方法失败:", error.message);
      reject(new Error(`备用API方法失败: ${error.message}`));
    }
  });
}

/**
 * 备用JavaScript方法：使用简单的网络检测
 * @returns {Promise<Object>} 返回包含IP地址的对象
 */
export function getIPWithJavaScriptFallback() {
  return new Promise(async (resolve, reject) => {
    try {
      console.log("尝试JavaScript备用方法");

      // 通过创建假的WebSocket连接获取本地IP
      const pc = new RTCPeerConnection();
      pc.createDataChannel("");

      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      const localIP = offer.sdp.match(/c=IN IP4 ([^\s]+)/);
      pc.close();

      if (localIP && localIP[1] && !localIP[1].startsWith("0.0.0.0")) {
        const result = {
          ip: localIP[1],
          method: "JavaScript备用方法",
          success: true,
        };
        resolve(result);
      } else {
        throw new Error("无法通过备用方法获取IP");
      }
    } catch (error) {
      console.warn("JavaScript备用方法也失败:", error.message);
      reject(new Error(`JavaScript备用方法失败: ${error.message}`));
    }
  });
}

/**
 * 智能获取IP地址（主要方法失败时自动切换到备用方法）
 * @returns {Promise<Object>} 返回包含IP地址的对象
 */
export function getIPWithFallback() {
  return new Promise(async (resolve, reject) => {
    try {
      // 先尝试WebRTC方法
      const result = await getIPWithWebRTC();
      resolve(result);
    } catch (webrtcError) {
      console.log("WebRTC方法失败，尝试JavaScript备用方法");

      try {
        const result = await getIPWithJavaScriptFallback();
        resolve(result);
      } catch (jsError) {
        console.log("JavaScript方法失败，尝试备用API");

        try {
          const result = await getIPWithBackupAPI();
          resolve(result);
        } catch (apiError) {
          console.error("所有IP获取方法都失败了");
          reject(
            new Error("所有IP获取方法都失败，请检查网络连接或手动输入IP地址")
          );
        }
      }
    }
  });
}

/**
 * 验证IP地址格式
 * @param {string} ip - 要验证的IP地址
 * @returns {boolean} 是否为有效的IPv4地址
 */
export function validateIPAddress(ip) {
  const IP_REGEX =
    /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)$/;
  return IP_REGEX.test(ip);
}

/**
 * 格式化IP获取结果用于显示
 * @param {Object} result - IP获取结果
 * @returns {string} 格式化后的显示文本
 */
export function formatIPResult(result) {
  if (!result || !result.success) {
    return "IP获取失败";
  }

  let text = `${result.ip} (${result.method})`;

  if (result.details) {
    const { country, prov, city, district, isp } = result.details;
    if (country || prov || city) {
      text += ` - ${country || ""}${prov || ""}${city || ""}${district || ""}`;
    }
    if (isp) {
      text += ` [${isp}]`;
    }
  }

  return text;
}
