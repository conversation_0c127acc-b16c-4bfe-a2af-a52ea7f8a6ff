const state = {
  // 用户列表
  userList: [],
  // 组织架构树
  orgTree: [],
  // 分页信息
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  // 搜索条件
  searchParams: {},
  // 当前选中的组织
  selectedOrg: null,
  // 加载状态
  loading: false,
};

const mutations = {
  SET_USER_LIST: (state, list) => {
    state.userList = list || [];
  },
  SET_ORG_TREE: (state, tree) => {
    state.orgTree = tree;
  },
  SET_PAGINATION: (state, pagination) => {
    state.pagination = { ...state.pagination, ...pagination };
  },
  SET_SEARCH_PARAMS: (state, params) => {
    state.searchParams = params;
  },
  SET_SELECTED_ORG: (state, org) => {
    state.selectedOrg = org;
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading;
  },
};

import { userApi, orgApi } from "@/api";

const actions = {
  // 获取用户列表
  async getUserList({ commit, state }, params = {}) {
    console.log("Store: 开始获取用户列表", {
      currentPage: state.pagination.current,
      pageSize: state.pagination.pageSize,
      searchParams: state.searchParams,
      additionalParams: params,
    });

    commit("SET_LOADING", true);
    try {
      const requestParams = {
        page: state.pagination.current,
        pageSize: state.pagination.pageSize,
        ...state.searchParams,
        ...params,
      };

      const response = await userApi.getList(requestParams);
      commit("SET_USER_LIST", response.data.list);
      commit("SET_PAGINATION", {
        total: response.data.total,
        current: response.data.current,
      });
      commit("SET_LOADING", false);
      return (
        response?.data || {
          list: state.userList,
          total: state.pagination.total,
        }
      );
    } catch (error) {
      commit("SET_LOADING", false);
      throw error;
    }
  },

  // 获取组织架构树
  async getOrgTree({ commit }) {
    try {
      const response = await orgApi.queryOrgTree();
      commit("SET_ORG_TREE", response.data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // 搜索用户
  async searchUsers({ commit }, params) {
    commit("SET_SEARCH_PARAMS", params);
    commit("SET_LOADING", true);
    try {
      const response = await userApi.search(params.keyword, params);
      commit("SET_USER_LIST", response.data.list || response.data);
      commit("SET_PAGINATION", {
        total: response.data.total || response.data.length,
      });
      commit("SET_LOADING", false);
    } catch (error) {
      commit("SET_LOADING", false);
      throw error;
    }
  },

  // 创建用户
  async createUser({ dispatch }, userData) {
    try {
      const response = await userApi.create(userData);
      // 创建成功后刷新列表
      await dispatch("getUserList");
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 更新用户
  async updateUser({ dispatch }, { id, userData }) {
    try {
      const response = await userApi.update(id, userData);
      // 更新成功后刷新列表
      await dispatch("getUserList");
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 删除用户
  async deleteUser({ dispatch }, id) {
    try {
      const response = await userApi.delete(id);
      // 删除成功后刷新列表
      await dispatch("getUserList");
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 批量删除用户
  async batchDeleteUsers({ dispatch }, ids) {
    try {
      const response = await userApi.batchDelete(ids);
      // 删除成功后刷新列表
      await dispatch("getUserList");
      return response;
    } catch (error) {
      throw error;
    }
  },
};

const getters = {};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
