// 会议管理相关api
import request from "@/utils/request";
export default class systemConfig {

  /**
   * --------------------------系统配置-------------------------------
   */

  // 摄像头相关---查询摄像头详情
  static queryCameraDetail(data) {
    return request({
        url: "/ds/camera/queryCameraDetail",
        method: "post",
        data ,
    });
  }

  // 摄像头相关---查询摄像头列表
  static queryCameraList(data) {
    return request({
        url: "/ds/camera/queryCameraList",
        method: "post",
        data ,
    });
  }

  // 摄像头相关---查询摄像头分页列表
  static queryCameraPage(data) {
    return request({
        url: "/ds/infoAggregation/queryReportInfoCameraPage",
        method: "post",
        data ,
    });
  }

  // 摄像头相关---查询事件绑定摄像头关联列表
  static queryReportInfoCameraList(data) {
    return request({
        url: "/ds/infoAggregation/queryReportInfoCameraList",
        method: "post",
        data ,
    });
  }

  // 摄像头相关---接报信息事件绑定摄像头
  static bindReportInfoCamera(data) {
    return request({
        url: "/ds/infoAggregation/bindReportInfoCamera",
        method: "post",
        data ,
    });
  }




  // 事件类型---新增事件类型
  static createEmergencyType(data) {
    return request({
        url: "/ds/emergencyType/create",
        method: "post",
        data ,
    });
  }

  // 事件类型---删除事件类型
  static deleteEmergencyType(data) {
    return request({
        url: "/ds/emergencyType/delete",
        method: "post",
        data ,
    });
  }

  // 事件类型---修改事件类型
  static updateEmergencyType(data) {
    return request({
        url: "/ds/emergencyType/update",
        method: "post",
        data ,
    });
  }

  // 事件类型---查询事件类型详情
  static queryEmergencyTypeById(data) {
    return request({
        url: "/ds/emergencyType/queryEmergencyTypeById",
        method: "post",
        data ,
    });
  }

  // 事件类型---查询事件类型列表
  static queryEmergencyTypeList(data) {
    return request({
        url: "/ds/emergencyType/queryEmergencyTypeList",
        method: "post",
        data ,
    });
  }

  // 事件类型---查询已经绑定的周边资源情况
  static queryResClassEventTypeRefInfo(eventTypeId) {
    return request({
        url: "/ds/resClassEventTypeRef/queryResClassEventTypeRefInfo?eventTypeId=" + eventTypeId,
        method: "get",
    });
  }

  // 事件类型---绑定周边资源情况
  static saveMapResource(data) {
    return request({
        url: "/ds/resClassEventTypeRef/save",
        method: "post",
        data ,
    });
  }

  


  // 地图资源分类---新增地图资源分类
  static createResClass(data) {
    return request({
        url: "/ds/resClass/createResClass",
        method: "post",
        data ,
    });
  }

  // 地图资源分类---删除地图资源分类
  static deleteResClass(data) {
    return request({
        url: "/ds/resClass/deleteResClass",
        method: "post",
        data ,
    });
  }

  // 地图资源分类---修改地图资源分类
  static updateResClass(data) {
    return request({
        url: "/ds/resClass/updateResClass",
        method: "post",
        data ,
    });
  }

  // 地图资源分类---查询地图资源分类详情
  static queryResClassInfo(data) {
    return request({
        url: "/ds/resClass/queryResClassInfo",
        method: "post",
        data ,
    });
  }

  // 地图资源分类---查询地图资源分类列表
  static queryResClassPage(data) {
    return request({
        url: "/ds/resClass/queryResClassPage",
        method: "post",
        data ,
    });
  }

  // 地图资源分类---查询地图资源列表
  static querySourceList(data) {
    return request({
        url: "/ds/resClass/querySourceList",
        method: "get",
        data ,
    });
  }

  // 地图资源分类---查询地图资源和分类列表
  static queryClassResList(data) {
    return request({
        url: "/ds/resClass/queryClassResList",
        method: "get",
        data ,
    });
  }


}
