// 重置部分element-ui样式
$sbc: #801d1400;
$qbc: #b0312500;

.el-dialog__header {
  border-bottom: 1px solid #e8eaec;
}

.el-drawer__header {
  color: #000;
  font-size: 18px;
  border-bottom: 1px solid #e8eaec;
  line-height: 50px;
  padding-top: 0;
  margin-bottom: 0;
}

.el-drawer__body {
  padding: 20px;
}

.el-card {
  box-shadow: 0 0 0 !important;
}

.el-loading-spinner .el-loading-text,
.el-loading-spinner i,
.el-pager li.active {
  color: $qbc;
}

.el-form-item__label {
  font-weight: 400 !important;
  color: rgb(43, 41, 41);
  font-size: 16px;
}

.el-checkbox__input {
  &.is-checked,
  &.is-indeterminate {
    // .el-checkbox__inner {
    //   background-color: $qbc;
    //   border-color: $qbc;
    // }
  }

  &.is-focus {
    .el-checkbox__inner {
      border-color: $qbc;
    }
  }

  .el-checkbox__inner:hover {
    border-color: $qbc;
  }
}

.el-input__inner:focus {
  outline: none;
  border-color: $qbc;
}

.el-submenu__title {
  font-size: 16px;
}

.el-menu-item {
  font-size: 16px;
  border-right: solid 1px $qbc;
}

.el-button--primary {
  // border: 1px solid $sbc !important;
  // background: $sbc !important;

  // &:hover {
  //   border: 1px solid $qbc !important;
  //   background: $qbc !important;
  // }

  // &.is-disabled {
  //   background: $qbc !important;
  //   border: 1px solid $qbc !important;
  // }
}

li.el-menu-item.is-active {
  background: $qbc !important;
  border-right: 2px solid $qbc;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  max-height: 80%;
  overflow: auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-table--enable-row-transition .el-table__body td {
  background-color: #f5f7fa;
  color: black;
}

.el-table--border td {
  border-right: 1px solid #b4b5b6;
}

// .el-table th.is-leaf, .el-table td {
//   border-bottom: 1px solid #b4b5b6;
// }

.el-table .cell {
  line-height: 36px;
  font-size: 14px;
}

//按钮样式
.el-button:hover,
.el-button:focus {
  color: var(--themeColor);
  border-color: var(--themeColor);
  background-color: var(--lightGray1);
}

.el-button--text:hover,
.el-button--text:focus {
  color: var(--themeColor);
  background-color: transparent;
  border-color: transparent;
}

.el-button--success,
.el-button--success:hover,
.el-button--success:focus,
.el-button--primary,
.el-button--primary:hover,
.el-button--primary:focus {
  color: var(--white);
  background-color: var(--themeColor);
  border-color: var(--themeColor);
}

.el-button--primary.is-plain {
  color: var(--themeColor);
  background: var(--lightGray1);
  border-color: var(--themeColor) !important;
}

//选项卡样式
.el-tabs__item.is-active,
.el-tabs__item:hover {
  color: var(--themeColor);
}

.el-tabs__active-bar {
  background-color: var(--themeColor);
}

//单选框样式
.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--themeColor);
  background: var(--themeColor);
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--themeColor);
}

//复选框样式
.el-checkbox.is-bordered.is-checked {
  border-color: var(--themeColor);
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: var(--themeColor);
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--themeColor);
  border-color: var(--themeColor);
}

//评分样式
.el-rate__icon {
  color: var(--themeColor);
}

//日期选择器样式
.el-range-editor.is-active,
.el-range-editor.is-active:hover {
  border-color: var(--themeColor);
}

.el-date-table td.start-date span,
.el-date-table td.end-date span {
  background-color: var(--themeColor);
}

.el-date-table td.today span,
.el-date-table td.available:hover {
  color: var(--themeColor);
}

.el-date-table td.in-range div {
  background-color: var(--lightGray1);
}

//日历组件样式
.el-calendar-table td.is-today {
  color: var(--themeColor);
}

.el-calendar-table td.is-selected {
  background-color: var(--lightGray2);
}

.el-calendar-table .el-calendar-day:hover {
  background-color: var(--lightGray1);
}

//el-select-group样式
.el-select-group {
  .el-select-dropdown__item.selected {
    background-color: var(--white) !important;
  }
}
