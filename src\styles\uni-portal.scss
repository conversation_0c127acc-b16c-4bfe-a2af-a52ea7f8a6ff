@import "variables.scss";

:root {
  --blue: #{$blue};
  --light-blue: #{$light-blue};
  --red: #{$red};
  --pink: #{$pink};
  --tiffany: #{$tiffany};
  --panGreen: #{$panGreen};
  --white: #{$white};
  --black: #{$black};
  --themeColor: #{$themeColor}; //主题色
  --greenColor: #{$green}; //主题色 2
  --yellowColor: #{$yellow}; //主题色 3
  --decolorization: #{$decolorization}; //去色值
  //--orangeColor: #{$orange}; //主题色 4
  --lightGray1: #{$lightGray1}; //浅色的背景颜色，会根据主题色自动生成0.1
  --lightGray2: #{$lightGray2}; //浅色的背景颜色，会根据主题色自动生成0.2
  --lightGray3: #{$lightGray3}; //浅色的背景颜色，会根据主题色自动生成0.3
  --lightGray4: #{$lightGray4}; //浅色的背景颜色，会根据主题色自动生成0.4
  --lightGray5: #{$lightGray5}; //浅色的背景颜色，会根据主题色自动生成0.5
  --lightGray6: #{$lightGray6}; //浅色的背景颜色，会根据主题色自动生成0.6
  --lightGray7: #{$lightGray7}; //浅色的背景颜色，会根据主题色自动生成0.7
  --lightGray8: #{$lightGray8}; //浅色的背景颜色，会根据主题色自动生成0.8
  --lightGray9: #{$lightGray9}; //浅色的背景颜色，会根据主题色自动生成0.9
  --personality: #{$personality};
  --personalityBackground: #{$personalityBackground};
}
