<template>
  <div class="org-management-container">
    <!-- 组织机构管理 - Page -->

    <div class="org-management-main">
      <!-- 左侧树形结构 -->
      <div class="left-panel" :class="{ collapsed: isTreeCollapsed }">
        <div class="tree-header">
          <el-button type="text" @click="toggleTree" class="collapse-btn">
            <i
              :class="isTreeCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            ></i>
          </el-button>
        </div>
        <div
          v-show="!isTreeCollapsed"
          class="tree-content"
          v-loading="treeLoading"
          element-loading-text="加载组织架构中..."
          element-loading-spinner="el-icon-loading"
        >
          <TreePanel
            ref="treePanel"
            :tree-data="orgTreeData"
            :tree-props="{ children: 'children', label: 'orgName' }"
            :show-search="true"
            :show-actions="false"
            :expand-all="true"
            @node-click="handleOrgClick"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
       <!-- <MultiLevelTable 
        ref="multiLevelTableRef"
        :tableType="2"
        :columns="columns"
        :tableData="tableData"
        :otherTableData="otherTableData"
      /> -->

      <SchedulingManagement
        ref="schedulingManagementRef"
        :tableScene="2"
      />

      <!-- <portal-table
        style="width: 100%"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        :loading="loading"
        row-key="id"
        :selection="true"
        :show-dropdown="true"
        @search="handleSearch"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @handle-selection-change="handleSelectionChange"
      /> -->

    </div>
  </div>
</template>

<script>
import TreePanel from "@/components/TreePanel.vue";
import PortalTable from "@/components/PortalTable/index.vue";

import { dutyManagementApi, orgApi } from '@/api/index'
import MultiLevelTable from "@/components/MultiHeader.vue";

import SchedulingManagement from "../SchedulingManagement";
import { auth } from "@/utils";


export default {
  name: "OrganizationManagement",
  components: {
    TreePanel,
    // PortalTable,
    MultiLevelTable,
    SchedulingManagement
  },
  data() {
    return {
      tableData: [],
      otherTableData: [],
      currentClickDept: {},
      orgTreeData: [],
      loading: false,
      treeLoading: false,
      searchKeyword: "",
      selectedRows: [], // 选中的行数据

      columns: [
        {text: true, prop: 'datAndWeek', label: '日期'},
        {text: true, prop: 'leader', label: '带班领导'},
        {text: true, prop: 'phoneNum', label: '手机号码'},
        {text: true, prop: 'operator', label: '值班人员'}
      ],

      dialogVisible: false,
      dialogTitle: "新增组织机构",
      currentEditData: null,
      selectedOrgId: null,
      isTreeCollapsed: false,

      // 导入相关
      importDialogVisible: false,
    };
  },
  computed: {},
  mounted() {
    this.registerHandlers();
    this.initData();
  },

  methods: {
    // 注册事件处理器
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.handleExportFile,
      });
    },

    // 导出表格
    handleExportFile() {
      // exportDutyInfo
      this.$refs.schedulingManagementRef.handleExportFile();
    },

    // 初始化数据
    async initData() {
      try {
        await this.loadOrgTree();
        // await this.loadOrgList();
      } catch (error) {
        console.error("组织机构管理页面初始化失败:", error);
        this.$message.error("页面初始化失败，请刷新重试");
      }
    },

    // 加载组织机构树 - 使用真实API数据
    async loadOrgTree() {
      try {
        this.treeLoading = true;

        // 根据API文档，树形接口参数
        const params = {
          id: "",
          orgName: "",
          orderRule: 0,
          parentId: "",
        };

        const response = await orgApi.queryOrgTree(params);

        if (response && response.code === 0) {
          this.orgTreeData = response.data || [];
          // 默认选中第一个节点
          this.setDefaultSelectedNode();
        } else {
          this.$message.error(response?.message || "加载组织架构失败");
          // 如果API失败，使用备用模拟数据
          this.loadFallbackTreeData();
        }
      } catch (error) {
        this.$message.error("加载组织架构失败，请重试");
        // 如果API失败，使用备用模拟数据
        this.loadFallbackTreeData();
      } finally {
        this.treeLoading = false;
      }
    },

    // 备用模拟数据（当API失败时使用）
    loadFallbackTreeData() {
      // 默认选中第一个节点
      this.setDefaultSelectedNode();
    },

    // 设置默认选中的树节点
    setDefaultSelectedNode() {
      if (this.orgTreeData && this.orgTreeData.length > 0) {
        // 选中第一个根节点
        // const firstNode = this.orgTreeData[0];
        // this.selectedOrgId = firstNode.id;

        // 默认选中当前登录用户的所属机构
        const userInfo = auth.getUserInfo();
        this.selectedOrgId = userInfo.orgId;

        // 在下一个tick中设置树的选中状态
        this.$nextTick(() => {
          this.setTreeCurrentNode(this.selectedOrgId);
        });

        // 加载该节点的列表数据
        // this.loadOrgList();
        
      }
    },

    // 设置树的当前选中节点
    setTreeCurrentNode(nodeId) {
      try {
        if (this.$refs.treePanel && this.$refs.treePanel.$refs.tree) {
          this.$refs.treePanel.$refs.tree.setCurrentKey(nodeId);
        } else {
          console.warn("树组件引用不存在，无法设置当前节点");
        }
      } catch (error) {
        console.error("设置树当前节点失败:", error);
      }
    },

    // 加载组织机构列表 - 使用真实API数据
    async loadOrgList() {
      try {
        this.loading = true;

        // 根据API文档，列表接口参数 - 动态参数
        const params = {
          count: this.pagination.pageSize, // 每页条数
          page: this.pagination.currentPage, // 页数从1开始
          id: this.selectedOrgId || "", // 选中的组织机构ID
          orgName: this.searchKeyword || "", // 组织机构名称搜索关键字
        };

        // const response = await orgApi.queryOrgList(params);

        // if (response && response.code === 0) {
        //   // 检查数据结构并适配
        //   let tableData = [];
        //   let total = 0;

        //   if (response.data) {
        //     // 如果data是数组，直接使用
        //     if (Array.isArray(response.data)) {
        //       tableData = response.data;
        //       total = response.data.length;
        //     }
        //     // 如果data是对象且包含items
        //     else if (response.data.items) {
        //       tableData = response.data.items;
        //       total = response.data.total || response.data.items.length;
        //     }
        //     // 如果data是对象且包含list
        //     else if (response.data.list) {
        //       tableData = response.data.list;
        //       total = response.data.total || response.data.list.length;
        //     }
        //     // 其他情况，尝试直接使用data
        //     else {
        //       tableData = [response.data];
        //       total = 1;
        //     }
        //   }

        //   this.tableData = tableData;
        //   this.pagination.total = total;
        // } else {
        //   this.$message.error(response?.message || "加载组织机构列表失败");
        //   this.loadFallbackListData();
        // }
      } catch (error) {
        this.$message.error("加载组织机构列表失败，请重试");
        // this.loadFallbackListData();
      } finally {
        this.loading = false;
      }
    },

    // // 备用列表模拟数据（当API失败时使用）
    // loadFallbackListData() {
    //   this.otherTableData = [];
    // },

    // 组织架构树节点点击
    handleOrgClick(data) {
      data.orgId = data.id;      
      this.selectedOrgId = data.id;
      // 点击树节点时重新加载列表数据
      // this.loadOrgList();
      // this.currentClickDept.deptId = data.id;
      // this.currentClickDept.deptName = data.orgName;
      this.$nextTick(function () {
        this.$refs.schedulingManagementRef.changeCurrentDeptData(data)
      })
    },

    // // 搜索
    // handleSearch(formData) {
    //   this.searchKeyword = formData.orgName || "";
    //   this.pagination.currentPage = 1; // 搜索时重置到第一页
    //   this.loadOrgList(); // 重新加载数据
    // },

    // // 分页相关方法
    // handleSizeChange(size) {
    //   this.pagination.pageSize = size;
    //   this.pagination.currentPage = 1;
    //   this.loadOrgList(); // 重新加载数据
    // },

    // handleCurrentChange(page) {
    //   this.pagination.currentPage = page;
    //   this.loadOrgList(); // 重新加载数据
    // },

    // 切换树形面板折叠状态
    toggleTree() {
      this.isTreeCollapsed = !this.isTreeCollapsed;
    },

    // 编辑组织机构
    handleEdit(row) {
      this.dialogTitle = "编辑组织机构";
      this.currentEditData = { ...row };
      this.dialogVisible = true;
    },

    // // 选择变化处理
    // handleSelectionChange(selection) {
    //   this.selectedRows = selection;
    // },

    // // 删除组织机构（根据勾选状态决定删除单个还是批量）
    // async handleDelete(row) {
    //   // 如果有勾选的行，则批量删除勾选的行
    //   if (this.selectedRows.length > 0) {
    //     const orgNames = this.selectedRows
    //       .map((item) => item.orgName)
    //       .join("、");
    //     await this.deleteOrganizations(
    //       this.selectedRows,
    //       `确定要删除以下${this.selectedRows.length}个组织机构吗？\n${orgNames}`
    //     );
    //   } else {
    //     // 如果没有勾选，则删除当前点击的行
    //     await this.deleteOrganizations(
    //       [row],
    //       `确定要删除组织机构"${row.orgName}"吗？`
    //     );
    //   }
    // },

    // // 批量删除组织机构
    // async handleBatchDelete() {
    //   if (this.selectedRows.length === 0) {
    //     this.$message.warning("请先选择要删除的组织机构");
    //     return;
    //   }

    //   const orgNames = this.selectedRows.map((row) => row.orgName).join("、");
    //   await this.deleteOrganizations(
    //     this.selectedRows,
    //     `确定要删除以下${this.selectedRows.length}个组织机构吗？\n${orgNames}`
    //   );
    // },

    // // 删除组织机构的通用方法
    // async deleteOrganizations(rows, confirmMessage) {
    //   try {
    //     // 确认删除
    //     const confirmResult = await this.$confirm(
    //       confirmMessage + "\n此操作不可撤销！",
    //       "删除确认",
    //       {
    //         confirmButtonText: "确定删除",
    //         cancelButtonText: "取消",
    //         type: "warning",
    //       }
    //     );

    //     if (confirmResult !== "confirm") {
    //       return;
    //     }

    //     // 准备删除参数
    //     const params = {
    //       id: rows.map((row) => row.id), // API要求id为数组格式
    //     };

    //     // 调用删除接口
    //     const response = await orgApi.deleteOrg(params);

    //     if (response && response.code === 0) {
    //       this.$message.success(`成功删除${rows.length}个组织机构`);
    //       // 重新加载树数据和列表数据
    //       this.loadOrgTree();
    //       this.loadOrgList();
    //       // 清空选中状态
    //       this.selectedRows = [];
    //     } else {
    //       this.$message.error(response?.message || "删除失败");
    //     }
    //   } catch (error) {
    //     if (error !== "cancel") {
    //       this.$message.error("删除失败，请重试");
    //     }
    //   }
    // },

    // 弹框取消处理
    handleDialogCancel() {
      this.dialogVisible = false;
      this.currentEditData = null;
    },

    // 表单提交成功处理
    handleFormSuccess() {
      this.dialogVisible = false;
      this.currentEditData = null;
      // 重新加载数据
      this.loadOrgTree();
      this.loadOrgList();
      this.$message.success("操作成功");
    },
  },
};
</script>

<style lang="scss" scoped>
.org-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.org-management-main {
  flex: 1;
  display: flex;
  gap: 8px;
  padding: 8px;
}

.left-panel {
  width: 260px;
  flex-shrink: 0;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 40px;
  overflow: hidden;
  max-width: 260px;
}

.left-panel.collapsed {
  width: 40px;
  min-width: 40px;
}

.tree-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--content-bg);
  border: none;
}

.collapse-btn {
  padding: 8px;
  color: #606266;
  font-size: 16px;
}

.collapse-btn:hover {
  color: #409eff;
}

.tree-content {
  flex: 1;
  overflow: auto;
  height: calc(100% - 40px);
  max-height: calc(100vh - 200px); /* 确保不超过视口高度 */
  position: relative; /* 为loading定位 */

  // 自定义loading样式
  .el-loading-spinner {
    .el-loading-text {
      color: #606266;
      font-size: 14px;
      margin-top: 15px;
    }

    .circular {
      width: 36px;
      height: 36px;
      animation: loading-rotate 2s linear infinite;
    }

    .path {
      stroke: var(--themeColor);
      stroke-width: 2;
      stroke-dasharray: 90, 150;
      stroke-dashoffset: 0;
      stroke-linecap: round;
      animation: loading-dash 1.5s ease-in-out infinite;
    }
  }

  @keyframes loading-rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 150;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -124;
    }
  }
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.search-wrapper {
  margin-bottom: 8px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .org-management-main {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    max-width: none;
    height: 200px;
  }

  .left-panel.collapsed {
    height: 40px;
  }
}
</style>
