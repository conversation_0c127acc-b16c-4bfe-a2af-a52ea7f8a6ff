<template>
  <div class="editable-table">
    <!-- 通用可编辑表格组件 - Component -->
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      @cell-click="handleCellClick"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        v-bind="column"
      >
        <template slot-scope="{ row, column: col, $index }">
          <!-- 编辑状态：显示输入框 -->
          <el-input
            v-if="isEditing($index, column.prop)"
            v-model="editValue"
            size="small"
            @blur="handleCellBlur($index, column.prop)"
            @keyup.enter="handleCellBlur($index, column.prop)"
            ref="editInput"
          />
          <!-- 显示状态：可点击编辑 -->
          <div
            v-else
            class="display-cell"
            :class="{ 'editable-cell': isColumnEditable(column) }"
            @click="startEdit($index, column.prop, row[column.prop])"
          >
            {{ formatCellValue(row[column.prop]) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "EditableTable",
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => [],
    },
    // 列配置
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableData: [],
      editingCell: null, // 当前编辑的单元格 {rowIndex, prop}
      editValue: "", // 当前编辑的值
      originalValue: "", // 原始值
    };
  },
  watch: {
    data: {
      handler(newData) {
        this.tableData = JSON.parse(JSON.stringify(newData));
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 判断单元格是否正在编辑
    isEditing(rowIndex, prop) {
      return (
        this.editingCell &&
        this.editingCell.rowIndex === rowIndex &&
        this.editingCell.prop === prop
      );
    },

    // 判断列是否可编辑
    isColumnEditable(column) {
      return column.editable !== false;
    },

    // 开始编辑单元格
    startEdit(rowIndex, prop, value) {
      const columnConfig = this.columns.find((col) => col.prop === prop);

      if (!this.isColumnEditable(columnConfig)) {
        return;
      }

      // 如果已经在编辑其他单元格，先保存
      if (this.editingCell) {
        this.handleCellBlur(this.editingCell.rowIndex, this.editingCell.prop);
      }

      // 开始编辑新单元格
      this.editingCell = { rowIndex, prop };
      this.originalValue = value;
      this.editValue = value;

      // 下一帧聚焦输入框
      this.$nextTick(() => {
        const editInput = this.$refs.editInput;
        if (editInput && editInput.focus) {
          editInput.focus();
        }
      });
    },

    // 处理单元格失焦
    handleCellBlur(rowIndex, prop) {
      if (!this.editingCell) return;

      const newValue = this.editValue;
      const oldValue = this.originalValue;

      // 更新表格数据
      this.$set(this.tableData[rowIndex], prop, newValue);

      // 触发变更事件
      this.$emit("cell-change", rowIndex, prop, newValue, oldValue);
      this.$emit("data-change", this.tableData);

      // 清除编辑状态
      this.editingCell = null;
      this.editValue = "";
      this.originalValue = "";
    },

    // 格式化单元格显示值
    formatCellValue(value) {
      if (value === null || value === undefined || value === "") {
        return "-";
      }
      return value;
    },

    // 获取表格数据
    getTableData() {
      return this.tableData;
    },

    // 重置表格数据
    resetTableData() {
      this.tableData = JSON.parse(JSON.stringify(this.data));
      this.editingCell = null;
      this.editValue = "";
      this.originalValue = "";
    },
  },
};
</script>

<style scoped lang="scss">
.editable-table {
  .display-cell {
    min-height: 20px;
    padding: 4px 0;

    &.editable-cell {
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
        border-radius: 2px;
      }
    }
  }

  .edit-cell {
    .el-input,
    .el-input-number,
    .el-date-picker,
    .el-select {
      width: 100%;
    }
  }
}

// 表格样式优化
::v-deep .el-table {
  .el-table__cell {
    padding: 8px 0;
  }
}
</style>
