<template>
  <div class="duty-evaluation-statistics-container">
    <el-row :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="bot_con">
            <div class="con_tit" style="display: flex;justify-content: space-between;">
              <span>装备类型</span>
              <div>
                <el-select v-model="levelId" placeholder="请选择" size="mini" style="width: 170px" @change="(value) => handleChange(value, 8)">
                  <el-option label="全部" :value="''"></el-option>  
                  <el-option v-for="data in equipmentLevelList" :label="data.label" :value="data.value"></el-option>
                </el-select>
                <el-select v-model="equipmentType" placeholder="请选择" size="mini" style="width: 170px; margin-left: 10px;" @change="changeType">
                  <el-option v-for="data in equipmentTypeList" :label="data.label" :value="data.value"></el-option>
                </el-select>
              </div>
                
            </div>
            <el-divider></el-divider>
            <div class="chart-container">
              <echartsOne ref="echartsOne" :chartsOneList="chartsOneList"></echartsOne>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="bot_con" >
            <div class="con_tit" style="display: flex;justify-content: space-between;">
              <span>装备分布</span>
              <el-select v-model="equipmentDistributionLevel" placeholder="请选择" size="mini" style="width: 150px;" @change="(value) => handleChange(value, 3)">
                <el-option label="全部" :value="''"></el-option>  
                <el-option v-for="data in equipmentLevelList" :label="data.label" :value="data.value"></el-option>
              </el-select>
            </div>
            <el-divider></el-divider>
            <div class="chart-container">
              <div class="lf_box">
                <el-table :data="chartsTwoList" height="200px">
                  <el-table-column align="center" prop="item_name" label="地区名称">
                  </el-table-column>
                  <el-table-column align="center" prop="groupCount" label="数量(支)">
                  </el-table-column>
                </el-table>
              </div>
              <echartsTwo ref="echartsTwo" :chartsTwoList="chartsTwoList"></echartsTwo>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;" :gutter="30">
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="bot_con">
            <div class="con_tit">装备状态</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <echartsThree ref="echartsThree" :chartsThreeList="chartsThreeList"></echartsThree>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="bot_con">
            <div class="con_tit" style="display: flex;justify-content: space-between;">
              <span>应用领域</span>
              <el-select v-model="equipmentLevel" placeholder="请选择" size="mini" style="width: 150px;" @change="(value) => handleChange(value, 5)">
                <el-option label="全部" :value="''"></el-option>  
                <el-option v-for="data in equipmentLevelList" :label="data.label" :value="data.value"></el-option>
              </el-select>
            </div>
            <el-divider></el-divider>
            <div class="chart-container">
              <echartsFour ref="echartsFour" :chartsFourList="chartsFourList"></echartsFour>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

  </div>
    
  </template>
  
  <script>
  import $apis from "@/api/index";
  import echartsOne from "./components/echartsOne.vue";
  import echartsTwo from "./components/echartsTwo.vue";
  import echartsThree from "./components/echartsThree.vue";
  import echartsFour from "./components/echartsFour.vue";
import EchartsTwo from "./components/echartsTwo.vue";
  export default {
    components: {
        echartsOne,
        echartsTwo,
        echartsThree,
        echartsFour,
    },
    data() {
      return {
        equipmentList:[],
        chartsOneList:[],
        chartsTwoList:[],
        chartsThreeList:[],
        chartsFourList:[],
        cpNum:0,
        ftNum:0,
        cyNum:0,
        tzNum:0,
        equipmentType:'2025212309',
        levelId:'',
        equipmentLevel:'',
        equipmentTypeList: [],
        equipmentDistributionLevel:'',
        equipmentLevelList:[],
      };
    },
    mounted() {
        this.getEquipmentTypeList();
        this.getLavelList()
        this.getList()
        this.getChartsOne()
        this.getChartsTow()
        this.getChartsThree()
    },
    computed: {
    },
    methods: {
        handleChange(e, n) {
            if (n == 3) {
                this.getChartsTow()
            } else if (n == 8) {
                this.getChartsOne()
            } else if (n == 5) {
                this.getList()
            }
        },
        async getLavelList() {
            this.loading = true;
            const dictionaryValue = "team_level";
            const params = {
                dictionaryValue,
            };
            // const res = await $apis.teamMemberApi.getLevelList(params);
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "id": "202501172221",
            "systemDictionaryId": "202501172212",
            "itemValue": "1",
            "itemName": "国家专业应急救援队伍",
            "itemDesc": "国家专业应急救援队伍",
            "itemSort": 0,
            "isDel": "0",
            "createTime": "2025-01-17 22:21:09",
            "createId": "1",
            "updateTime": "2025-01-17 22:21:12",
            "updateId": "1",
            "systemDictionaryItemId": "202501172221"
        },
        {
            "id": "202501172222",
            "systemDictionaryId": "202501172212",
            "itemValue": "2",
            "itemName": "市级专业应急救援队伍",
            "itemDesc": "市级专业应急救援队伍",
            "itemSort": 1,
            "isDel": "0",
            "createTime": "2025-01-17 22:22:33",
            "createId": "1",
            "updateTime": "2025-01-17 22:22:35",
            "updateId": "1",
            "systemDictionaryItemId": "202501172222"
        },
        {
            "id": "202501172224",
            "systemDictionaryId": "202501172212",
            "itemValue": "4",
            "itemName": "区级专业应急救援队伍",
            "itemDesc": "区级专业应急救援队伍",
            "itemSort": 3,
            "isDel": "0",
            "createTime": "2025-01-17 22:23:52",
            "createId": "1",
            "updateTime": "2025-01-17 22:23:54",
            "updateId": "1",
            "systemDictionaryItemId": "202501172224"
        },
        {
            "id": "2025011722241",
            "systemDictionaryId": "202501172212",
            "itemValue": "5",
            "itemName": "基层救援队伍",
            "itemDesc": "基层救援队伍",
            "itemSort": 4,
            "isDel": "0",
            "createTime": "2025-01-17 22:24:39",
            "createId": "1",
            "updateTime": "2025-01-17 22:24:42",
            "updateId": "1",
            "systemDictionaryItemId": "2025011722241"
        },
        {
            "id": "202501172223",
            "systemDictionaryId": "202501172212",
            "itemValue": "3",
            "itemName": "社会应急力量",
            "itemDesc": "社会应急力量",
            "itemSort": 5,
            "isDel": "0",
            "createTime": "2025-01-17 22:23:25",
            "createId": "1",
            "updateTime": "2025-01-17 22:23:28",
            "updateId": "1",
            "systemDictionaryItemId": "202501172223"
        }
    ]
}
            const { code, data, error } = res;
            if (code === 0) {
            data.forEach(e => {
                this.equipmentLevelList.push({ value: e.id, label: e.itemName });
            });
            } else {
            this.$message.error(error);
            }
        },
        async getList() {
            const params = {
                id:this.equipmentLevel,
            }
            // const res = await $apis.teamFxApi.equipmentAreaStatistics(params);
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "groupCount": 8038,
            "item_name": "防汛救援",
            "item_sort": 999,
            "id": "2025213911"
        },
        {
            "groupCount": 6843,
            "item_name": "森林灭火",
            "item_sort": 999,
            "id": "2025213907"
        },
        {
            "groupCount": 2527,
            "item_name": "救助保障",
            "item_sort": 999,
            "id": "2025213902"
        },
        {
            "groupCount": 499,
            "item_name": "水域救援",
            "item_sort": 999,
            "id": "2025213916"
        },
        {
            "groupCount": 445,
            "item_name": "通信保障",
            "item_sort": 999,
            "id": "2025213901"
        },
        {
            "groupCount": 371,
            "item_name": "城市保障",
            "item_sort": 999,
            "id": "2025213903"
        },
        {
            "groupCount": 1260,
            "item_name": "矿山救援",
            "item_sort": 999,
            "id": "2025213915"
        },
        {
            "groupCount": 4080,
            "item_name": "交通救援",
            "item_sort": 999,
            "id": "2025213909"
        },
        {
            "groupCount": 5100,
            "item_name": "山地搜救",
            "item_sort": 999,
            "id": "2025213905"
        },
        {
            "groupCount": 7100,
            "item_name": "医疗救护",
            "item_sort": 999,
            "id": "2025213904"
        },
        {
            "groupCount": 5600,
            "item_name": "建筑救援",
            "item_sort": 999,
            "id": "2025213913"
        },
        {
            "groupCount": 3700,
            "item_name": "危化救援（环境应急）",
            "item_sort": 999,
            "id": "2025213912"
        },
        {
            "groupCount": 9600,
            "item_name": "有限空间救援",
            "item_sort": 999,
            "id": "2025213917"
        },
        {
            "groupCount": 2200,
            "item_name": "空中侦测",
            "item_sort": 999,
            "id": "2025213908"
        },
        {
            "groupCount": 5001,
            "item_name": "建筑物倒塌搜救",
            "item_sort": 999,
            "id": "2025213906"
        },
        {
            "groupCount": 100,
            "item_name": "地震、地质灾害救援",
            "item_sort": 999,
            "id": "2025213914"
        }
    ]
}
            const { code, data, error } = res;
            
            if (code === 0) {
                data.map((item) => {
                    if (item.item_name.indexOf('（') !== -1) {
                        item.item_name = item.item_name.split('（')[0]
                    }
                    if (item.item_name.indexOf('、') !== -1) {
                        item.item_name = '地震地质'
                    }
                    if (item.item_name == '有限空间救援') {
                        item.item_name = '有限空间'
                    }
                    if (item.item_name == '建筑物倒塌搜救') {
                        item.item_name = '建筑搜救'
                    }
                })
                this.equipmentList = data
                this.chartsFourList = data
                this.$nextTick(() => {
                    this.$refs.echartsFour.initChart()
                })
            } else {
            this.$message.error(error);
            }
        },
        async getEquipmentTypeList() {
            const dictionaryValue = "equipment_type";
            const params = {
                dictionaryValue,
            };
            // const res = await $apis.teamMemberApi.getLevelList(params);
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "id": "202501241648",
            "systemDictionaryId": "202501172216",
            "itemValue": "牵引设备",
            "itemName": "牵引设备",
            "itemDesc": "牵引设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-24 16:52:12",
            "createId": "1",
            "updateTime": "2025-01-24 16:52:12",
            "updateId": "1",
            "systemDictionaryItemId": "202501241648"
        },
        {
            "id": "2025212301",
            "systemDictionaryId": "202501172216",
            "itemValue": "侦察机器人",
            "itemName": "侦察机器人",
            "itemDesc": "侦察机器人",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212301"
        },
        {
            "id": "2025212302",
            "systemDictionaryId": "202501172216",
            "itemValue": "个人护具（搜救）",
            "itemName": "个人护具（搜救）",
            "itemDesc": "个人护具（搜救）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212302"
        },
        {
            "id": "2025212303",
            "systemDictionaryId": "202501172216",
            "itemValue": "保障车辆",
            "itemName": "保障车辆",
            "itemDesc": "保障车辆",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212303"
        },
        {
            "id": "2025212304",
            "systemDictionaryId": "202501172216",
            "itemValue": "工程车",
            "itemName": "工程车",
            "itemDesc": "工程车",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212304"
        },
        {
            "id": "2025212305",
            "systemDictionaryId": "202501172216",
            "itemValue": "清障车",
            "itemName": "清障车",
            "itemDesc": "清障车",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212305"
        },
        {
            "id": "2025212306",
            "systemDictionaryId": "202501172216",
            "itemValue": "绳索救援套装",
            "itemName": "绳索救援套装",
            "itemDesc": "绳索救援套装",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212306"
        },
        {
            "id": "2025212307",
            "systemDictionaryId": "202501172216",
            "itemValue": "个人护具（危化）",
            "itemName": "个人护具（危化）",
            "itemDesc": "个人护具（危化）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212307"
        },
        {
            "id": "2025212308",
            "systemDictionaryId": "202501172216",
            "itemValue": "个人护具（森林灭火）",
            "itemName": "个人护具（森林灭火）",
            "itemDesc": "个人护具（森林灭火）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212308"
        },
        {
            "id": "2025212309",
            "systemDictionaryId": "202501172216",
            "itemValue": "个人护具（水域）",
            "itemName": "个人护具（水域）",
            "itemDesc": "个人护具（水域）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212309"
        },
        {
            "id": "2025212310",
            "systemDictionaryId": "202501172216",
            "itemValue": "钻孔设备",
            "itemName": "钻孔设备",
            "itemDesc": "钻孔设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212310"
        },
        {
            "id": "2025212311",
            "systemDictionaryId": "202501172216",
            "itemValue": "危化消防设备",
            "itemName": "危化消防设备",
            "itemDesc": "危化消防设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212311"
        },
        {
            "id": "2025212312",
            "systemDictionaryId": "202501172216",
            "itemValue": "森林灭火车辆、器具",
            "itemName": "森林灭火车辆、器具",
            "itemDesc": "森林灭火车辆、器具",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212312"
        },
        {
            "id": "2025212313",
            "systemDictionaryId": "202501172216",
            "itemValue": "水域救生设备",
            "itemName": "水域救生设备",
            "itemDesc": "水域救生设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212313"
        },
        {
            "id": "2025212314",
            "systemDictionaryId": "202501172216",
            "itemValue": "现场急救设备",
            "itemName": "现场急救设备",
            "itemDesc": "现场急救设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212314"
        },
        {
            "id": "2025212315",
            "systemDictionaryId": "202501172216",
            "itemValue": "侦检雷达",
            "itemName": "侦检雷达",
            "itemDesc": "侦检雷达",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212315"
        },
        {
            "id": "2025212316",
            "systemDictionaryId": "202501172216",
            "itemValue": "除雪除冰设备",
            "itemName": "除雪除冰设备",
            "itemDesc": "除雪除冰设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212316"
        },
        {
            "id": "2025212317",
            "systemDictionaryId": "202501172216",
            "itemValue": "无人机",
            "itemName": "无人机",
            "itemDesc": "无人机",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212317"
        },
        {
            "id": "2025212318",
            "systemDictionaryId": "202501172216",
            "itemValue": "轨道探伤设备",
            "itemName": "轨道探伤设备",
            "itemDesc": "轨道探伤设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212318"
        },
        {
            "id": "2025212319",
            "systemDictionaryId": "202501172216",
            "itemValue": "压实设备",
            "itemName": "压实设备",
            "itemDesc": "压实设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212319"
        },
        {
            "id": "2025212320",
            "systemDictionaryId": "202501172216",
            "itemValue": "破碎设备",
            "itemName": "破碎设备",
            "itemDesc": "破碎设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212320"
        },
        {
            "id": "2025212321",
            "systemDictionaryId": "202501172216",
            "itemValue": "指挥通讯设备",
            "itemName": "指挥通讯设备",
            "itemDesc": "指挥通讯设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212321"
        },
        {
            "id": "2025212322",
            "systemDictionaryId": "202501172216",
            "itemValue": "空气压缩设备",
            "itemName": "空气压缩设备",
            "itemDesc": "空气压缩设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212322"
        },
        {
            "id": "2025212323",
            "systemDictionaryId": "202501172216",
            "itemValue": "测距设备",
            "itemName": "测距设备",
            "itemDesc": "测距设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212323"
        },
        {
            "id": "2025212324",
            "systemDictionaryId": "202501172216",
            "itemValue": "水流（水深、水质）监测",
            "itemName": "水流（水深、水质）监测",
            "itemDesc": "水流（水深、水质）监测",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212324"
        },
        {
            "id": "2025212325",
            "systemDictionaryId": "202501172216",
            "itemValue": "测温设备",
            "itemName": "测温设备",
            "itemDesc": "测温设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212325"
        },
        {
            "id": "2025212326",
            "systemDictionaryId": "202501172216",
            "itemValue": "装载设备",
            "itemName": "装载设备",
            "itemDesc": "装载设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212326"
        },
        {
            "id": "2025212327",
            "systemDictionaryId": "202501172216",
            "itemValue": "救援舟艇",
            "itemName": "救援舟艇",
            "itemDesc": "救援舟艇",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212327"
        },
        {
            "id": "2025212328",
            "systemDictionaryId": "202501172216",
            "itemValue": "生命探测设备",
            "itemName": "生命探测设备",
            "itemDesc": "生命探测设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212328"
        },
        {
            "id": "2025212329",
            "systemDictionaryId": "202501172216",
            "itemValue": "支护（顶撑、扩张）设备",
            "itemName": "支护（顶撑、扩张）设备",
            "itemDesc": "支护（顶撑、扩张）设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212329"
        },
        {
            "id": "2025212331",
            "systemDictionaryId": "202501172216",
            "itemValue": "危化品检测设备",
            "itemName": "危化品检测设备",
            "itemDesc": "危化品检测设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212331"
        },
        {
            "id": "2025212332",
            "systemDictionaryId": "202501172216",
            "itemValue": "漏电（漏液）检测设备",
            "itemName": "漏电（漏液）检测设备",
            "itemDesc": "漏电（漏液）检测设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212332"
        },
        {
            "id": "2025212333",
            "systemDictionaryId": "202501172216",
            "itemValue": "深井救援设备",
            "itemName": "深井救援设备",
            "itemDesc": "深井救援设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212333"
        },
        {
            "id": "2025212334",
            "systemDictionaryId": "202501172216",
            "itemValue": "洗消设备",
            "itemName": "洗消设备",
            "itemDesc": "洗消设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212334"
        },
        {
            "id": "2025212335",
            "systemDictionaryId": "202501172216",
            "itemValue": "发电设备（应急电源）",
            "itemName": "发电设备（应急电源）",
            "itemDesc": "发电设备（应急电源）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212335"
        },
        {
            "id": "2025212336",
            "systemDictionaryId": "202501172216",
            "itemValue": "照明设备",
            "itemName": "照明设备",
            "itemDesc": "照明设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212336"
        },
        {
            "id": "2025212337",
            "systemDictionaryId": "202501172216",
            "itemValue": "破拆（切割）设备",
            "itemName": "破拆（切割）设备",
            "itemDesc": "破拆（切割）设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212337"
        },
        {
            "id": "2025212338",
            "systemDictionaryId": "202501172216",
            "itemValue": "排烟设备",
            "itemName": "排烟设备",
            "itemDesc": "排烟设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212338"
        },
        {
            "id": "2025212339",
            "systemDictionaryId": "202501172216",
            "itemValue": "堵漏设备",
            "itemName": "堵漏设备",
            "itemDesc": "堵漏设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212339"
        },
        {
            "id": "2025212340",
            "systemDictionaryId": "202501172216",
            "itemValue": "起重设备",
            "itemName": "起重设备",
            "itemDesc": "起重设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212340"
        },
        {
            "id": "2025212341",
            "systemDictionaryId": "202501172216",
            "itemValue": "排水设备（排水车、泵组）",
            "itemName": "排水设备（排水车、泵组）",
            "itemDesc": "排水设备（排水车、泵组）",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212341"
        },
        {
            "id": "2025212342",
            "systemDictionaryId": "202501172216",
            "itemValue": "挖掘设备",
            "itemName": "挖掘设备",
            "itemDesc": "挖掘设备",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212342"
        },
        {
            "id": "2025212343",
            "systemDictionaryId": "202501172216",
            "itemValue": "帐篷",
            "itemName": "帐篷",
            "itemDesc": "帐篷",
            "itemSort": 999,
            "isDel": "0",
            "createTime": "2025-01-19 21:34:44",
            "createId": "1",
            "updateTime": "2025-01-19 21:34:44",
            "updateId": "1",
            "systemDictionaryItemId": "2025212343"
        }
    ]
}
            const { code, data, error } = res;
            if (code === 0) {
                data.forEach(e => {
                    this.equipmentTypeList.push({ value: e.id, label: e.itemName });
                });
            } else {
                this.$message.error(error);
            }
        },
        changeType(e) {
            this.equipmentType = e
            this.getChartsOne()
        },
        async getChartsOne() {
            
            const params = {
                id:this.equipmentType,
                levelId:this.levelId,
            }
            // const res = await $apis.teamFxApi.equipmentTwoTypeStatistics(params);
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "equipmentName": "SRT体系绳索救援装备",
            "equipmentNum": 8
        },
        {
            "equipmentName": "便携式一体头灯",
            "equipmentNum": 50
        },
        {
            "equipmentName": "单兵外腰带",
            "equipmentNum": 45
        },
        {
            "equipmentName": "单兵装备包",
            "equipmentNum": 45
        },
        {
            "equipmentName": "电动送风长管呼吸器",
            "equipmentNum": 1
        },
        {
            "equipmentName": "反光背心",
            "equipmentNum": 50
        },
        {
            "equipmentName": "防火头盔",
            "equipmentNum": 45
        },
        {
            "equipmentName": "隔绝式正压氧气呼吸器",
            "equipmentNum": 30
        },
        {
            "equipmentName": "护目镜",
            "equipmentNum": 45
        },
        {
            "equipmentName": "紧急逃生器",
            "equipmentNum": 2
        },
        {
            "equipmentName": "救生衣",
            "equipmentNum": 50
        },
        {
            "equipmentName": "救援装备损伤检测系统",
            "equipmentNum": 1
        },
        {
            "equipmentName": "气瓶",
            "equipmentNum": 2
        },
        {
            "equipmentName": "抢险救援服",
            "equipmentNum": 50
        },
        {
            "equipmentName": "头盔",
            "equipmentNum": 20
        },
        {
            "equipmentName": "五点式安全带",
            "equipmentNum": 30
        },
        {
            "equipmentName": "正压式空气呼吸器",
            "equipmentNum": 2
        }
    ]
}
            const { code, data, error } = res;
            if (code === 0) {
            this.chartsOneList = data
            this.$nextTick(() => {
                this.$refs.echartsOne.initChart()
            })
            } else {
                this.$message.error(error);
            }
        },
        async getChartsTow() {
            const params = {
                id:this.equipmentDistributionLevel
            }
            // const res = await $apis.teamFxApi.equipmentLocationDistrictStatistics(params);
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "groupCount": 10289,
            "item_name": "延庆区",
            "item_sort": 15,
            "id": "202501222221"
        },
        {
            "groupCount": 3044,
            "item_name": "丰台区",
            "item_sort": 3,
            "id": "202501222230"
        },
        {
            "groupCount": 2240,
            "item_name": "西城区",
            "item_sort": 1,
            "id": "202501222228"
        },
        {
            "groupCount": 1126,
            "item_name": "顺义区",
            "item_sort": 6,
            "id": "202501222234"
        },
        {
            "groupCount": 581,
            "item_name": "海淀区",
            "item_sort": 5,
            "id": "202501222233"
        },
        {
            "groupCount": 513,
            "item_name": "昌平区",
            "item_sort": 11,
            "id": "202501222225"
        },
        {
            "groupCount": 476,
            "item_name": "朝阳区",
            "item_sort": 2,
            "id": "202501222229"
        },
        {
            "groupCount": 214,
            "item_name": "门头沟区",
            "item_sort": 10,
            "id": "202501222224"
        },
        {
            "groupCount": 199,
            "item_name": "大兴区",
            "item_sort": 8,
            "id": "202501222236"
        },
        {
            "groupCount": 174,
            "item_name": "通州区",
            "item_sort": 7,
            "id": "202501222235"
        },
        {
            "groupCount": 138,
            "item_name": "平谷区",
            "item_sort": 12,
            "id": "202501222226"
        },
        {
            "groupCount": 117,
            "item_name": "河北迁安市",
            "item_sort": 888,
            "id": "202501222237"
        },
        {
            "groupCount": 109,
            "item_name": "石景山区",
            "item_sort": 4,
            "id": "202501222231"
        },
        {
            "groupCount": 27,
            "item_name": "密云区",
            "item_sort": 13,
            "id": "202501222227"
        },
        {
            "groupCount": 9,
            "item_name": "怀柔区",
            "item_sort": 14,
            "id": "202501222222"
        },
        {
            "groupCount": 6,
            "item_name": "房山区",
            "item_sort": 9,
            "id": "202501222223"
        },
        {
            "groupCount": 0,
            "item_name": "东城区",
            "item_sort": 0,
            "id": "202501222220"
        },
        {
            "groupCount": 0,
            "item_name": "其他",
            "item_sort": 999,
            "id": "202502251027"
        }
    ]
}
            const { code, data, error } = res;
            if (code === 0) {
            this.chartsTwoList = data
            this.chartsTwoList.map((item) => {
                if (item.item_name == '昌平区') {
                this.cpNum = item.groupCount
                }
                if (item.item_name == '丰台区') {
                    this.ftNum = item.groupCount
                }
                if (item.item_name == '朝阳区') {
                    this.cyNum = item.groupCount
                }
                if (item.item_name == '通州区') {
                    this.tzNum = item.groupCount
                }
            })
            this.$nextTick(() => {
                this.$refs.echartsTwo.initChart()
            })
            } else {
            this.$message.error(error);
            }
        },
        async getChartsThree() {
            // const res = await $apis.teamFxApi.equipmentReadinessStatusStatistics();
            const res = {
    "code": 0,
    "message": "",
    "data": [
        {
            "newCount": 194,
            "usableCount": 790,
            "scrapCount": 0,
            "item_name": "区级专业应急救援队伍",
            "item_sort": 3,
            "id": "202501172224"
        },
        {
            "newCount": 135,
            "usableCount": 1919,
            "scrapCount": 0,
            "item_name": "基层救援队伍",
            "item_sort": 4,
            "id": "2025011722241"
        },
        {
            "newCount": 39,
            "usableCount": 1256,
            "scrapCount": 0,
            "item_name": "市级专业应急救援队伍",
            "item_sort": 1,
            "id": "202501172222"
        },
        {
            "newCount": 45,
            "usableCount": 93,
            "scrapCount": 0,
            "item_name": "社会应急力量",
            "item_sort": 5,
            "id": "202501172223"
        },
        {
            "newCount": 0,
            "usableCount": 2125,
            "scrapCount": 0,
            "item_name": "国家专业应急救援队伍",
            "item_sort": 0,
            "id": "202501172221"
        }
    ]
}
            const { code, data, error } = res;
            if (code === 0) {
            this.chartsThreeList = data
            this.$nextTick(() => {
                this.$refs.echartsThree.initChart()
            })
            } else {
                this.$message.error(error);
            }
        },
        

    },
  };
  </script>
  
<style lang="scss" scoped>
//   ::v-deep .el-table th {
//     background-color: #0938BD !important;
// }
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;

  .bot_con {
    flex: 1;
    .con_tit {
      height: 35px;
      line-height: 30px;
      // padding-left: 50px;
      font-size: 18px;
      font-weight: bold;
      color: black;
      ::v-deep .el-input__inner {
          color: black;
      }
      ::v-deep .el-select .el-input .el-select__caret {
          color: #00B7EE !important;
      }
    }
  }
  .chart-container {
    height: 260px;
    display: flex;
    gap: 20px;

    flex: 1;
    .lf_box {
      width: 35%;
      height: 100%;
    //   padding: 30px 0;
      // margin-left: 40px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
    }
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 8%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
    
</style>

  