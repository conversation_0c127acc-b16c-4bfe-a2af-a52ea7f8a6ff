<template>
  <general-dialog :dialog-visible="dialogVisible" :general-dialog-title="isEditState ? '编辑短信模版' : '新增短信模版'" dialog-width="800px"
    @cancel="handleClose" @confirm="handleSubmit">
    <!-- class="add-form" -->
    <el-form ref="form" class="add-form" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="20">

        <el-col :span="24">
          <el-form-item label="内容" prop="suffixContent">
            <el-input v-model="form.suffixContent" type="textarea" maxlength="200" :rows="10" resize="none"
              placeholder="请输入内容" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status" v-removeAriaHidden>
              <el-radio label="1">启用</el-radio>
              <el-radio label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
     
      </el-row>

    </el-form>
  </general-dialog>
</template>

<script>
import GeneralDialog from '@/components/GeneralDialog.vue'
export default {
  name: "ContactForm",
  components: {
    GeneralDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        templateContent: "",
        status: "1",

      },
      rules: {
        templateContent: [
          {
            required: true,
            message: '请输入内容',
            trigger: "blur"
          },
        ]
      },
      submitting: false,
    };
  },
  mounted() {
    this.registerHandlers();
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    isEditState: {
      get() {
        return this.isEdit;
      },
      set(val) {
        this.$emit("update:isEdit", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
        this.$nextTick(()=>{
          this.resetForm()
        })
      }
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    addHandler() {
      this.dialogVisible = true;
      this.isEditState = false
    },
    registerHandlers() {
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'add_top',
        handler: this.addHandler
      });
    },
    initForm() {
    },
    resetForm() {
      this.form = {
        templateContent: "",
        status: "1",
      }
      this.$refs['form'].resetFields();
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const formData = { ...this.form };
            if (this.isEdit) {
              formData.id = this.formData.id;
            }
            this.$emit("submit", formData,this.isEdit);
          } finally {
            this.submitting = false;
          }
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },
    handleClose() {
      this.$confirm("确定要关闭吗？未保存的数据将丢失。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.dialogVisible = false;
      })
    },
  },
};
</script>

<style scoped>
.sort {
  width: 100%;
}

/* .dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
} */
</style>
