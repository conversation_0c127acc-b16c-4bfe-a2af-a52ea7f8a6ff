<template>
  <div class="form-section">
    <!-- 其他信息表单组件 - Component -->
    <h4 class="section-title">
      <i class="el-icon-s-order"></i>
      其他信息
    </h4>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="接报方式" prop="reportMethod">
          <el-select
            :value="value.reportMethod"
            placeholder="请选择接报方式"
            clearable
            style="width: 100%"
            @input="handleReportMethodChange"
          >
            <el-option
              v-for="item in reportMethodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="报送单位">
          <el-input
            :value="value.reportUnit"
            placeholder="自动带入登录用户所属单位"
            readonly
            class="readonly-input"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="报送人">
          <el-input
            :value="value.reportPerson"
            placeholder="自动带入登录用户姓名"
            readonly
            class="readonly-input"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "OtherInfoForm",
  props: {
    value: {
      type: Object,
      default: () => ({
        reportMethod: "",
        reportUnit: "",
        reportPerson: "",
      }),
    },
    reportMethodOptions: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleReportMethodChange(value) {
      this.$emit("input", { ...this.value, reportMethod: value });
    },
  },
};
</script>

<style scoped>
.form-section {
  margin-bottom: 30px;
  padding: 0;
  background: #ffffff;
  border: none;
  position: relative;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0;
  color: #0a1629;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.section-title::before {
  display: none;
}

.section-title i {
  color: #4569af;
  font-size: 16px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-select {
  width: 100%;
}

.el-input__inner {
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #ffffff;
}

.el-input__inner:hover {
  border-color: #c0c4cc;
}

.el-input__inner:focus {
  border-color: #4569af;
  box-shadow: none;
  outline: none;
}

.el-select .el-input__inner {
  cursor: pointer;
}

.readonly-input .el-input__inner {
  background-color: #f5f7fa !important;
  color: #909399 !important;
  cursor: not-allowed;
  border-color: #e4e7ed;
}

.readonly-input .el-input__inner:hover {
  border-color: #e4e7ed;
}
</style>
