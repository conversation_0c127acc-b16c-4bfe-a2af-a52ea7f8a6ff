<template>
  <div class="form-section">
    <!-- 基本信息表单组件 - Component -->
    <h4 class="section-title">
      <i class="el-icon-document"></i>
      基本信息
    </h4>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="事件标题" prop="infoTitle">
          <el-input
            :value="value.infoTitle"
            :readonly="readonly"
            placeholder="请输入事件标题"
            maxlength="100"
            clearable
            show-word-limit
            @input="handleInfoTitleChange"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="事发时间" prop="infoTime">
          <el-date-picker
            :value="value.infoTime"
            :readonly="readonly"
            type="datetime"
            placeholder="请选择事发时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
            @input="handleInfoTimeChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="事件地点" prop="locationPath">
          <el-cascader
            ref="locationCascader"
            :value="value.locationPath"
            :options="locationOptions"
            :props="locationCascaderProps"
            :disabled="readonly"
            placeholder="请选择区县/街道"
            clearable
            filterable
            style="width: 100%"
            @change="handleLocationChange"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="事件类型">
          <el-select
            :value="value.infoChildType"
            :disabled="readonly"
            placeholder="请选择事件类型"
            clearable
            style="width: 100%"
            @input="handleEventTypeChange"
          >
            <el-option
              v-for="item in eventTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="经度">
          <el-input
            :value="value.longitude"
            :readonly="readonly"
            placeholder="请输入经度"
            clearable
            @input="handleLongitudeChange"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="纬度">
          <el-input
            :value="value.latitude"
            :readonly="readonly"
            placeholder="请输入纬度"
            clearable
            @input="handleLatitudeChange"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { locationCascaderProps } from "../utils/receiveInfoUtils";

export default {
  name: "BasicInfoForm",
  props: {
    value: {
      type: Object,
      default: () => ({
        infoTitle: "",
        infoTime: "",
        locationPath: [],
        longitude: "",
        latitude: "",
        infoChildType: "",
      }),
    },
    locationOptions: {
      type: Array,
      default: () => [],
    },
    eventTypeOptions: {
      type: Array,
      default: () => [],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      locationCascaderProps,
    };
  },
  watch: {
    locationOptions: {
      handler() {
        // 强制刷新级联选择器
        if (this.$refs.locationCascader) {
          this.$nextTick(() => {
            this.$refs.locationCascader.$forceUpdate();
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleInfoTitleChange(value) {
      this.$emit("input", { ...this.value, infoTitle: value });
    },
    handleInfoTimeChange(value) {
      this.$emit("input", { ...this.value, infoTime: value });
    },
    handleLocationChange(value) {
      this.$emit("input", { ...this.value, locationPath: value });
    },
    handleEventTypeChange(value) {
      this.$emit("input", { ...this.value, infoChildType: value });
    },
    handleLongitudeChange(value) {
      this.$emit("input", { ...this.value, longitude: value });
    },
    handleLatitudeChange(value) {
      this.$emit("input", { ...this.value, latitude: value });
    },
  },
};
</script>

<style scoped>
.form-section {
  margin-bottom: 30px;
  padding: 0;
  background: #ffffff;
  border: none;
  position: relative;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0;
  color: #0a1629;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.section-title::before {
  display: none;
}

.section-title i {
  color: #4569af;
  font-size: 16px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-select {
  width: 100%;
}

.el-input__inner {
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #ffffff;
}

.el-input__inner:hover {
  border-color: #c0c4cc;
}

.el-input__inner:focus {
  border-color: #4569af;
  box-shadow: none;
  outline: none;
}

.el-select .el-input__inner {
  cursor: pointer;
}

.el-cascader {
  width: 100%;
}

.el-cascader .el-input__inner {
  cursor: pointer;
}
</style>
