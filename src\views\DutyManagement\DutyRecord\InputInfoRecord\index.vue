<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="信息标题" prop="infoTitle">
          <el-input 
            :disabled="this.styleType === 3"
            style="width: 400px;" 
            v-model="form.infoTitle" />
        </el-form-item>

        <el-form-item label="信息详情" prop="infoContent">
          <el-input 
            :disabled="this.styleType === 3"
            type="textarea" 
            style="width: 400px;" 
            v-model="form.infoContent" 
            :rows="4" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select :disabled="this.styleType === 2" v-model="form.status" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in recordStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="是否关注" prop="collect">
          <el-switch
            :disabled="this.styleType === 2"
            v-model="form.collect"
            :active-value= 1
            :inactive-value= 0
          ></el-switch>
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, dutyManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond } from "@/utils/publicMethod";

export default {
  name: "InputInfoRecord",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "infoContent",
          label: "信息详情",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "createTime",
          label: "录入时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "status",
          label: "信息状态",
          type: "select",
          placeholder: "请输入",
          width: "120",
          options: [
            { label: "待办", value: 0 },
            { label: "已办", value: 1 },
            { label: "归档", value: 2 },
          ],
        },
      ],
      columns: [
        { prop: "infoTitle", label: "信息标题", text: true },
        { prop: "infoContent", label: "信息详情", text: true },
        { prop: "createTime", label: "录入时间", text: true },
        { prop: "dept", label: "录入单位", text: true },
        { prop: "createName", label: "录入人员", text: true },
        { prop: "status", label: "状态", text: true },
        {
          prop: 'collect',
          label: '是否关注',
          switch: true,
          activeValue: 1,
          inactiveValue: 0
        },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '230px',
          operationList: [
            {
              label: '查看',
              permission: 'dutyRecord:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'dutyRecord:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'dutyRecord:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            }
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      recordStatusList: [
        { label: "待办", value: 0 },
        { label: "已办", value: 1 },
        { label: "归档", value: 2 },
      ],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增录入信息",

      form: {
        infoTitle: "",
        infoContent: "",
        status: "",
        collect: 0
      },
      rules: {
        infoTitle: [
          {required: true, message: '信息标题不能为空', trigger: 'blur'}
        ],
        infoContent: [
          {required: true, message: '信息详情不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '信息状态不能为空', trigger: 'blur'}
        ],
        // collect: [
        //   {required: true, message: '被检查岗位不能为空', trigger: 'blur'}
        // ],
        // inspectionUser: [
        //   {required: true, message: '被检查人员不能为空', trigger: 'blur'}
        // ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.getTableDataList();
    // this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    // handleChangeMonth(value) {
    //   this.form.inspectionTime = conversionDateNotSecond(value);
    // },
    //查询字典类型
    // async queryDictionaryType() {
    //   try {
    //     this.inspectionTypeList = await getItemList(inspectionDictionaryType);
    //     this.searchItems[1].options = this.inspectionTypeList.map((item) => ({
    //       label: item.itemName,
    //       value: item.id
    //     }))
    //   } catch (error) {
    //     this.$message.error(error.message);
    //   }

    //   try {
    //     this.inspectionResultList = await getItemList(inspectionResultType);
    //     this.searchItems[2].options = this.inspectionResultList.map((item) => ({
    //       label: item.itemName,
    //       value: item.id
    //     }))
    //   } catch (error) {
    //     this.$message.error(error.message);
    //   }
    // },

    //查看详情
    getRowDataInfo(row) {
      dutyManagementApi.queryInputInfoById({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.form = data;
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.form = {};
      this.generalDialogTitle = "新增录入信息";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑录入信息";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看录入信息";
    },

    //删除
    handleDelete(row) {
      let txt = '此操作将永久删除该条数据, 是否继续?'
      this.$confirm(txt, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dutyManagementApi.deleteInputInfo({id: row.id}).then((res) => {
          const {code, data, error} = res;
          if (code !== 0) return this.$message.error(res.data.message)
          this.$message.success('删除成功')
          this.getTableDataList()
        })
      })
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryInputInfoPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await dutyManagementApi.createInputInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await dutyManagementApi.updateInputInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },

    //关注/取消关注
    handleSwitch(row) {
      let msg
      if (row.collect === 1) {
        msg = "您确定要将此事件标记为重点关注？";
      } else if (row.collect === 0) {
        msg = "您确定要将此事件取消重点关注？";
      }
      this.$confirm( msg , {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then( () => {
        dutyManagementApi.updateInputInfo({ id: row.id, collect: row.collect }).then((res) => {
          const {code, error} = res;
          if (code === 0) {
            this.$message.success('操作成功');
            this.handSubmitSuccess();
          } else {
            row.collect = row.collect === 1 ? 0 : 1;
            this.$message.error(error);
          }
        })
      })
      .catch(() => {
        row.collect = row.collect === 1 ? 0 : 1;
      })
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
