/**
 * 通讯录相关API接口 - 模拟数据版本
 */

// 联系人模拟数据
const contactData = [
  {
    id: 1,
    name: "张伟",
    phone: "13812345678",
    mobile: "13812345678",
    department: "市应急管理局",
    position: "局长",
    level: "A级",
    remark: "应急管理局局长",
    departmentId: 1,
  },
  {
    id: 2,
    name: "王芳",
    phone: "13887654321",
    mobile: "13887654321",
    department: "市公安局",
    position: "副局长",
    level: "A级",
    remark: "公安局副局长",
    departmentId: 2,
  },
  {
    id: 3,
    name: "李强",
    phone: "13856789012",
    mobile: "13856789012",
    department: "市消防救援支队",
    position: "支队长",
    level: "A级",
    remark: "消防救援支队长",
    departmentId: 3,
  },
  {
    id: 4,
    name: "刘明",
    phone: "13765432109",
    mobile: "13765432109",
    department: "市卫健委",
    position: "主任",
    level: "B级",
    remark: "卫健委主任",
    departmentId: 4,
  },
  {
    id: 5,
    name: "陈红",
    phone: "13654321098",
    mobile: "13654321098",
    department: "市交通局",
    position: "局长",
    level: "A级",
    remark: "交通局局长",
    departmentId: 5,
  },
  // 子部门联系人
  {
    id: 6,
    name: "赵敏",
    phone: "13712345678",
    mobile: "13712345678",
    department: "应急指挥中心",
    position: "值班员",
    level: "C级",
    remark: "应急指挥中心值班员",
    departmentId: 11,
  },
  {
    id: 7,
    name: "孙强",
    phone: "13823456789",
    mobile: "13823456789",
    department: "风险监测科",
    position: "科员",
    level: "C级",
    remark: "风险监测科科员",
    departmentId: 12,
  },
  {
    id: 8,
    name: "周丽",
    phone: "13934567890",
    mobile: "13934567890",
    department: "指挥中心",
    position: "调度员",
    level: "C级",
    remark: "公安局指挥中心调度员",
    departmentId: 21,
  },
  {
    id: 9,
    name: "吴刚",
    phone: "13045678901",
    mobile: "13045678901",
    department: "特勤大队",
    position: "队员",
    level: "C级",
    remark: "消防特勤大队队员",
    departmentId: 31,
  },
  {
    id: 10,
    name: "郑雪",
    phone: "13156789012",
    mobile: "13156789012",
    department: "医政医管科",
    position: "科员",
    level: "C级",
    remark: "卫健委医政医管科科员",
    departmentId: 41,
  },
];

export default {
  async getList(params = {}) {
    await new Promise((resolve) => setTimeout(resolve, 300));
    let data = [...contactData];

    if (params.keyword) {
      data = data.filter(
        (item) =>
          item.name.includes(params.keyword) ||
          item.phone.includes(params.keyword) ||
          item.department.includes(params.keyword)
      );
    }

    if (params.departmentId) {
      // 支持按部门ID过滤，包括子部门
      data = data.filter((item) => {
        // 直接匹配部门ID
        if (item.departmentId === params.departmentId) {
          return true;
        }

        // 如果是子部门，也要包含在内
        // 这里简化处理，实际项目中可能需要更复杂的层级关系处理
        const parentDeptMap = {
          11: 1, // 应急指挥中心 -> 市应急管理局
          12: 1, // 风险监测科 -> 市应急管理局
          21: 2, // 指挥中心 -> 市公安局
          22: 2, // 交警支队 -> 市公安局
          31: 3, // 特勤大队 -> 市消防救援支队
          32: 3, // 战勤保障大队 -> 市消防救援支队
          41: 4, // 医政医管科 -> 市卫健委
          42: 4, // 疾控中心 -> 市卫健委
          51: 5, // 运输管理科 -> 市交通局
          52: 5, // 路政管理科 -> 市交通局
        };

        // 如果选择的是父部门，包含所有子部门的联系人
        return parentDeptMap[item.departmentId] === params.departmentId;
      });
    }

    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;

    const result = {
      data: {
        list: data.slice(start, start + pageSize),
        total: data.length,
        current: page,
        pageSize,
      },
    };

    return result;
  },

  async search(keyword, params = {}) {
    // 搜索功能，复用getList的逻辑
    return this.getList({ keyword, ...params });
  },

  async getByOrganization(orgId, params = {}) {
    // 按组织获取联系人，复用getList的逻辑
    return this.getList({ departmentId: orgId, ...params });
  },
};
