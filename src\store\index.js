import Vue from "vue";
import Vuex from "vuex";
import generalEvent from "./modules/generalEvent/generalEvent";
import contact from "./modules/contact";
import user from "./modules/user";
import userManagement from "./modules/userManagement";
import publicStore from "./modules/publicStore";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    generalEvent,
    user,
    contact,
    userManagement,
    publicStore,
  },
  strict: process.env.NODE_ENV !== "production",
});

export default store;
