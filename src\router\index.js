import Vue from "vue";
import VueRouter from "vue-router";
import routeService from "@/services/routeService";

Vue.use(VueRouter);

// 基础组件懒加载
const Layout = () => import("@/layout/index.vue");
const Home = () => import("@/views/Home.vue");
const ThirdHome = () => import("@/views/ThirdHome/index.vue");
const Login = () => import("@/views/Login/index.vue");

// 刊物信息编辑页面组件
const PublicationEdit = () =>
  import(
    "@/views/InformationSubmit/PublicationInfo/PublicationManagement/Edit/index.vue"
  );

// 基础静态路由（登录、首页、404等）
const baseRoutes = [
  {
    path: "/login",
    name: "Login",
    component: Login,
    meta: {
      title: "登录",
      requiresAuth: false,
    },
  },
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    meta: { requiresAuth: true },
    children: [
      {
        path: "home",
        name: "Home",
        component: Home,
        meta: {
          title: "首页",
          icon: "el-icon-house",
        },
      },
      {
        path: "informationSubmit/publicationInfo/publicationManagement/edit",
        name: "PublicationEdit",
        component: PublicationEdit,
        meta: {
          title: "刊物信息编辑",
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: "/",
    component: Layout,
    redirect: "/thirdHome",
    meta: { requiresAuth: true },
    children: [
      {
        path: "thirdHome",
        name: "ThirdHome",
        component: ThirdHome,
        meta: {
          title: "嵌套页面",
          icon: "el-icon-house",
        },
      },
    ],
  },
  // 处理登录相关的错误路径
  {
    path: "/login/*",
    redirect: "/login",
  },
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes: baseRoutes,
});

// 初始化路由服务
routeService.init(router);

// 解决Vue Router重复导航错误
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => {
    if (err.name !== "NavigationDuplicated") {
      throw err;
    }
  });
};

const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => {
    if (err.name !== "NavigationDuplicated") {
      throw err;
    }
  });
};

// 防止重复调用getUserInfo的标志
let isGettingUserInfo = false;

router.beforeEach(async (to, _from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 应急值守业务系统`;
  }

  const { auth, logoutUtils } = require("@/utils");
  const isLoggedIn = auth.isLoggedIn();

  // console.log(
  //   `路由守卫: ${_from.path || "/"} -> ${
  //     to.path
  //   }, isLoggedIn: ${isLoggedIn}, isGettingUserInfo: ${isGettingUserInfo}`
  // );

  // 检查是否需要认证
  // 对于未匹配的路由（动态路由未加载），除了登录页外都需要认证
  const requiresAuth =
    to.path === "/login"
      ? false
      : to.matched.length > 0
      ? to.matched.some((record) => record.meta.requiresAuth !== false)
      : true; // 未匹配的路由默认需要认证

  // 如果没有token但路由数据还存在，清除所有数据
  if (!isLoggedIn && routeService.isRoutesLoaded()) {
    console.log("检测到token已失效，清除所有用户数据");
    logoutUtils.clearAllUserData();
  }

  if (requiresAuth && !isLoggedIn) {
    // 需要认证但未登录，跳转到登录页
    next({
      path: "/login",
      query: { redirect: to.fullPath },
    });
    return;
  }

  if (to.path === "/login" && isLoggedIn) {
    // 已登录用户访问登录页，需要先验证菜单权限
    if (!routeService.isRoutesLoaded()) {
      // 防止重复调用
      if (isGettingUserInfo) {
        // console.log("正在获取用户信息，等待完成...");
        next(false); // 阻止导航
        return;
      }

      try {
        isGettingUserInfo = true;
        const store = require("@/store").default;
        await store.dispatch("user/getUserInfo");
        // 权限验证通过，跳转到首页
        // console.log("权限验证通过，跳转到首页");
        isGettingUserInfo = false;
        next("/home");
        return;
      } catch (error) {
        console.error("权限验证失败:", error);
        isGettingUserInfo = false;

        // 权限验证失败，清除数据并停留在登录页
        logoutUtils.clearAllUserData();
        next(); // 停留在登录页
        return;
      }
    } else {
      // console.log("路由已加载，跳转到首页");
      next("/home");
      return;
    }
  }

  // 如果已登录但路由未加载，尝试加载用户路由
  if (isLoggedIn && !routeService.isRoutesLoaded()) {
    // 排除不需要动态路由的页面
    const staticPaths = ["/home", "/login"];
    if (!staticPaths.includes(to.path)) {
      // 防止重复调用
      if (isGettingUserInfo) {
        next(false); // 阻止导航
        return;
      }

      try {
        isGettingUserInfo = true;
        const store = require("@/store").default;
        await store.dispatch("user/getUserInfo");
        // console.log("路由加载完成，重新导航到目标路径");
        isGettingUserInfo = false;
        next({ ...to, replace: true });
        return;
      } catch (error) {
        console.error("加载用户路由失败:", error);
        isGettingUserInfo = false;

        // 清除数据并跳转到登录页
        logoutUtils.clearAllUserData();

        // 显示错误提示
        try {
          const { Message } = require("element-ui");
          Message.error({
            message: "登录状态已过期，请重新登录",
            duration: 3000,
            showClose: true,
          });
        } catch (e) {
          console.warn("无法显示消息提示:", e);
        }

        // 立即跳转到登录页
        next({
          path: "/login",
          query: { redirect: to.fullPath },
          replace: true,
        });
        return;
      }
    }
  }

  next();
});

export default router;
