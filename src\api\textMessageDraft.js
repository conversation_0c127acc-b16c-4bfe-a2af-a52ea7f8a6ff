/**
 * 草稿箱相关API接口 - 模拟数据版本
 */
import request from "@/utils/request";

export default {
    // 获取草稿箱列表
    async getTextMessageeDraftList(params = {}) {
        return request({
            url: '/ds/smsTask/queryDraftList',
            method: 'get',
            params: params
        })
    },

    async queryTextMessageeDraftById(params = {}) {
        return request({
            url: '/ds/smsCharactersSuffix/queryById',
            method: 'post',
            data: params
        })
    },
    // 删除草稿箱
    async removeTextMessageeDraft(params = {}) {
        return request({
            url: '/ds/smsCharactersSuffix/deleteById',
            method: 'post',
            data: params
        })
    },
};
