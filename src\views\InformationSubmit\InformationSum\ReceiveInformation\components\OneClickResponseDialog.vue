<!--
  一键响应对话框组件 - Component

  主要功能：
  - 响应级别下拉选择
  - 事件类型多选组件
  - 提交一键响应请求
-->
<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="一键响应"
    :before-close="handleClose"
    :modal="false"
    @close="handleClose"
    custom-class="one-click-response-dialog"
  >
    <el-form
      ref="responseForm"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <!-- 响应级别 -->
      <el-form-item label="响应级别" prop="responseLevel">
        <el-select
          v-model="form.responseLevel"
          placeholder="请选择响应级别"
          style="width: 100%"
        >
          <el-option
            v-for="item in responseLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 事件类型 -->
      <el-form-item label="事件类型" prop="selectedEventTypes">
        <el-tree
          ref="eventTypeTree"
          :data="eventTypeOptions"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :check-strictly="false"
          @check="handleEventTypeCheck"
          style="
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
          "
        />
      </el-form-item>

      <!-- 已选择的事件类型显示 -->
      <el-form-item label="已选择" v-if="selectedEventTypeLabels.length > 0">
        <div class="selected-types">
          <el-tag
            v-for="(label, index) in selectedEventTypeLabels"
            :key="index"
            closable
            @close="removeSelectedType(index)"
            style="margin-right: 8px; margin-bottom: 4px"
          >
            {{ label }}
          </el-tag>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        确认响应
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { systemManagementApi, receiveInformationApi } from "@/api";
import systemConfigApi from "@/api/systemConfig";

export default {
  name: "OneClickResponseDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      submitting: false,
      form: {
        responseLevel: "",
        selectedEventTypes: [],
      },
      responseLevelOptions: [],
      eventTypeOptions: [],
      selectedEventTypeLabels: [],
      treeProps: {
        children: "children",
        label: "name",
      },
      rules: {
        responseLevel: [
          { required: true, message: "请选择响应级别", trigger: "change" },
        ],
        selectedEventTypes: [
          {
            validator: (rule, value, callback) => {
              if (this.form.selectedEventTypes.length === 0) {
                callback(new Error("请至少选择一个事件类型"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData();
      } else {
        this.resetForm();
      }
    },
  },
  methods: {
    async initData() {
      this.loading = true;
      try {
        await Promise.all([this.loadResponseLevels(), this.loadEventTypes()]);
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("加载数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 加载响应级别选项
    async loadResponseLevels() {
      try {
        const response = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507041738",
        });
        if (response.code === 0) {
          this.responseLevelOptions = response.data.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        }
      } catch (error) {
        console.error("加载响应级别失败:", error);
        throw error;
      }
    },

    // 加载事件类型选项
    async loadEventTypes() {
      try {
        const response = await systemConfigApi.queryEmergencyTypeList({});
        if (response.code === 0) {
          this.eventTypeOptions = response.data || [];
        }
      } catch (error) {
        console.error("加载事件类型失败:", error);
        throw error;
      }
    },

    // 处理事件类型选择
    handleEventTypeCheck(data, checkedInfo) {
      const checkedNodes = checkedInfo.checkedNodes;
      this.form.selectedEventTypes = checkedNodes.filter(
        (node) => !node.children || node.children.length === 0
      );
      this.updateSelectedLabels();
    },

    // 更新已选择的标签显示
    updateSelectedLabels() {
      this.selectedEventTypeLabels = this.form.selectedEventTypes.map(
        (node) => {
          const path = this.getNodePath(node);
          return path.map((item) => item.name).join(" > ");
        }
      );
    },

    // 获取节点路径（包含ID和名称）
    getNodePath(node) {
      const path = [];
      let current = node;

      // 构建从当前节点到根节点的路径
      while (current) {
        path.unshift({
          id: current.id,
          name: current.name || current.label,
        });

        // 使用parentId查找父节点
        if (current.parentId) {
          current = this.findNodeById(current.parentId, this.eventTypeOptions);
        } else {
          current = null;
        }
      }

      return path;
    },

    // 根据ID查找节点
    findNodeById(id, nodes) {
      for (const node of nodes) {
        if (node.id === id) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(id, node.children);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },

    // 查找父节点
    findParentNode(targetNode, nodes, parent = null) {
      for (const node of nodes) {
        if (node.id === targetNode.id) {
          return parent;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findParentNode(targetNode, node.children, node);
          if (found !== null) {
            return found;
          }
        }
      }
      return null;
    },

    // 移除已选择的类型
    removeSelectedType(index) {
      const removedNode = this.form.selectedEventTypes[index];
      this.form.selectedEventTypes.splice(index, 1);
      this.$refs.eventTypeTree.setChecked(removedNode.id, false);
      this.updateSelectedLabels();
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.responseForm.validate();

        this.submitting = true;

        const submitData = {
          id: this.detailId,
          responseLevel: this.form.responseLevel,
          infoTypeList: this.form.selectedEventTypes.map((node) => {
            const path = this.getNodePath(node);

            const level1 = path[0] || { id: "" };
            const level2 = path[1] || { id: "" };
            const level3 = path[2] || { id: "" };

            return {
              infoType: level1.id,
              infoChildType: level2.id,
              infoThirdType: level3.id,
            };
          }),
        };

        // const response = await receiveInformationApi.oneTouchResponse(
        //   submitData
        // );

        // if (response.code === 0) {
        //   this.$message.success("一键响应成功");
        //   this.handleClose();
        // 传递响应数据给父组件
        this.$emit("success", {
          responseLevel: this.form.responseLevel,
          infoTypeList: submitData.infoTypeList,
        });
        // 提交成功后关闭对话框
        this.handleClose();
        // } else {
        //   this.$message.error(response.message || "响应失败");
        // }
      } catch (error) {
        if (error.message) {
          // 表单验证错误
          return;
        }
        console.error("一键响应失败:", error);
        this.$message.error("响应失败，请重试");
      } finally {
        this.submitting = false;
      }
    },

    // 重置表单
    resetForm() {
      this.form = {
        responseLevel: "",
        selectedEventTypes: [],
      };
      this.selectedEventTypeLabels = [];
      if (this.$refs.responseForm) {
        this.$refs.responseForm.clearValidate();
      }
      if (this.$refs.eventTypeTree) {
        this.$refs.eventTypeTree.setCheckedKeys([]);
      }
    },

    // 关闭对话框
    handleClose() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.selected-types {
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-tree {
  .el-tree-node__content {
    height: 35px;
    padding: 4px 0;
  }
  .el-tree-node__expand-icon {
    margin-right: 10px;
  }
}

::v-deep .el-dialog {
  height: 500px;
  overflow-y: auto;
}
</style>
