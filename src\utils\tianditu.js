import axios from 'axios';

const TIANDITU_KEY =  '86454d558df582a25322dee83e2a472b';
const baseUrl = 'https://api.tianditu.gov.cn';

// 地理编码
export async function geocodingInterface(keyWord) {
    try {
        const params = {
            ds: JSON.stringify({ keyWord }),
            tk: TIANDITU_KEY
        };

        const response = await axios.get(`${baseUrl}/geocoder`, {
            params
        });
        if (response.status === 200) {
            return response.data;
        } else {
            throw new Error(`请求失败，状态码: ${response.status}`);
        }
    } catch (error) {
      console.log(error);
    }
}