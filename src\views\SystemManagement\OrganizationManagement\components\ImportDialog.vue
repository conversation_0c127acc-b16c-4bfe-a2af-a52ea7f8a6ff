<template>
  <general-dialog
    :dialog-visible="visible"
    :dialog-width="'700px'"
    general-dialog-title="导入组织机构"
    :show-footer="false"
    @cancel="handleCancel"
  >
    <div class="import-dialog-content">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择文件" icon="el-icon-upload"></el-step>
        <el-step title="导入完成" icon="el-icon-check"></el-step>
      </el-steps>

      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            class="upload-component"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            accept=".xlsx,.xls"
            style="width: 100%"
          >
            <i class="el-icon-upload upload-icon"></i>
            <div class="upload-text">
              <p>将Excel文件拖到此处，或<em>点击选择文件</em></p>
              <p class="upload-tip">
                支持 .xlsx、.xls 格式，文件大小不超过10MB
              </p>
            </div>
          </el-upload>
        </div>

        <div v-if="selectedFile" class="file-info">
          <div class="file-item">
            <i class="el-icon-document file-icon"></i>
            <div class="file-details">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-size">
                {{ formatFileSize(selectedFile.size) }}
              </div>
            </div>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="removeFile"
              class="remove-btn"
            >
              移除
            </el-button>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            type="primary"
            :disabled="!selectedFile"
            :loading="importLoading"
            @click="startImport"
          >
            开始导入
          </el-button>
        </div>
      </div>

      <div v-if="currentStep === 1" class="step-content">
        <div class="result-content">
          <div class="result-icon">
            <i
              :class="
                importResult.success ? 'el-icon-success' : 'el-icon-error'
              "
              :style="{ color: importResult.success ? '#67C23A' : '#F56C6C' }"
            ></i>
          </div>
          <div class="result-text">
            <h3>{{ importResult.success ? "导入成功" : "导入失败" }}</h3>
            <p>{{ importResult.message }}</p>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="handleCancel">关闭</el-button>
          <el-button v-if="!importResult.success" @click="resetImport">
            重新导入
          </el-button>
        </div>
      </div>
    </div>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { orgApi } from "@/api/index.js";

export default {
  name: "ImportDialog",
  components: {
    GeneralDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentStep: 0,
      fileList: [],
      selectedFile: null,
      importLoading: false,
      importResult: {
        success: false,
        message: "",
      },
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetDialog();
      }
    },
  },
  methods: {
    resetDialog() {
      this.currentStep = 0;
      this.fileList = [];
      this.selectedFile = null;
      this.importLoading = false;
      this.importResult = {
        success: false,
        message: "",
      };
    },

    handleFileChange(file, fileList) {
      this.fileList = fileList;
      // 统一使用原始文件对象，确保文件流对象的一致性
      if (file && file.raw) {
        this.selectedFile = file.raw;
      } else if (fileList.length === 0) {
        // 当文件列表为空时，清空选中的文件
        this.selectedFile = null;
      }
    },

    beforeUpload(file) {
      const isExcel =
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传Excel文件!");
        this.removeFile();
        return false;
      }
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过10MB!");
        this.removeFile();
        return false;
      }

      // 确保使用原始文件对象作为文件流，与handleFileChange保持一致
      this.selectedFile = file;
      return false;
    },

    removeFile() {
      this.fileList = [];
      this.selectedFile = null;
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.clearFiles();
      }
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + " B";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + " KB";
      } else {
        return (size / (1024 * 1024)).toFixed(1) + " MB";
      }
    },

    async startImport() {
      this.importLoading = true;
      try {
        // 验证文件对象是否为有效的文件流
        if (!this.selectedFile || !(this.selectedFile instanceof File)) {
          throw new Error("无效的文件对象，请重新选择文件");
        }

        const formData = new FormData();
        formData.append("file", this.selectedFile);

        const response = await orgApi.uploadOrg(formData);

        if (response && response.code === 0) {
          this.importResult = {
            success: true,
            message: response?.message || "导入成功",
          };
          this.$emit("success");
        } else {
          this.importResult = {
            success: false,
            message: response?.message || "导入失败，请检查文件格式",
          };
        }
      } catch (error) {
        this.importResult = {
          success: false,
          message: "导入失败: " + (error.message || "网络错误"),
        };
      } finally {
        this.importLoading = false;
        this.currentStep = 1;
      }
    },

    resetImport() {
      this.currentStep = 0;
      this.removeFile();
    },

    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
.import-dialog-content {
  padding: 20px;
}

.import-dialog-content .el-upload,
.import-dialog-content .el-upload-dragger {
  width: 100% !important;
  max-width: none !important;
  min-width: 100% !important;
}

.el-steps {
  margin-bottom: 30px;
}

.step-content {
  min-height: 300px;
}

.upload-area {
  margin-bottom: 20px;
  width: 100%;
}

.upload-component {
  width: 100% !important;
}

.upload-component .el-upload {
  width: 100% !important;
  display: block;
}

.upload-component .el-upload-dragger {
  width: 100% !important;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 0;
  padding: 20px;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

/* 文件信息 */
.file-info {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.file-item {
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.remove-btn {
  color: #f56c6c;
}

.preview-header {
  margin-bottom: 20px;
  text-align: center;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-table {
  margin-bottom: 16px;
}

.preview-summary {
  text-align: center;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.preview-summary p {
  margin: 0;
  color: #606266;
}

.preview-summary span {
  margin: 0 16px;
}

.valid-count {
  color: #67c23a;
}

.invalid-count {
  color: #f56c6c;
}

/* 结果页面 */
.result-content {
  text-align: center;
  padding: 40px 0;
}

.result-icon {
  margin-bottom: 20px;
}

.result-icon i {
  font-size: 64px;
}

.result-text h3 {
  margin: 0 0 12px 0;
  color: #303133;
}

.result-text p {
  margin: 0;
  color: #606266;
}

/* 操作按钮 */
.step-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.step-actions .el-button {
  margin: 0 6px;
}

/* 深度选择器确保样式穿透 */
.import-dialog-content >>> .el-upload {
  width: 100% !important;
}

.import-dialog-content >>> .el-upload-dragger {
  width: 100% !important;
}
</style>
