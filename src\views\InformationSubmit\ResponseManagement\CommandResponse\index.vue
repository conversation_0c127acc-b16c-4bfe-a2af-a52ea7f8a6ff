<template>
  <div
    class="command-response-index"
    :style="{ padding: !showInstruction ? '20px' : 0 }"
  >
    <!-- 指令响应 -->
    <template v-if="!showInstruction">
      <el-row
        v-if="commandResponseList.length > 0"
        :gutter="20"
        :style="{ height: `${rowHeight}px`, overflow: 'auto' }"
      >
        <el-col v-for="item in commandResponseList" :span="6">
          <el-card class="box-card" @click.native="handleClick(item)">
            <div slot="header" class="clearfix">
              <span>{{ item.name }}</span>
            </div>
            <div class="text-list">
              <div class="text">
                {{
                  item.commandCount === 0 || item.commandCount === null
                    ? "无"
                    : item.commandCount + "类指令"
                }}
              </div>
              <div class="text">
                {{
                  item.orgCount === 0 || item.orgCount === null
                    ? "无"
                    : item.orgCount + "机构"
                }}
              </div>
              <el-button
                :class="{
                  'start-plan': !item.startPlan,
                }"
                :type="item.startPlan ? 'warning' : ''"
              >
                <!-- 启动一级预案 -->
                {{
                  item.startPlan === 1
                    ? "启动一级响应"
                    : item.startPlan === 2
                    ? "启动二级响应"
                    : item.startPlan === 3
                    ? "启动三级响应"
                    : item.startPlan === 4
                    ? "启动四级响应"
                    : "无"
                }}
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-empty v-else description="描述文字"></el-empty>

      <Pagination
        :page-sizes="[12]"
        :total="queryParams.total"
        :page-size.sync="queryParams.pageSize"
        :current-page.sync="queryParams.pageNum"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />
    </template>

    <!-- 指令页面 -->
    <instruction-index
      v-if="showInstruction"
      :command-response-data="commandResponseData"
      @handleCancel="handleCancel"
    />
  </div>
</template>

<script>
import InstructionIndex from "@/views/InformationSubmit/ResponseManagement/CommandResponse/components/Instruction/index.vue";
import { commandConfigurationApi } from "@/api";
import Pagination from "@/components/Pagination/index.vue";

export default {
  name: "CommandResponse",
  components: { Pagination, InstructionIndex },
  data() {
    return {
      rowHeight: 695,
      commandResponseList: [],

      showInstruction: false,
      commandResponseData: {},
      emptyDescription: "暂无指令响应数据",

      queryParams: {
        pageNum: 1,
        pageSize: 12,
        total: 0,
      },
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      const { code, data, message, error } =
        await commandConfigurationApi.queryInstructionOnePage({
          page: this.queryParams.pageNum,
          count: this.queryParams.pageSize,
        });
      if (code !== 0) return this.$message.error(message || error);

      this.commandResponseList = data.items;
      this.queryParams.total = data.total;
    },

    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.fetchData();
    },

    handleClick(row) {
      this.showInstruction = true;
      this.commandResponseData = row;
    },

    handleCancel() {
      this.fetchData();
      this.showInstruction = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.command-response-index {
  ::v-deep {
    .el-row {
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;

      .el-col {
        display: flex;

        .box-card {
          width: 100%;
          margin-bottom: 20px;
          display: flex;
          flex-direction: column;
          cursor: pointer;

          .el-card__body {
            flex: 1;
            display: flex;
            padding: 20px;
            height: 100%;

            .start-plan {
              border-width: 0;
            }

            .start-plan.el-button:active,
            .start-plan.el-button:focus,
            .start-plan.el-button:hover {
              color: black;
              border-color: transparent;
              background-color: transparent;
            }
          }
        }
      }
    }
  }

  .box-card {
    .clearfix {
      font-size: 15px;
    }

    .text-list {
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .text {
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 16px;
        color: black;
        text-align: center;
      }
    }
  }
}
</style>
