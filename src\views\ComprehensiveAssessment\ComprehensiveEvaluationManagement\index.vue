<template>
  <div class="duty-evaluation-statistics-container">
    <div v-show="dutyVisible">
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <div class="Statistics">
              <div
                class="Statistics-item"
                v-for="(item, index) in statisticsData"
                :key="item.title"
              >
                <div
                  class="vertical-line"
                  :class="`bg-color-${index % 4}`"
                ></div>
                <div class="content">
                  <div class="image">
                    <el-image
                      :src="
                        require(`@/assets/images/statistics_${index + 1}.png`)
                      "
                    />
                  </div>
                  <div class="content-text">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="data">
                      <div class="number">{{ item.data }}</div>
                    </div>
                    <div
                      class="percentage"
                      :style="{ color: getPercentageColor(item.percentage) }"
                    >
                      {{ item.percentage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">评价完成情况</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <echarts-component :options="completionOptions" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">评价分数分布</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <div class="left">
                <echarts-component :options="typeOptions" />
              </div>
              <div class="right">
                <div class="percentage">
                  <div
                    class="percentage-item"
                    v-for="item in percentageData"
                    :key="item.title"
                  >
                    <div class="percentage-text">
                      <div class="title">{{ item.title }}</div>
                      <div class="number">{{ item.number }}%</div>
                    </div>
                    <el-progress
                      :show-text="false"
                      :stroke-width="10"
                      :percentage="item.number"
                      :color="item.color"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="table-title">最新评价</div>
            <div class="table-container">
              <PortalTable
                :tableHeight="260"
                :columns="columns"
                :showSelection="false"
                :showIndex="false"
                :table-data="tableData"
                row-key="id"
                :pagination="pagination"
                :showAddButton="false"
                @handle-size-change="handleSizeChange"
                @handle-current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="table-title">评价通知</div>
            <div class="evaluation-notice-container">
              <div
                v-for="(notice, index) in evaluationNoticeData"
                :key="index"
                class="notice-item"
              >
                <div class="notice-title">
                  <div>{{ notice.noticeTitle }}</div>
                  <div>{{ notice.sendTime }}</div>
                </div>
                <div class="notice-content">{{ notice.noticeRemark }}</div>
              </div>
              <div class="publish-button">
                <el-button @click="addNotify" icon="el-icon-plus"
                  >发布新通知</el-button
                >
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <PublicDialog ref="publicDialogRef" @dutyShow="dutyShow" />
    <add-dialog ref="addDialogRef" @refreshTableData="refreshTableData" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import echartsComponent from "@/components/echarts.vue";
import PublicDialog from "@/views/ComprehensiveAssessment/ComprehensiveEvaluationManagement/components/publicDialog.vue";
import AddDialog from "@/views/ComprehensiveAssessment/EvaluationNotificationManagement/components/addDialog.vue";

import * as echarts from "echarts/core";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import { number } from "echarts/core";
export default {
  name: "DutyEvaluationStatistics",
  components: {
    PortalTable,
    echartsComponent,
    PublicDialog,
    AddDialog,
  },
  data() {
    return {
      dutyVisible: true,
      // 0:草稿 1:待审核 2:审核驳回 3:审核通过
      statusList: [
        {
          label: "草稿",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "审核驳回",
          value: 2,
        },
        {
          label: "审核通过",
          value: 3,
        },
      ],
      statisticsData: [
        {
          id: 1,
          title: "年度评价方案",
          data: "2025",
          percentage: "已发布",
        },
        {
          id: 2,
          title: "待审核评价",
          data: "10",
          percentage: "等待处理",
        },
        {
          id: 3,
          title: "季度评价报表",
          data: "Q2",
          percentage: "截止到9月30日",
        },
        {
          id: 4,
          title: "评价通知",
          data: "3",
          percentage: "本月已发布",
        },
      ],
      completionOptions: {
        // 修改为两种颜色：绿色(#80FFA5)和灰色(#CCCCCC)
        color: ["#80FFA5", "#CCCCCC"],
        title: {
          text: "", // 清空标题（根据原图无标题）
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        // 修改图例为两个数据系列
        legend: {
          data: ["未完成数据", "已完成数据"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            // 修改为12个月份
            data: [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            // 坐标轴线样式
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#333", // 深色坐标轴
              },
            },
            // 坐标轴标签
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            // 禁用网格线（无网格线）
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          // 已完成数据（灰色系列）
          {
            name: "已完成数据",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#CCCCCC" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [5, 1, 6, 5, 6, 17, 5, 8, 5, 2, 9, 6],
          },
          // 未完成数据（绿色系列）
          {
            name: "未完成数据",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(141, 204, 171, 0.8)" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [0, 5, 15, 20, 45, 20, 10, 8, 12, 15, 10, 0], // 模拟峰值数据
          },
        ],
      },
      typeOptions: {
        tooltip: {
          trigger: "item",
          // formatter: "{b}: {d}%",
          formatter: "{b}:{c}",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542", "#24d2d3"],
        series: [
          {
            name: "评价分布",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 40, name: "系统评价" },
              { value: 35, name: "自评" },
              { value: 25, name: "待审核" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "84.5",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "平均分",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData: [
        {
          title: "系统评价",
          number: 40,
          color: "#fb788a",
        },
        {
          title: "自评",
          number: 35,
          color: "#52c1f5",
        },
        {
          title: "待审核",
          number: 25,
          color: "#ffc542",
        },
      ],
      columns: [
        { text: true, prop: "code", label: "评价编号", width: "130" },
        { text: true, prop: "orgName", label: "部门/地区", width: "180" },
        {
          text: true,
          prop: "totalSelfRatedScore",
          label: "自评分数",
          width: "120",
        },
        {
          text: true,
          prop: "totalSystemScore",
          label: "系统评分",
          width: "120",
        },
        { text: true, prop: "statusFormat", label: "状态", width: "120" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "详情",
              permission: "dutyEvaluationStatistics:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
      evaluationNoticeData: [
        {
          id: 1,
          title: "2025年度应急值守综合评价通知",
          date: "2025-07-01",
          content:
            "请各地区、各部门于2025年12月31日前完成自评工作，并上传佐证材料至应急值守管理系统。逾期未完成的单位将在年度考核中扣分。评价标准详见附件。",
        },
        {
          id: 2,
          title: "关于2025年第三季度应急值守评价的提醒",
          date: "2025-07-15",
          content:
            "第三季度应急值守评价工作即将截止，请尚未提交的单位尽快完成自评并提交相关材料。截止日期为2025年9月30日，逾期将影响年度评分。",
        },
        {
          id: 3,
          title: "2025年第二季度应急值守评价结果通报",
          date: "2025-07-10",
          content:
            "2025年第二季度应急值守评价工作已完成，现将评价结果予以通报。各单位可登录系统查看详细评价报告，并于7个工作日内完成相关整改工作。",
        },
      ],
    };
  },
  methods: {
    queryNoticeList() {
      comprehensiveAssessmentApi.queryNoticeList({}).then((res) => {
        if (res.code === 0) {
          this.evaluationNoticeData = res.data;
        }
      });
    },
    async queryLatestEvaluationPage() {
      const params = {
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
      };
      const res = await comprehensiveAssessmentApi.queryLatestEvaluationPage(
        params
      );
      if (res.code === 0) {
        this.tableData = res.data.items;
        this.tableData.forEach((item) => {
          item.statusFormat = this.statusList.find(
            (status) => status.value === item.status
          )?.label;
        });
        this.pagination.total = res.data.total;
      }
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.queryLatestEvaluationPage();
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.queryLatestEvaluationPage();
    },
    getPercentageColor(percentage) {
      const value = parseFloat(percentage);
      if (value > 1) return "#67C23A";
      if (value < 1) return "#F56C6C";
      return "#999"; // 等于1时的颜色
    },

    handleDetail(row) {
      this.dutyVisible = false;
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "detail";
      this.$refs.publicDialogRef.title = "评价详情";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    onSubmit() {
      console.log("提交");
    },
    dutyShow() {
      console.log("ppppppppp");

      this.dutyVisible = true;
    },
    addNotify() {
      this.$refs.addDialogRef.dialogVisible = true;
      this.$refs.addDialogRef.dialogType = "add";
      this.$refs.addDialogRef.dialogTitle = "发布新通知";
      this.$refs.addDialogRef.resetForm();
    },
    refreshTableData() {
      this.pagination.currentPage = 1;
      this.queryNoticeList();
    },
    // 评价完成情况
    queryCompletionStatus() {
      comprehensiveAssessmentApi.queryCompletionStatus({}).then((res) => {
        if (res.code === 0) {
          this.completionOptions.xAxis[0].data = res.data?.monData;
          this.completionOptions.series[0].data = res.data?.completedData;
          this.completionOptions.series[1].data = res.data?.incompleteData;
        }
      });
    },
    // 评价分数分布
    queryDistributionScores() {
      comprehensiveAssessmentApi.queryDistributionScores({}).then((res) => {
        if (res.code === 0) {
          this.typeOptions.series[0].data = res.data?.scoresData;
          this.typeOptions.graphic[0].style.text = res.data?.averageScore;
          let total = 0;
          let color = ["#fb788a", "#52c1f5", "#ffc542", "#24d2d3"];
          res.data?.scoresData.forEach((item) => (total += Number(item.value)));
          this.percentageData = res.data?.scoresData.map((item, index) => {
            return {
              title: item.name,
              number: Number(((item.value / total) * 100).toFixed(2)),
              color: color[index],
            };
          });
        }
      });
    },
    // 评价统计总数
    queryEvaluationManage() {
      comprehensiveAssessmentApi.queryEvaluationManage({}).then((res) => {
        if (res.code === 0) {
          this.statisticsData.forEach((item) => {
            if (item.id == 1) {
              item.data = res.data.year;
              item.percentage = res.data.yearStatus;
            }
            if (item.id == 2) {
              item.data = res.data.evalNotesCount;
            }
          });
        }
      });
    },
  },
  mounted() {
    this.queryLatestEvaluationPage();
    this.queryNoticeList();
    this.queryCompletionStatus();
    this.queryDistributionScores();
    this.queryEvaluationManage();
  },
};
</script>

<style lang="scss" scoped>
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
  .Statistics {
    display: flex;
    gap: 30px;
    .Statistics-item {
      padding-left: 15px;
      height: 140px;
      display: flex;
      gap: 15px;
      .vertical-line {
        width: 6px;
        height: 120px;
      }
      .bg-color-0 {
        background-color: #007bff; // 蓝色
      }
      .bg-color-1 {
        background-color: #9f47ec; // 绿色
      }
      .bg-color-2 {
        background-color: #0ce63f; // 黄色
      }
      .bg-color-3 {
        background-color: #cc3b4e; // 红色
      }

      .content {
        height: 100px;
        display: flex;
        align-items: center;
        margin-top: 10px;
        gap: 20px;
        .image {
          width: 50px;
          height: 50px;
        }
        .content-text {
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-bottom: 1px solid #3b3a3a;
          width: 200px;
          .title {
            font-size: 15px;
            margin-bottom: 5px;
          }
          .data {
            display: flex;
            gap: 40px;
            align-items: flex-end;
            margin-bottom: 5px;
            .number {
              font-size: 20px;
              font-weight: bold;
              color: #007bff;
            }
            .unit {
              font-size: 10px;
              color: #999;
            }
          }
          .percentage {
            font-size: 15px;
            color: #999;
          }
        }
      }
    }
  }
  .chart-title,
  .table-title {
    font-size: 20px;
    font-weight: bold;
  }
  .chart-container {
    height: 300px;
    display: flex;
    gap: 20px;
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 15%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .table-container {
    height: 300px;
    margin-top: 30px;
  }
  .evaluation-notice-container {
    margin-top: 30px;
    height: 300px;
    overflow-y: auto;
    position: relative;

    .notice-item {
      margin-bottom: 30px;
      .notice-title {
        display: flex;
        gap: 20px;
        font-size: 15px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .notice-content {
        font-size: 15px;
        color: #333;
        white-space: nowrap; /* 禁止换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
        max-width: 100%; /* 限制最大宽度 */
      }
    }
    .publish-button {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      // width: 100%;
    }
  }
  .add-evaluation-button {
    margin: 30px 0 0 50px;
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>