const { defineConfig } = require("@vue/cli-service");

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  devServer: {
    port: 8080,
    open: true,
    historyApiFallback: true,
    client: {
      overlay: {
        errors: true,
        warnings: false,
        runtimeErrors: false,
      },
    },
    proxy: {
      "/api": {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          "^/api": "",
        },
      },

      //舆情态势Api
      "/publicSentimentApi": {
        //target: "http://10.251.36.198:18880/v1",
        target: "http://47.121.201.42:9804/v1",
        changeOrigin: true,
        pathRewrite: {
          "^/publicSentimentApi": "",
        },
      },
    },
  },
  configureWebpack: {
    devtool: "source-map",
    resolve: {
      alias: {
        vue$: "vue/dist/vue.esm.js",
      },
    },
  },
  css: {
    loaderOptions: {
      scss: {
        // prependData: `@import "@/styles/color-custom.scss";`
      },
    },
  },
});
