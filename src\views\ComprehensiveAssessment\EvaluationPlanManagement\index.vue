<template>
  <div class="evaluation-criteria">
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover">
          <el-form :inline="true" :model="searchData" class="demo-form-inline">
            <el-form-item label="方案名称">
              <el-input
                v-model="searchData.name"
                placeholder="请输入方案名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="方案类型">
              <el-select v-model="searchData.type" placeholder="请选择">
                <el-option
                  v-for="item in planTypeList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="评价周期">
              <el-input
                v-model="searchData.cycle"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
            <el-form-item label="方案状态">
              <el-select v-model="searchData.status" placeholder="请选择">
                <el-option
                  v-for="item in planStatusList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="onSubmit"
                >高级筛选</el-button
              >
              <el-button type="primary" icon="el-icon-refresh" @click="onReset"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <PortalTable
            :tableHeight="550"
            :columns="columns"
            :table-data="tableData"
            row-key="id"
            :pagination="pagination"
            :showAddButton="false"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          />
        </el-card>
      </el-col>
    </el-row>
    <publicDialog ref="publicDialogRef" @refreshTableData="refreshTableData" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import publicDialog from "@/views/ComprehensiveAssessment/EvaluationPlanManagement/components/publicDialog.vue";
export default {
  name: "EvaluationCriteria",
  components: {
    PortalTable,
    publicDialog,
  },
  data() {
    return {
      pieceList: [
        {
          title: "总评价细则",
          text: "24",
          status: "12",
        },
        {
          title: "自动评分细则",
          text: "18",
          status: "8",
        },
        {
          title: "人工评分细则",
          text: "6",
          status: "-5",
        },
        {
          title: "待审核细则",
          text: "2",
          status: "0",
        },
      ],
      planTypeList: [
        {
          label: "年度评价",
          value: "年度评价",
        },
        {
          label: "季度评价",
          value: "季度评价",
        },
        {
          label: "月度评价",
          value: "月度评价",
        },
        {
          label: "周度评价",
          value: "周度评价",
        },
      ],
      planStatusList: [
        {
          label: "草稿",
          value: 0,
        },
        {
          label: "已拒绝",
          value: 1,
        },
        {
          label: "已归档",
          value: 2,
        },
        {
          label: "待审核",
          value: 3,
        },
        {
          label: "已审核",
          value: 4,
        },
      ],
      searchData: {
        name: "",
        type: "",
        cycle: "",
        status: null,
      },
      columns: [
        { text: true, prop: "schemeName", label: "方案名称", minWidth: "300" },
        { text: true, prop: "type", label: "方案类型", width: "120" },
        { text: true, prop: "cycle", label: "评价周期", width: "120" },
        { text: true, prop: "startTime", label: "创建时间", width: "150" },
        { text: true, prop: "status", label: "状态", width: "120" },
        {
          action: true,
          label: "操作",
          width: "300",
          operationList: [
            {
              label: "编辑",
              permission: "evaluationCriteria:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "evaluationCriteria:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "详情",
              permission: "evaluationCriteria:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          index: "001",
          name: "2023年度应急值守综合评价方案",
          creator: "张三",
          type: "年度评价",
          period: "2023年度",
          createTime: "2023-01-15",
          status: "已审核",
          operations: "",
        },
        {
          index: "002",
          name: "2023年第一季度应急值守评价方案",
          creator: "张三",
          type: "季度评价",
          period: "2023年Q1",
          createTime: "2023-01-05",
          status: "待审核",
          operations: "",
        },
        {
          index: "003",
          name: "2023年1月应急值守评价方案",
          creator: "张三",
          type: "月度评价",
          period: "2023年1月",
          createTime: "2023-01-01",
          status: "草稿",
          operations: "",
        },
        {
          index: "004",
          name: "2023年第1周应急值守评价方案",
          creator: "张三",
          type: "周度评价",
          period: "2023年第1周",
          createTime: "2022-12-26",
          status: "已拒绝",
          operations: "",
        },
        {
          index: "005",
          name: "2022年度应急值守综合评价方案",
          creator: "张三",
          type: "年度评价",
          period: "2022年度",
          createTime: "2022-01-18",
          status: "已归档",
          operations: "",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
    };
  },
  methods: {
    // 查询评价方案列表分页
    querySchemeList() {
      try {
        comprehensiveAssessmentApi
          .querySchemeList({
            page: this.pagination.currentPage,
            count: this.pagination.pageSize,
            ...this.searchData,
          })
          .then((res) => {
            this.tableData = res.data.items.map((item) => {
              return {
                ...item,
                type: this.planTypeList.find((type) => type.value === item.type)
                  ?.label,
                status: this.planStatusList.find(
                  (status) => status.value === item.status
                )?.label,
              };
            });
            this.pagination.total = res.data.total;
          });
      } catch (error) {
        // this.$message.error("查询评价方案列表失败: " + error.message);
      }
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.querySchemeList();
    },
    onReset() {
      this.searchData = {
        name: "",
        type: "",
        cycle: "",
        status: "",
      };
      this.querySchemeList();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.querySchemeList();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.querySchemeList();
    },
    handleEdit(row) {
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.querySchemeById(row);
      this.$refs.publicDialogRef.dialogType = "edit";
      this.$refs.publicDialogRef.dialogTitle = "编辑方案";
    },
    handleDelete(row) {},
    handleDetail(row) {
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.querySchemeById(row);
      this.$refs.publicDialogRef.dialogType = "detail";
      this.$refs.publicDialogRef.dialogTitle = "查看方案详情";
    },

    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "import_top",
        handler: this.importTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.exportTopHandler,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.downloadTopHandler,
      });
    },
    addTopHandler() {
      this.$refs.publicDialogRef.resetForm();
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "add";
      this.$refs.publicDialogRef.dialogTitle = "新增方案";
    },
    async importTopHandler() {
      try {
        // 创建文件输入元素
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".xlsx,.xls";

        input.onchange = async (e) => {
          const file = e.target.files[0];
          if (!file) return;

          // 创建FormData对象
          const formData = new FormData();
          formData.append("file", file);

          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: "文件上传中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          try {
            // 调用API
            const res = await comprehensiveAssessmentApi.schemeUpload(formData);

            if (res.code === 0) {
              this.$message.success("导入成功");
              this.refreshTableData(); // 刷新表格数据
            } else {
              this.$message.error(res.message || "导入失败");
            }
          } catch (error) {
            console.error("导入错误:", error);
            this.$message.error("导入失败: " + (error.message || "未知错误"));
          } finally {
            loading.close();
          }
        };

        // 触发文件选择
        input.click();
      } catch (error) {
        console.error("导入异常:", error);
        this.$message.error("导入异常: " + error.message);
      }
    },
    async exportTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.schemeExportList({
          ...this.searchData,
        });

        if (!res.data) {
          throw new Error("API返回数据为空");
        }

        // 添加响应类型检查;
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价方案.xlsx";

        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败: " + error.message);
      }
    },
    async downloadTopHandler() {
      try {
        const res = await comprehensiveAssessmentApi.downloadTemplate({
          type: 5,
        });
        if (!res.data) {
          throw new Error("API返回数据为空");
        }
        const blob = new Blob([res.data], {
          type:
            res.data.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const downloadElement = document.createElement("a");
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = "评价方案模板.xlsx";
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败: " + error.message);
      }
    },
    refreshTableData() {
      this.pagination.currentPage = 1;
      this.searchData = {
        name: "",
        type: "",
        cycle: "",
        status: "",
      };
      this.querySchemeList();
    },
  },
  mounted() {
    this.registerHandlers();
    this.querySchemeList();
  },
};
</script>

<style lang="scss" scoped>
.evaluation-criteria {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px; // 统一控制所有元素间距
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>