<template>
  <!-- 指令页面 -->
  <div class="task-index">
    <div class="instruction-name">{{ instructionData.commandName }}</div>
    <portal-table
      style="padding: 20px"
      :tableHeight="tableHeight"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />

    <div class="add-back-button">
      <el-button
        type="primary"
        plain
        icon="el-icon-back"
        @click="handleBackClick"
      >
        返回
      </el-button>
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
        新增
      </el-button>
      <el-button
        type="warning"
        icon="el-icon-s-promotion"
        @click="handleIssueCommand"
      >
        任务下发
      </el-button>
    </div>

    <!-- 新增弹窗 -->
    <general-dialog
      :dialog-top="'40px'"
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :show-footer="false"
      @cancel="handleCancel"
    >
      <command-configuration-form
        :type="'task'"
        :is-edit-mode="isEditMode"
        :editing-row="editingRow"
        :org-options="orgOptions"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      />
    </general-dialog>

    <!-- 任务下发弹窗 -->
    <general-dialog
      :dialog-visible="issueDialogVisible"
      :dialog-width="issueDialogWidth"
      :general-dialog-title="generalIssueDialogTitle"
      @cancel="handleCancel"
      @confirm="handleIssueSubmit"
    >
      <portal-table
        style="padding: 20px"
        :table-height="565"
        :showAddButton="false"
        :columns="issueColumns"
        :pagination="issuePagination"
        :table-data="issueTableData"
        row-key="name"
        @handle-size-change="handleIssueSizeChange"
        @handle-current-change="handleIssueCurrentChange"
        @handle-selection-change="handleIssueSelectionChange"
      />
    </general-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { commandConfigurationApi, orgApi } from "@/api";
import CommandConfigurationForm from "@/views/InformationSubmit/ResponseManagement/CommandConfiguration/components/CommandConfigurationForm.vue";

export default {
  name: "TaskIndex",
  components: { CommandConfigurationForm, GeneralDialog, PortalTable },
  props: {
    instructionData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableHeight: 600,
      selectedRows: [],
      searchItems: [
        {
          prop: "keyWord",
          label: "关键词搜索",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
      ],
      searchParams: null,
      tableData: [],
      columns: [
        { prop: "orgNameStr", label: "机构/单位/名称", text: true },
        { prop: "commandCenter", label: "指挥部/现场指挥部", text: true },
        { prop: "commandRole", label: "角色", text: true },
        {
          prop: "responsibilities",
          label: "职责/任务",
          text: true,
          minWidth: 200,
        },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "编辑",
              permission: "dutyCheck:download",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "dutyCheck:download",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },

      isEditMode: false,
      editingRow: null,
      orgOptions: [],
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "添加指令",
      //事件类型
      eventTypeList: [],
      // 响应指令列表
      responseInstructionList: [],
      //机构单位
      institutionalUnitList: [],
      //指挥部/现场指挥部
      commandList: [],
      //现场指挥部
      siteCommandList: [],
      form: {
        name: "",
      },
      rules: {
        name: [{ required: true, message: "请输入指令名称", trigger: "blur" }],
      },

      issueDialogVisible: false,
      issueDialogWidth: "1100px",
      generalIssueDialogTitle: "任务下发",
      issueColumns: [
        { prop: "infoTitle", label: "事件标题", text: true },
        { prop: "infoTime", label: "事发时间", text: true },
        { prop: "createTime", label: "接报时间", text: true },
        { prop: "infoType", label: "事件类型", text: true },
        { prop: "infoReportingUnit", label: "上报单位", text: true },
        { prop: "responseLevel", label: "响应级别", text: true },
      ],
      issuePagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      issueTableData: [],
      issueSelectedRows: [],
    };
  },
  mounted() {
    const gaping = 50 + 40 + 15 + 12 + 20 + 45 + 140;
    this.tableHeight = window.innerHeight - gaping;
    this.fetchData();
  },
  methods: {
    async fetchData(searchParams = {}) {
      const params = {
        reportId: this.instructionData.reportId,
        responseDirective: this.instructionData.id,
        page: this.pagination.currentPage,
        count: this.pagination.pageSize,
        ...searchParams,
      };
      const { code, data, message, error } =
        await commandConfigurationApi.queryResponseCommandPage(params);
      if (code !== 0) return this.$message.error(message || error);
      this.tableData = data.items;
      this.pagination.total = data.total;
    },
    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1; // 搜索时重置到第一页
      this.fetchData(row); // 重新加载数据
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleAdd() {
      this.getOrgTree();
      this.editingRow = {
        reportId: this.instructionData.reportId,
        responseDirective: this.instructionData.id,
      };
      this.isEditMode = false;
      this.dialogVisible = true;
      this.generalDialogTitle = "新增指令配置";
    },

    handleEdit(row) {
      this.getOrgTree();
      row.reportId = this.instructionData.reportId;
      row.responseDirective = this.instructionData.id;
      this.editingRow = row;
      this.isEditMode = true;
      this.generalDialogTitle = "编辑指令配置";
      this.dialogVisible = true;
      this.form = row;
    },

    async getOrgTree() {
      let params = {
        id: "",
        orgName: "",
        orderRule: 0,
        parentId: "",
      };
      const { code, data, message, error } = await orgApi.queryOrgTree(params);
      if (code !== 0) return this.$message.error(message || error);
      this.orgOptions = this.transformOrgTreeToOptions(data);
    },

    transformOrgTreeToOptions(treeData) {
      const options = [];
      const traverse = (nodes) => {
        nodes.forEach((node) => {
          options.push({
            label: node.orgName,
            value: node.id,
          });
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        });
      };
      traverse(treeData);
      return options;
    },

    handleIssueCommand() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要下发的指令");
        return;
      }
      this.issueDialogVisible = true;
    },

    handleDelete(row) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    async handleSubmit(submitData) {
      try {
        let response;

        if (this.isEditMode) {
          // 编辑模式
          response = await commandConfigurationApi.updateResponseCommand({
            ...submitData,
            id: this.editingRow.id,
          });

          if (response.code === 0) {
            this.$message.success("编辑指令配置成功！");
            await this.fetchData(); // 重新获取数据
          } else {
            this.$message.error(response.message || "编辑失败");
            return;
          }
        } else {
          // 新增模式
          response = await commandConfigurationApi.createResponseCommand(
            submitData
          );

          if (response.code === 0) {
            this.$message.success("新增指令配置成功！");
            await this.fetchData(); // 重新获取数据
          } else {
            this.$message.error(response.message || "新增失败");
            return;
          }
        }

        this.dialogVisible = false;
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error("提交失败，请重试");
      }
    },

    handleIssueSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleIssueCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    handleIssueSelectionChange(selection) {
      this.issueSelectedRows = selection;
    },

    //任务下发
    handleIssueSubmit() {
      if (
        this.selectedRows.length === 0 ||
        this.issueSelectedRows.length === 0
      ) {
        this.$message.warning("请选择要下发到的事件");
        return;
      }
      console.log(this.selectedRows);
      console.log(this.issueSelectedRows);
    },

    handleCancel() {
      this.dialogVisible = false;
      this.issueDialogVisible = false;
      this.form = {
        name: "",
      };
    },

    handleBackClick() {
      this.$emit("handleTaskCancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.task-index {
  position: relative;

  .instruction-name {
    height: 40px;
    margin-top: 10px;
    margin-left: 20px;
    line-height: 40px;
    font-size: 20px;
    font-weight: 700;
    text-align: left;
  }

  .add-back-button {
    position: absolute;
    top: 2px;
    right: 20px;

    .el-button--primary.is-plain {
      color: var(--themeColor);
      background: var(--lightGray1);
      border-color: var(--themeColor) !important;
    }
  }
}
</style>
