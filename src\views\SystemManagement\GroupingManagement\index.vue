<template>
  <div class="grouping-management-container">
    <div class="role-management-main">
      <portal-table
        style="width: 100%"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="name"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />
      <general-dialog
        :dialog-visible="dialogVisible"
        :dialog-width="dialogWidth"
        :general-dialog-title="generalDialogTitle"
        :set-component-name="$store.getters.componentName"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      >
        <el-form
          ref="addForm"
          :model="form"
          :rules="rules"
          class="add-form"
          label-position="top"
          label-width="100px"
        >
          <el-form-item label="分组说明" prop="groupExplain">
            <el-input
              v-model="form.groupExplain"
              placeholder="请输入分组说明"
            />
          </el-form-item>
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="form.groupName" placeholder="请输入分组名称" />
          </el-form-item>
        </el-form>
      </general-dialog>
      <add-uers-dialog ref="addUersDialogRef" />
    </div>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import AddUersDialog from "@/views/SystemManagement/GroupingManagement/components/addUersDialog.vue";
import { systemManagementApi } from "@/api";
export default {
  name: "GroupingManagement",
  components: {
    PortalTable,
    GeneralDialog,
    AddUersDialog,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "name",
          label: "分组名称",
          type: "input",
          placeholder: "请输入分组名称",
          width: "700px",
        },
      ],
      tableData: [],
      columns: [
        {
          text: true,
          prop: "groupName",
          label: "分组名称",
        },
        {
          text: true,
          prop: "groupExplain",
          label: "分组说明",
        },
        {
          text: true,
          prop: "createTime",
          label: "创建时间",
        },
        { text: true, prop: "createName", label: "创建人" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "编辑",
              permission: "groupingManagement:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "groupingManagement:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "添加人员",
              permission: "groupingManagement:addUsers",
              buttonClick: this.handleAddUsers,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      searchKeyword: "",
      form: {
        groupExplain: "",
        groupName: "",
        id: "",
      },
      rules: {
        groupExplain: [
          { required: true, message: "请输入分组说明", trigger: "blur" },
        ],
        groupName: [
          { required: true, message: "请输入分组名称", trigger: "blur" },
        ],
      },
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增分组",
      dialogType: "add",
    };
  },
  methods: {
    // 获取分组数据
    async getGroupList() {
      const params = {
        count: this.pagination.pageSize,
        groupName: this.searchKeyword,
        page: this.pagination.currentPage,
      };
      let res;
      try {
        res = await systemManagementApi.queryGroupManagePage(params);
      } catch (error) {}

      this.tableData = res?.data.items || [];
      this.pagination.total = res?.data.total || 0;
    },
    // 搜索
    handleSearch(data) {
      this.pagination.currentPage = 1;
      this.searchKeyword = data.name || "";
      this.getGroupList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.getGroupList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getGroupList();
    },

    addHandler() {
      this.dialogVisible = true;
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
    },
    //取消
    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        groupExplain: "",
        groupName: "",
      };
    },
    //提交
    handleSubmit() {
      if (this.dialogType === "add") {
        this.addSubmit();
      } else if (this.dialogType == "edit") {
        this.editSubmit();
      }
    },
    addSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .createGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.pagination.currentPage = 1;
              this.pagination.pageSize = 10;
              this.$refs.addForm.resetFields();
              this.getGroupList();
            })
            .catch(() => {
              // this.$message.error("新增失败");
            });
        }
      });
    },
    // 列表编辑提交
    editSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          systemManagementApi
            .updateGroupManage(this.form)
            .then(() => {
              this.dialogVisible = false;
              this.$refs.addForm.resetFields();
              this.getGroupList();
            })
            .catch(() => {
              // this.$message.error("编辑失败");
            });
        }
      });
    },
    // 列表删除按钮
    handleDelete(row) {
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          systemManagementApi.deleteGroupManage({ id: row.id }).then(() => {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.getGroupList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 列表编辑按钮
    handleEdit(row) {
      this.generalDialogTitle = "编辑分组";
      this.dialogVisible = true;
      this.dialogType = "edit";
      this.form = {
        groupExplain: row.groupExplain,
        groupName: row.groupName,
        id: row.id,
      };
    },
    // 列表添加人员
    handleAddUsers(row) {
      this.$refs.addUersDialogRef.addUersDialogVisible = true;
      this.$refs.addUersDialogRef.dialogVisible = true;
      this.$refs.addUersDialogRef.form.id = row.id;
      this.$refs.addUersDialogRef.form.groupName = row.groupName;
    },
  },
  mounted() {
    this.registerHandlers();
    this.getGroupList();
  },
};
</script>

<style lang="scss" scoped>
.grouping-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px;
  box-sizing: border-box;
  .role-management-main {
    flex: 1;
    display: flex;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
  }
}

.search-wrapper {
  margin-bottom: 8px;

  /* 搜索表单样式优化 */
  ::v-deep .search-form {
    .search-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-row,
      .search-buttons-row {
        margin: 0 !important;
      }
    }
  }
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

  /* 按钮样式优化 */
  ::v-deep .el-button--text {
    &:nth-child(2) {
      color: #b93742 !important;
      &:hover {
        color: #f70a0a !important;
      }
    }
    &:nth-child(1):hover,
    &:nth-child(3):hover {
      color: #0621f1 !important;
    }
  }
}
</style>