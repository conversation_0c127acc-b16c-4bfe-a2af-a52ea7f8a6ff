<template>
  <!-- 信息记录 -->
  <div class="info-record">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="记录内容" prop="infoRecord">
        <el-input
          v-model="form.infoRecord"
          type="textarea"
          :rows="6"
          placeholder="请输入记录内容"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "InfoRecord",
  data() {
    return {
      form: {
        infoRecord: "",
      },
      rules: {
        infoRecord: [
          { required: true, message: "请输入信息记录", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$message({
            message: "信息记录成功",
            type: "success",
          });
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      infoRecord: "",
    };
  },
};
</script>

<style scoped lang="scss"></style>
