<!-- 换班替班 -->
<template>
  <div style="padding: 20px;" class="user-index-container">
    <portal-table
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :showSelection=false
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
      @switch-change="handleSwitchChange"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />
    <!-- 新增模版 -->
    <general-dialog
      :dialog-visible="templateDialog"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :showFooter=false
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <addEidt ref="addEditClient" @ok="loadData"/>
    </general-dialog>

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :showFooter=false
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <checked ref="addEditClient" @ok="loadData"/>
    </general-dialog>

  </div>
</template>

<script>

import { dutyManagementApi } from '@/api/index'
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import {conversionDateNotTime} from '@/utils/publicMethod'

import checked from './components/checked.vue'

export default {
  name: 'UserIndex',
  components: {PortalTable, GeneralDialog, checked},
  data() {
    return {
      columns: [
        {text: true, prop: 'dat', label: '日期'},
        {text: true, prop: 'substituteTypeName', label: '类型'},
        {text: true, prop: 'datReplace', label: '换班日期'},
        {text: true, prop: 'positionName', label: '替换岗位'},
        {text: true, prop: 'userName', label: '替换前人员姓名'},
        {text: true, prop: 'userNameReplace', label: '替换后人员姓名'},
        // {text: true, prop: 'notarizeTime', label: '确认时间'},
        {text: true, prop: 'substituteContent', label: '替换原因'},
        {text: true, prop: 'statusName', label: '状态'},
        // {
        //   action: true, //是否显示操作
        //   label: '操作',
        //   width: '160px',
        //   operationList: [
        //     // {
        //     //   label: '查看',
        //     //   permission: 'menu:edit',
        //     //   buttonClick: this.handleEdit,
        //     //   isShow: (row, $index) => {
        //     //     return true
        //     //   }
        //     // },
        //     {
        //       label: '审批',
        //       permission: 'menu:delete',
        //       buttonClick: this.handleApproval,
        //       isShow: (row, $index) => {
        //         if(this.tableData[$index].status == 2){
        //             return false
        //         }else{
        //             return true
        //         }
        //       }
        //     }
        //   ]
        // }
      ],
      searchItems: [
        {
          prop: "dataList",
          label: "时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          label: "类型",
          prop: "substituteTypeName",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "positionName",
          label: "替换岗位",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "userName",
          label: "人员姓名",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
      ],
      postTemplateList: [
        {
          id: "1",
          value: "type1",
          label: "汛期岗位模板",
        },
        {
          id: "2",
          value: "type2",
          label: "森防期岗位模板",
        },
      ],
      tableData: [
        // {
        //   dat: "2025-06-28",
        //   substituteTypeName: "替班",
        //   datReplace: "--",
        //   positionName: "带班处长-主班",
        //   userName: "马云",
        //   userNameReplace: "雷军",
        //   notarizeTime: "2025-06-28",
        //   substituteContent: "小米YU7",
        //   status: "完成",
        // },
        // {
        //   dat: "2025-06-28",
        //   substituteTypeName: "换班",
        //   datReplace: "2025-06-28",
        //   positionName: "带班处长-主班",
        //   userName: "马云",
        //   userNameReplace: "雷军",
        //   notarizeTime: "2025-06-28",
        //   substituteContent: "小米YU7",
        //   status: "待审批",
        // }
      ],

      searchParams: null,
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      dialogVisible: false,
      dialogWidth: '560px',
      generalDialogTitle: '',

      templateDialog: false,
    }
  },
  created() {

  },
  mounted() {
    this.registerHandlers();
    this.tableDataFn()
  },
  methods: {
    registerHandlers() {
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'add_top',
        handler: this.handleAddTemplateClick
      });
      this.$store.commit('generalEvent/registerEventHandler', {
        type: 'edit_top',
        handler: this.handleAddTemplateClick
      });
    },
    handleAddTemplateClick() {
      // 新增模板
      this.templateDialog = true
    },
    handleApproval(row) {
      let txt = '确定审批该条记录, 是否继续?'
      this.$confirm(txt, '审批', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dutyManagementApi.duTyReplaceApproval({id: row.id}).then((res) => {
          const {code, data, message, error} = res;
          if (code !== 0) return this.$message.error(message || error)
          this.$message.success('审批成功')
          this.tableDataFn()
        })
      })
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.dataList && row.dataList.length > 0) {
        row.startTime = conversionDateNotTime(row.dataList[0])
        row.endTime = conversionDateNotTime(row.dataList[1])
        delete row.dataList
      }
      this.searchParams = row;
      this.tableDataFn();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.tableDataFn();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.tableDataFn();
    },

    async tableDataFn() { //获取所有菜单
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...this.searchParams
      };
      const res = await dutyManagementApi.querySubstitutePage(params)
      const {code, data, error} = res
      if (code === 0) {
        this.tableData = data.items;
        this.pagination.total = data.total;
        // this.updateTreeArray(this.tableData)
      } else {
        this.$message.error(error)
      }
    },
    updateTreeArray(nodes) {
      let _this = this
      nodes.forEach(node => {
        node.active = false
        if (node.menuType === 0) {
          node.menuTypeName = '菜单'
        } else if (node.menuType === 1) {
          node.menuTypeName = '按钮'
        } else {
          node.menuTypeName = '模块'
        }
        if (node.children) {
          node.children.forEach(child => {
            if (typeof child === 'object') { // 如果已经是对象，则可以递归处理或直接修改其属性
              _this.updateTreeArray([child]) // 对子对象递归调用，这里可能需要调整以适应实际情况，例如直接在原数组上操作可能不需要额外的数组包裹。
            }
          })
        }
      })
    },
    //查询数据
    fetchData() {
    },

    //选中数据
    handleSelectionChange(selection) {
      console.log(selection)
    },

    //日期格式化函数，转换为yyyy-MM-dd格式
    dateFormatter(row, column, cellValue) {
      return conversionDate(cellValue)
    },

    //开关
    handleSwitchChange(row, $index) {
      //console.log(row, $index);
    },

    loadData(e) {
      this.dialogVisible = false
      if (e === 1) {
        return
      }
      this.tableDataFn()
    },

    //新增
    handleAddPostClick() {
      // 新增岗位
      this.generalDialogTitle = '新增菜单'
      this.dialogVisible = true
      this.$nextTick(function () {
        this.$refs.addEditClient.addFormFn()
      })
    },
    //编辑
    handleEdit(row) {
      this.generalDialogTitle = '修改菜单'
      this.dialogVisible = true
      this.$nextTick(function () {
        this.$refs.addEditClient.edit(row)
      })
    },

    //提交
    handleSubmit() {

    },

    handleCancel() {
      this.dialogVisible = false;
      this.templateDialog = false;
    },

    //删除
    handleDelete(row) {
      let txt = '此操作将永久删除该条数据, 是否继续?'
      if (row.children.length > 0) {
        txt = '此操作将永久删除该条数据和子级数据，是否继续？'
      }
      this.$confirm(txt, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $api.systemApi.deleteMenu({id: [row.id]}).then((res) => {
          if (res.data.code !== 0) return this.$message.error(res.data.message)
          this.$message.success('删除成功')
          this.tableDataFn()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
