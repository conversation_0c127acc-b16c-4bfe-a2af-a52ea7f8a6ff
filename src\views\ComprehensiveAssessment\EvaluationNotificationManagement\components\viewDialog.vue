<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :dialog-width="'800px'"
    :set-component-name="$store.getters.componentName"
    :show-footer="false"
    @cancel="handleCancel"
  >
    <div class="view-dialog">
      <div class="title">{{ noticeInfo.noticeTitle }}</div>
      <el-divider></el-divider>
      <div class="notice-list">
        <div class="left">
          <div class="left-item">通知编号：{{ noticeInfo.noticeNum }}</div>
          <div class="left-item">发送单位：{{ noticeInfo.senderUnit }}</div>
          <!-- <div class="left-item">接收单位：{{ noticeInfo.receiverUnit }}</div> -->
          <div class="left-item">发送时间：{{ noticeInfo.sendTime }}</div>
          <div class="left-item">发送状态：{{ sendStatus }}</div>
        </div>
        <div class="right">
          <div class="right-item">阅读情况</div>
          <div class="right-item">
            总接收单位：{{ noticeInfo.receiverUnitNum }}
          </div>
          <div class="right-item">
            已阅读：
            <span style="color: #00a63e">
              {{ noticeInfo.haveRead }}（{{ noticeInfo.haveReadPercentum }})
            </span>
          </div>
          <div class="right-item">
            未阅读：
            <span style="color: #e70711">
              {{ noticeInfo.unread }}（{{ noticeInfo.unreadPercentum }})
            </span>
          </div>
          <el-progress
            :stroke-width="10"
            :show-text="false"
            :percentage="parsePercentage(noticeInfo.haveReadPercentum)"
          ></el-progress>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="notice-content">
        <div class="notice-content-title item-title">通知内容</div>
        <div
          class="notice-content-text"
          v-html="formattedRemark(noticeInfo.noticeRemark)"
        ></div>
      </div>
      <el-divider></el-divider>
      <div class="download">
        <div class="item-title">附件下载</div>
        <div class="download-list">
          <div
            class="download-item"
            v-for="(item, index) in noticeInfo.fileInfoList"
            :key="index"
            @click="downloadFile(item)"
          >
            {{ item.fileName }}
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="notice-table">
        <div class="item-title">接收单位阅读情况</div>
        <el-table
          max-height="200px"
          :data="noticeInfo.readNoticeList"
          border
          style="width: 100%"
        >
          <el-table-column
            align="center"
            prop="orgId"
            label="单位名称"
            width="180"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="readTime"
            label="阅读时间"
            width="180"
          >
          </el-table-column>
          <el-table-column align="center" prop="readStatusStr" label="状态">
            <template #default="{ row }">
              {{
                row.readStatusStr === "0"
                  ? "待发送"
                  : row.readStatusStr === "1"
                  ? "已发送"
                  : row.readStatusStr === "2"
                  ? "已阅读"
                  : row.readStatusStr === "3"
                  ? "待阅读"
                  : row.readStatusStr
              }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </general-dialog>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import { auth } from "@/utils";
export default {
  components: {
    GeneralDialog,
  },
  data() {
    return {
      dialogVisible: false,
      noticeInfo: {},
      tableData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
        {
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
        },
        {
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄",
        },
      ],
      sendStatusOption: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "待发送",
          value: 0,
        },
        {
          label: "已发送",
          value: 1,
        },
        {
          label: "已阅读",
          value: 2,
        },
        {
          label: "待阅读",
          value: 3,
        },
      ],
    };
  },
  computed: {
    sendStatus() {
      return (
        this.sendStatusOption.find(
          (item) => item.value === this.noticeInfo.sendStatus
        )?.label || ""
      );
    },
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    // 查询评价通知详情
    queryNoticeInfo(row) {
      comprehensiveAssessmentApi
        .queryNoticeInfo({
          id: row.id,
        })
        .then((res) => {
          if (res.code === 0) {
            this.noticeInfo = res.data;
          }
        });
    },
    parsePercentage(percentStr) {
      if (!percentStr) return 0;
      // 去除百分号并转换为数字
      return parseFloat(percentStr.replace("%", "")) || 0;
    },
    formattedRemark(noticeRemark) {
      if (!noticeRemark || typeof noticeRemark !== "string") {
        return "";
      }
      // 替换换行符为<br>
      let formatted = noticeRemark.replace(/\n/g, "<br>");
      // 替换连续空格为&nbsp;（每个空格都替换）
      formatted = formatted.replace(/ /g, "&nbsp;");
      return formatted;
    },
    downloadFile(item) {
      this.toDownloadFile(item);
    },
    // 下载文件
    toDownloadFile(row) {
      console.log(row.fileUrl);
      const loading = this.$loading({
        lock: true,
        text: "正在下载...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        if (!row.fileUrl) {
          this.$message.warning("文件链接无效");
          return;
        }
        fetch(auth.getFileBaseUrl() + row.fileUrl)
          .then((response) => response.blob())
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = row.assessRulesName || "file";
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url); // 释放 URL 对象

            this.$message.success("文件下载已开始");
          })
          .catch((error) => {
            console.error("文件下载失败:", error);
            this.$message.error("文件下载失败，请稍后重试");
          });
      } catch (error) {
        console.error("文件下载失败:", error);
        this.$message.error("文件下载失败，请稍后重试");
      } finally {
        loading.close();
      }
    },
  },
};
</script>

<style  lang="scss" scoped>
.view-dialog {
  width: 100%;
  height: 700px;
  padding: 40px 20px;
  overflow-y: auto;
  .title {
    font-size: 23px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .item-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  .notice-list {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .right {
      margin-right: 200px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      font-size: 17px;
      & > :last-child {
        margin-top: 10px;
      }
    }
  }
  .notice-content-text {
    line-height: 1.8;
  }
  .download-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .download-item {
      cursor: pointer;
    }
    .download-item:hover {
      color: #007bff;
    }
  }
}
</style>