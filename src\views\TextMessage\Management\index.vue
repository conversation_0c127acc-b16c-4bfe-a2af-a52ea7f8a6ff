<template>
    <!-- 右侧内容区域 -->
    <div class="contact-book-container">
        <div class="contact-book-main">
            <!-- 搜索区域 -->
            <div class="search-wrapper">
                <SearchForm :form-config="searchConfig" :tips="searchTips" :show-tips="false" @search="handleSearch"
                    @reset="handleReset" ref="searchFormRef" />
            </div>
            <!-- 表格区域 -->
            <div class="table-wrapper">
                <DataTable :table-data="contactList || []" :columns="responsiveTableColumns || []"
                    :tableHeight="600"
                    :total="pagination?.total || 0" :current-page="pagination?.current || 1"
                    :page-size="pagination?.pageSize || 10" :row-actions="tableActions" :action-column-width="260"
                    :loading="loading" :stripe="true" :border="true" :show-pagination="true" :show-index="true"
                    :key="tableKey"  @row-action="handleTableRowAction" 
                    @current-change="handleCurrentChange" 
                    @size-change="handleSizeChange"/>
            </div>
        </div>
        <Association :visible.sync="associationVisible" :formData="formData" @submit="handleFormSubmit"></Association>
        <Forward :visible.sync="forwardVisible" :formData="formData" @submit="handleFormSubmit"></Forward>
        <DetailsView :visible.sync="detailsViewVisible" :formData="formData"></DetailsView>
    </div>
</template>

<script name="TextMessageManagement">
import { textMessageManagementApi } from "@/api";
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import Association from './components/association.vue';
import Forward from './components/forward.vue';
import DetailsView from './components/detailsView.vue';
import mixinTable from './mixinTable.js'
export default {
    mixins: [mixinTable],
    components: {
        SearchForm,
        DataTable,
        Association,
        Forward,
        DetailsView
    },
    data() {
        return {
            // 搜索配置
            searchConfig: [
                {
                    prop: "content",
                    label: "短信内容",
                    type: "input",
                    placeholder: "输入短信内容",
                    width: "200px",
                },
                {
                    prop: "receiver",
                    label: "接收人",
                    type: "input",
                    placeholder: "输入短信接收人",
                    width: "200px",
                },
                {
                    prop: "status",
                    label: "状态",
                    type: "select",
                    placeholder: "请选择接状态",
                    width: "200px",
                    options: [
                        { label: "未发送", value: "0" },
                        { label: "已发送", value: "1" },
                    ],
                },
                {
                    prop: "eventName",
                    label: "事件名称",
                    type: "input",
                    placeholder: "输入短信事件名称",
                    width: "200px",
                },

            ],
            searchTips: [

            ],
            // 表格列配置
            tableColumns: [
                { prop: "content", label: "短信内容", minWidth: 240, sortable: false },
                { prop: "names", label: "接收对象", minWidth: 240, sortable: false,render: (row, index) => {
                    return row.names.join()
                }},
                { prop: "planSendTime", label: "发送时间", width: 200, sortable: false },
                { prop: "status", label: "信息状态", width: 100, sortable: false,render: (row, index) => {
                   return ['未发送', '已发送'][row.status] 
                }},
                { prop: "editor", label: "编辑人", width: 150, sortable: false },

            ],
            tableActions: [
                {
                    key: "view",
                    label: '查看',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "association",
                    label: '关联事件',
                    type: "text",
                    size: "mini",
                    show:function(row){
                        return !row.eventId
                    }
                },
                {
                    key: "cancelEvent",
                    label: '取消关联',
                    type: "text",
                    size: "mini",
                    show:function(row){
                        return row.eventId
                    }
                },
                {
                    key: "forward",
                    label: '转发',
                    type: "text",
                    size: "mini",
                },
                {
                    key: "remove",
                    label: '删除',
                    type: "text",
                    size: "mini",
                    show:function(row){
                        return row.status != '1'
                    }
                },
            ],
            // 表格刷新key
            tableKey: 0,
            associationVisible:false,
            forwardVisible:false,
            detailsViewVisible:false,
            formData:{}
        };
    },
    computed: {
        // 响应式表格列配置
        responsiveTableColumns() {
            const screenWidth = window.innerWidth || 1200;
            return this.tableColumns;

        },
    },
    methods: {
       
        // 搜索相关方法
        async handleSearch(formData) {
            const searchParams = { ...formData };
            await this.seachContactList(searchParams);
            await this.getContactList();
            this.$message.success("搜索完成");
        },
        // 列表
        async initData() {
            try {
                await this.getContactList();
            } catch (error) {
                console.error("短信模版页面初始化失败:", error);
                this.$message.error("页面初始化失败，请刷新重试");
            }
        },
        // 重置
        async handleReset() {
            await this.seachContactList({});
            await this.setCurrentChange({ current: 1 })
            await this.getContactList();
        },
        // 关联事件
        async handleFormSubmit() {
            this.associationVisible = false
            this.forwardVisible = false
            this.handleReset()
        },
        handleTableRowAction(action, row, index){
            let key = action.key
            console.log(key)
            switch (key) {
                case 'view':
                    this.detailsViewVisible = true
                    this.formData = {
                        theme: row.theme,
                        editor: row.editor,
                        reviewer: row.reviewer,
                        planSendTime: row.planSendTime,
                        content: row.content, 
                        taskId:row.taskId,
                        eventName:row.eventName
                    }
                    break;
                case 'association':
                    this.associationVisible = true
                    this.formData = {
                        theme: row.theme,
                        editor: row.editor,
                        reviewer: row.reviewer,
                        planSendTime: row.planSendTime,
                        content: row.content, 
                        taskId:row.taskId
                    }
                    break;
                case 'cancelEvent':
                    this.$confirm("确定要取消吗", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        await textMessageManagementApi.cancelEvent({taskId:row.taskId});
                        this.initData()
                    }).catch(() => {
                    });
                    break;

                    
                case 'forward':
                    this.formData = {
                        theme: row.theme,
                        editor: row.editor,
                        reviewer: row.reviewer,
                        planSendTime: row.planSendTime,
                        content: row.content, 
                        taskId:row.taskId
                    }
                    this.forwardVisible = true
                    break;
                case 'remove':
                    this.$confirm("确定要删除吗", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        await this.removeTextMessage(row)
                        this.initData()
                    }).catch(() => {
                    });
                    break;
            
                default:
                    break;
            }

        },
        async handleSizeChange(size) {
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({ pageSize: size })
            await this.getContactList();
        },
        // 分页
        async handleCurrentChange(page){
            let formData = this.$refs.searchFormRef.formData
            await this.seachContactList(formData)
            await this.setCurrentChange({current:page})
            await this.getContactList();
        }
    },
    mounted() {
        this.initData();
    }
}
</script>

<style scoped>
.contact-book-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-book-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    overflow: hidden;
}


.search-wrapper {
    margin-bottom: 8px;
}

.table-wrapper {
    flex: 1;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-wrapper >>> .el-button--text:nth-child(1) {
  color: #409eff !important;
}
.table-wrapper >>> .el-button--text:nth-child(2) {
  color: #67c23a !important;
}
.table-wrapper >>> .el-button--text:nth-child(3) {
  color: #ba68c8 !important;
}
.table-wrapper >>> .el-button--text:nth-child(4) {
  color: #e6a23c !important;
}

</style>