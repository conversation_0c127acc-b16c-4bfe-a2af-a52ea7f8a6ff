<template>
  <div class="analysis-index">
    <!--  舆情数量统计分析 和 重点关注舆情统计分析  -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="sentiment-num" shadow="hover">
          <div class="chart-title">舆情数量统计分析</div>
          <template v-for="(item, index) in sentimentNum">
            <el-statistic
              :value="item.value"
              :title="item.title"
              :suffix="item.unit"
            ></el-statistic
          ></template>
        </el-card>
      </el-col>
      <!--      <el-col :span="12">
        <el-card class="focus" shadow="hover">
          <div class="chart-title">重点关注舆情统计分析</div>
          <template v-for="(item, index) in focusList">
            <el-statistic
              :value="item.value"
              :title="item.title"
              :suffix="item.unit"
            ></el-statistic>
          </template>
        </el-card>
      </el-col>-->
    </el-row>

    <!--  舆情走势统计分析 和 舆情媒体类型分析  -->
    <el-row :gutter="20">
      <el-col :span="14">
        <el-card class="trend-statistics" shadow="hover">
          <div slot="header" class="chart-title">
            <span class="title">舆情走势统计分析</span>
            <div class="radio">
              <span style="margin-right: 20px">时间范围</span>
              <el-radio-group v-model="trendStatisticsTime">
                <el-radio-button label="全部"></el-radio-button>
                <el-radio-button label="当天"></el-radio-button>
                <el-radio-button label="24小时"></el-radio-button>
                <el-radio-button label="近7天"></el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="tendencyOptions" />
            </div>
            <!--            <div style="width: 2px; height: auto; border: 1px solid #cccccc; margin: 0 10px"></div>-->
            <!--            <div class="chart-container">
              <echarts-component :options="sourceRangeOptions" />
            </div>-->
          </div>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card class="echarts-card media-type" shadow="hover">
          <div slot="header" class="chart-title">
            <span>舆情媒体类型分析</span>
          </div>
          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="mediaTypeOptions" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!--  舆情重点媒体分布 和 舆情涉事分类分析 和 舆情活跃媒体  -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="echarts-card" shadow="hover">
          <div slot="header" class="chart-title">
            <span>舆情重点媒体分布</span>
          </div>
          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="mediaDistributionOptions" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="echarts-card" shadow="hover">
          <div slot="header" class="chart-title">
            <span>舆情涉事分类分析</span>
          </div>
          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="narrationClassOptions" />
            </div>
          </div>
        </el-card>
      </el-col>
      <!--      <el-col :span="8">
        <el-card class="echarts-card" shadow="hover">
          <div slot="header" class="chart-title">
            <span>舆情活跃媒体</span>
          </div>
          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="activeMediaOptions" />
            </div>
          </div>
        </el-card>
      </el-col>-->
    </el-row>

    <!--  舆情属地分析  -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="echarts-card" shadow="hover">
          <div slot="header" class="chart-title">
            <span>舆情属地分析</span>
          </div>
          <div class="echarts">
            <div class="chart-container">
              <echarts-component :options="territorialAnalysisOptions" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import echartsComponent from "@/components/echarts.vue";
import { publicSentimentApi } from "@/api";

export default {
  name: "AnalysisIndex",
  components: { echartsComponent },
  data() {
    return {
      //舆情数量统计分析数据
      sentimentNum: [
        {
          title: "今日预警信息",
          value: 10000,
          unit: "条",
        },
        {
          title: "本周预警信息",
          value: 1000,
          unit: "条",
        },
        {
          title: "本月预警信息",
          value: 1000,
          unit: "条",
        },
        {
          title: "本年预警信息",
          value: 1000,
          unit: "条",
        },
      ],

      //重点关注舆情统计分析数据
      focusList: [
        {
          title: "处置转发信息",
          value: 1000,
          unit: "条",
        },
      ],

      //舆情走势统计分析
      trendStatisticsTime: "上海",
      //舆情倾向性分析
      tendencyOptions: {
        title: {
          text: "舆情倾向性分析",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            data: [
              { value: 1048, name: "正面" },
              { value: 735, name: "负面" },
              { value: 580, name: "中性" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      },
      //舆情信源范围分析
      sourceRangeOptions: {
        title: {
          text: "舆情信源范围分析 ",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            data: [
              { value: 1048, name: "中央级媒体" },
              { value: 735, name: "地方级媒体" },
              { value: 580, name: "省级媒体" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      },

      //舆情媒体类型分析
      mediaTypeOptions: {
        grid: {
          top: "20", // 增加顶部留白空间
          left: "6%", // 增加左侧留白空间
          right: "20", // 增加右侧留白空间
          bottom: "10%", // 增加底部留白空间
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          axisLabel: {
            margin: 10, // 调整标签与坐标轴的间距
            interval: 0, // 强制显示所有标签
          },
          data: [],
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
        },
        series: [
          {
            data: [],
            type: "bar",
            barWidth: 30, // 调整柱条宽度
            label: {
              // 新增 label 配置
              show: true,
              position: "top",
              color: "#333",
            },
          },
        ],
      },

      //舆情重点媒体分布
      mediaDistributionOptions: {
        grid: {
          top: "20", // 增加顶部留白空间
          left: "10%", // 增加左侧留白空间
          right: "20", // 增加右侧留白空间
          bottom: "20%", // 增加底部留白空间
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          axisLabel: {
            rotate: 45, // 倾斜45度
            margin: 10, // 调整标签与坐标轴的间距
            interval: 0, // 强制显示所有标签
          },
          data: [
            "快手",
            "B站",
            "twitter",
            "facebook",
            "youtube",
            "今日头条",
            "腾讯新闻",
            "新浪新闻",
            "搜狐新闻",
            "凤凰网",
            "网易",
          ],
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 130, 130, 130, 130],
            type: "bar",
            label: {
              // 新增 label 配置
              show: true,
              position: "top",
              color: "#333",
            },
          },
        ],
      },
      //舆情涉事分类分析
      narrationClassOptions: {
        grid: {
          top: "20", // 增加顶部留白空间
          left: "10%", // 增加左侧留白空间
          right: "20", // 增加右侧留白空间
          bottom: "20%", // 增加底部留白空间
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          axisLabel: {
            rotate: 45, // 倾斜45度
            margin: 10, // 调整标签与坐标轴的间距
            interval: 0, // 强制显示所有标签
          },
          data: ["自然灾害", "治安慰问", "医疗卫生", "安全事故", "其他"],
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
        },
        series: [
          {
            data: [120, 200, 150, 80, 70],
            type: "bar",
            barWidth: 30, // 调整柱条宽度
            label: {
              // 新增 label 配置
              show: true,
              position: "top",
              color: "#333",
            },
          },
        ],
      },
      //舆情活跃媒体
      activeMediaOptions: {
        grid: {
          top: "20", // 增加顶部留白空间
          left: "15%", // 增加左侧留白空间
          right: "15", // 增加右侧留白空间
          bottom: "15%", // 增加底部留白空间
        },
        yAxis: {
          type: "category",
          axisLine: {
            // 新增y轴线条配置
            lineStyle: {
              width: 1, // 轴线宽度
            },
          },
          data: [
            "微博",
            "短视频",
            "微信",
            "贴吧",
            "网络视频",
            "电视视频",
            "app",
            "其他",
          ],
          axisLabel: {
            margin: 20, // 调整标签间距
          },
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 130],
            type: "bar",
            itemStyle: {
              borderRadius: [0, 20, 20, 0], // 右侧圆角
              color: "#5470c6", // 可选颜色调整
            },
            barWidth: 12, // 调整柱条宽度
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
          },
        ],
      },

      //舆情属地分析
      territorialAnalysisOptions: {
        grid: {
          top: "20", // 增加顶部留白空间
          left: "50", // 增加左侧留白空间
          right: "20", // 增加右侧留白空间
          bottom: "10%", // 增加底部留白空间
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          axisLabel: {
            margin: 10, // 调整标签与坐标轴的间距
            interval: 0, // 强制显示所有标签
          },
          data: [
            "海淀",
            "朝阳",
            "西城",
            "东城",
            "昌平",
            "丰台",
            "通州",
            "大兴",
            "顺义",
            "延庆",
            "平谷",
            "石景山",
            "房山",
            "门头沟",
            "怀柔",
            "经开",
            "密云",
          ],
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
        },
        series: [
          {
            data: [
              180, 170, 160, 150, 140, 130, 120, 110, 100, 90, 80, 70, 60, 50,
              40, 30, 20,
            ],
            type: "bar",
            itemStyle: {
              borderRadius: [20, 20, 0, 0], // 右侧圆角
              color: "#5470c6", // 可选颜色调整
            },
            barWidth: 30, // 调整柱条宽度
            label: {
              // 新增 label 配置
              show: true,
              position: "top",
              color: "#333",
            },
          },
        ],
      },
    };
  },
  mounted() {
    //舆情数量统计分析
    this.getSubjectAttitudeTrend()
    //舆情倾向性分析
    this.getWarningMedia();
    //舆情涉事分类分析
    this.getSubjectMediaTrend();
    //舆情重点媒体分布
    this.getWarningTagIndustry();
    //舆情涉事分类分析
    this.getSubjectTagIndustry();
    //舆情活跃媒体
    this.getSubjectSite();
    //舆情属地分析
    this.getSubjectPublishRegion();
  },
  methods: {
    //舆情数量统计分析
    getSubjectAttitudeTrend(){
      publicSentimentApi.querySubjectAttitudeTrend().then((res) => {
        const { data } = res;
        console.log('舆情数量统计分析',data);
      });
    },

    //舆情倾向性分析
    getWarningMedia() {
      publicSentimentApi.queryThematicSubjectAttitude().then((res) => {
        const { data } = res;
        if (data) {
          if (data.length === 0) {
            this.tendencyOptions.series[0].data = [];
          } else {
            this.tendencyOptions.series[0].data = [
              { value: data.positive, name: "正面" },
              { value: data.negative, name: "负面" },
              { value: data.neutral, name: "中性" },
            ];
          }
        }
      });
    },

    //舆情媒体类型分析
    getSubjectMediaTrend() {
      publicSentimentApi.querySubjectMediaTrend().then((res) => {
        const { data } = res;
        if (data) {
          if (data.length === 0) {
            this.mediaTypeOptions.xAxis.data = [];
            this.mediaTypeOptions.series[0].data = [];
          } else {
            // 合并所有日期的统计数据
            const mergedData = data.reduce((acc, cur) => {
              Object.keys(cur).forEach((key) => {
                if (key !== "date") {
                  acc[key] = (acc[key] || 0) + cur[key];
                }
              });
              return acc;
            }, {});

            // 按中文名称顺序定义x轴数据和对应字段
            const xAxisData = [
              "微博",
              "短视频",
              "微信",
              "贴吧",
              "网络视频",
              "电视视频",
              "网媒",
              "报刊",
              "论坛",
              "其他媒体",
            ];

            const seriesData = [
              mergedData.weiboCount, // 微博
              mergedData.shortVideoCount, // 短视频
              mergedData.wechatCount, // 微信
              mergedData.tiebaCount, // 贴吧
              mergedData.netVideoCount, // 网络视频
              mergedData.tvVideoCount, // 电视视频
              mergedData.newsCount, // 网媒
              mergedData.pressCount, // 报刊
              mergedData.forumCount, // 论坛
              mergedData.otherCount, // 其他媒体
            ];

            // 更新图表配置
            this.mediaTypeOptions.xAxis.data = xAxisData;
            this.mediaTypeOptions.series[0].data = seriesData;
          }
        }
      });
    },

    //舆情重点媒体分布
    getWarningTagIndustry() {
      publicSentimentApi.queryWarningMedia().then((res) => {
        const { data } = res;
        if (data) {
          if (data.length === 0) {
            this.mediaDistributionOptions.xAxis.data = [];
            this.mediaDistributionOptions.series[0].data = [];
          } else {
            this.mediaDistributionOptions.xAxis.data = data.map((item) => {
              return item.name;
            });
            this.mediaDistributionOptions.series[0].data = data.map((item) => {
              return item.value;
            });
          }
        }
      });
    },

    //舆情涉事分类分析
    getSubjectTagIndustry() {
      publicSentimentApi.querySubjectTagIndustry().then((res) => {
        const { data } = res;
        if (data) {
          if (data.data.length === 0) {
            this.narrationClassOptions.xAxis.data = [];
            this.narrationClassOptions.series[0].data = [];
          } else {
            this.narrationClassOptions.xAxis.data = data.data.map((item) => {
              return item.industryTag;
            });
            this.narrationClassOptions.series[0].data = data.data.map(
              (item) => {
                return item.count;
              }
            );
          }
        }
      });
    },

    //舆情活跃媒体
    getSubjectSite() {
      publicSentimentApi.querySubjectSite().then((res) => {
        const { data } = res;
        if (data) {
          if (data.length === 0) {
            this.activeMediaOptions.xAxis.data = [];
            this.activeMediaOptions.series[0].data = [];
          } else {
            console.log("舆情活跃媒体", data);
          }
        }
      });
    },

    //舆情属地分析
    getSubjectPublishRegion() {
      publicSentimentApi.querySubjectPublishRegion().then((res) => {
        const { data } = res;

        if (data) {
          if (data.publishRegion.length === 0) {
            this.territorialAnalysisOptions.xAxis.data = [];
            this.territorialAnalysisOptions.series[0].data = [];
          } else {
            let publishRegionList = data.publishRegion;
            if (publishRegionList && publishRegionList.length > 0) {
              const xAxisData = publishRegionList.map((item) => item.name);
              const seriesData = publishRegionList.map((item) => item.count);
              this.territorialAnalysisOptions.xAxis.data = xAxisData;
              this.territorialAnalysisOptions.series[0].data = seriesData;
            }
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.analysis-index {
  padding: 20px;

  ::v-deep {
    .el-row {
      margin-top: 20px;
    }
  }

  .focus,
  .sentiment-num {
    ::v-deep {
      .el-card__body {
        display: flex;
        align-items: center;

        .chart-title {
          font-size: 18px;
          font-weight: 700;
          color: rgb(51, 51, 51);
        }

        .el-statistic {
          flex: 1;
          display: flex;
          flex-direction: column-reverse;

          .head {
            margin-top: 15px;
            margin-bottom: 0;
          }

          .con {
            color: rgb(73, 181, 255);
            align-items: flex-end;

            .number {
              font-weight: 700;
              font-size: 36px;
            }

            .suffix {
              font-weight: 400;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .trend-statistics {
    ::v-deep {
      .el-card__header {
        .chart-title {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .title {
            font-size: 18px;
            font-weight: 700;
            color: rgb(51, 51, 51);
          }
        }
      }
      .el-card__body {
        .echarts {
          display: flex;

          .chart-container {
            flex: 1;
            height: 300px;
          }
        }
      }
    }
  }

  .echarts-card {
    ::v-deep {
      .el-card__header {
        .chart-title {
          height: 40px;
          line-height: 40px;
          font-size: 18px;
          font-weight: 700;
          color: rgb(51, 51, 51);
        }
      }

      .el-card__body {
        .echarts {
          .chart-container {
            height: 300px;
          }
        }
      }
    }
  }
}
</style>
