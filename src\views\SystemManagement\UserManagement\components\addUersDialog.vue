<template>
  <div class="add-uers-dialog-container">
    <portal-table
      ref="roleTableRef"
      style="width: 100%"
      :tableHeight="330"
      :showAddButton="false"
      :showSelection="true"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="id"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { systemManagementApi, orgApi } from "@/api";
export default {
  name: "AddUersDialog",
  props: {
    allocationData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      tableData: [],
      columns: [
        {
          text: true,
          prop: "roleName",
          label: "角色名称",
        },
        {
          text: true,
          prop: "userClass",
          label: "角色分级",
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      searchItems: [
        {
          prop: "roleName",
          label: "",
          type: "input",
          placeholder: "请输入角色名称",
          width: "700px",
        },
      ],
      form: {
        ids: [],
      },
      rules: {},
      searchKeyword: "",
    };
  },
  components: {
    PortalTable,
  },
  methods: {
    handleSearch(data) {
      this.pagination.currentPage = 1;
      this.searchKeyword = data.roleName || "";
      this.queryAddressBookList();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.queryAddressBookList();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.queryAddressBookList();
    },
    handleSelectionChange(val) {
      this.form.ids = val.map((item) => item.id);
      this.$emit("addIds", this.form.ids);
    },
    // 查询人员(用户)列表
    queryAddressBookList() {
      systemManagementApi
        .queryRolePage({
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          roleName: this.searchKeyword,
        })
        .then((res) => {
          if (res.data.items.length > 0) {
            res.data.items.forEach((item) => {
              this.allocationData.forEach((selectItem) => {
                if (item.id === selectItem.roleId) {
                  item.isSelected = true; // 设置选中状态
                  console.log(selectItem.roleId);
                }
              });
            });
            this.tableData = JSON.parse(JSON.stringify(res.data.items));
            this.tableData.forEach((row) => {
              if (row.isSelected) {
                this.$refs.roleTableRef.$refs.tableRef.toggleRowSelection(
                  row,
                  true
                );
              }
            });
          } else {
            this.tableData = [];
          }
          this.pagination.total = res.data.total;
        });
    },
  },
  mounted() {
    this.queryAddressBookList();
  },
};
</script>

<style lang="scss" scoped>
.add-uers-dialog-container {
  height: 100%;
}
::v-deep .portal-table {
  // width: 700px !important;
  // margin: 100px 30px 0 30px;
}
</style>