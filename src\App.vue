<template>
  <div id="app">
    <router-view />

<!--    <assistant-ai-dialog v-if="auth().getToken()" />-->
  </div>
</template>

<script>
import { systemManagementApi } from "@/api";
import { auth } from "@/utils";
import AssistantAiDialog from "@/components/AssistantAI/index.vue";
// import { getQueryObject } from './utils/index.js' // error log

export default {
  name: "App",
  components: { AssistantAiDialog },
  data() {
    return {};
  },
  created() {
    this.checkAutoLogin();
  },
  methods: {
    auth() {
      return auth
    },
    // 获取URL中的门户token参数
    getQueryParam(url) {
      // const url = 'http://*************:8086/#/login?tokenCode=HK6001mt7a4+5wGg4J8jRW4ilFopSqs1Wy8CajJYaKJt/+HSXW7IdgiOxG97fDSm3B+S11Ltmaoaa/yR6A4nbg==';
      url = url == null ? window.location.href : url;
      const paramStr = url.split("?")[1];
      if (!paramStr) return null;
      const params = paramStr.split("&");
      let paramObj = {};
      for (const param of params) {
        // 只分割第一个 "="，保留后面的所有内容
        const [k, ...vParts] = param.split("=");
        const v = vParts.join("="); // 重新组合后面的 "=" 号
        paramObj[k] = v;
        // if (k === key) return v;
      }
      return paramObj;
    },

    async checkAutoLogin() {
      // const testCode = "http://localhost:8080/#/home?tokenCode=1uoAv93AgoC6wPWbNtVUU0g5sZPBrzu1etom1j0WqNhotlnfv0XKxcbD3qCzTU7o/Dp7pyE4vUIZ98asxMNP2g****";
      const paramObj = this.getQueryParam();
      console.log("loginToken:", paramObj?.tokenCode);
      const tokenCode = paramObj?.tokenCode;
      if (!tokenCode) {
        return;
      }
      try {
        // 调用登录接口，传递 key 作为参数
        const params = {
          tokenCode: tokenCode,
        };
        const res = await systemManagementApi.doCodeLogin(params);
        const { code, data, error } = res;
        if (code === 0) {
          console.log("登录成功，跳转到主页面", data);

          auth.setToken(data.token, false);
          auth.setRefreshToken(data.refreshToken, false);
          if (data.tokenType) {
            auth.setTokenType(data.tokenType, false);
          }
          this.$store.commit("user/SET_TOKEN", data.token);

          // let token = data.tokenType + " " + data.token;
          // localStorage.setItem("token", token);

          // this.$router.push('/home');

          this.$message.success("登录成功");

          // 跳转到首页或之前访问的页面
          const redirect = this.$route.query.redirect || "/";
          // 避免重复导航，只在目标路径与当前路径不同时才跳转
          if (this.$route.path !== redirect) {
            this.$router.push(redirect);
          }
        } else {
          console.log("登录失败，跳转到登录页面");
          this.$router.push("/login"); // 跳转到登录页面
        }
      } catch (error) {
        console.error("登录请求失败:", error);
        this.$router.push("/login"); // 跳转到登录页面
      }
    },
  },
};
</script>

<style>
#app {
  height: 100%;
  overflow: auto;
}
</style>
