<template>
  <div class="edite-dialog-container" v-if="editeDialogVisible">
    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="add-form"
        label-position="top"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色分级" prop="userClass">
          <el-select v-model="form.userClass" placeholder="请输入角色分级">
            <el-option
              v-for="item in userClassList"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色状态" prop="status">
          <el-select v-model="form.status" placeholder="请输入状态">
            <el-option label="正常" value="1" />
            <el-option label="注销" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色描述" prop="roleDesc">
          <el-input
            v-model="form.roleDesc"
            :rows="6"
            placeholder="请输入角色描述"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </general-dialog>
  </div>
</template>

<script>
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi } from "@/api";
export default {
  data() {
    return {
      editeDialogVisible: false,
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "编辑角色",
      form: {
        id: "",
        roleName: "",
        userClass: "",
        roleDesc: "",
        status: 0,
      },
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        userClass: [
          { required: true, message: "请输入角色分级", trigger: "blur" },
        ],
        status: [
          { required: true, message: "请输入角色状态", trigger: "blur" },
        ],
      },
      userClassList: [
        {
          label: "123-1",
          value: "123-1",
        },
        {
          label: "123-2",
          value: "123-2",
        },
        {
          label: "123-3",
          value: "123-3",
        },
      ],
    };
  },
  components: {
    GeneralDialog,
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          console.log(this.form);
          this.dialogVisible = false;
          systemManagementApi.updateRole(this.form).then((res) => {
            this.$message.success("编辑成功");
            this.$emit("update");
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.edite-dialog-container {
  height: 100%;
}
</style>