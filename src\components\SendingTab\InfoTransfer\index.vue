<template>
  <!-- 信息转办 -->
  <div class="info-transfer">
    <el-form
      ref="addForm"
      :model="form"
      :rules="rules"
      class="add-form"
      label-position="top"
      label-width="100px"
    >
      <el-form-item label="转发单位" prop="matter">
        <el-input
          v-model="form.matter"
          placeholder="请输入转发单位"
          size="small"
          clearable
        />
      </el-form-item>

      <el-form-item label="事件标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入事件标题"
          size="small"
          clearable
        />
      </el-form-item>

      <el-form-item label="调度指令" prop="command">
        <el-input
          v-model="form.command"
          type="textarea"
          :rows="6"
          placeholder="请输入调度指令"
          size="small"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "InfoTransfer",
  props: {
    componentProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        // 转发单位
        matter: "",
        // 事件标题
        title: "",
        // 调度指令
        command: "",
      },
      rules: {
        matter: [
          { required: true, message: "请输入转发单位", trigger: "blur" },
        ],
        title: [{ required: true, message: "请输入事件标题", trigger: "blur" }],
        command: [
          { required: true, message: "请输入调度指令", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    componentProps: {
      handler(newVal, oldVal) {
        this.form.id = newVal?.id;
        this.form.title = newVal.infoTitle;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$message({
            message: "信息转办成功",
            type: "success",
          });
        }
      });
    },
  },
  beforeDestroy() {
    this.form = {
      matter: "",
      title: "",
      command: "",
    };
  },
};
</script>

<style scoped lang="scss"></style>
